# gen_cotodigital_urls.py
# Usage: python gen_cotodigital_urls.py
# Produces: cotodigital_urls.txt (1,207 lines)

from pathlib import Path

base_url = "https://www.cotodigital.com.ar/sitios/cdigi/categoria"
params = (
    "?Nf=product.endDate%7CGTEQ+1.755648E12%7C%7Cproduct.startDate%7CLTEQ+1.755648E12"
    "&Nr=AND%28product.sDisp_200%3A1004%2Cproduct.language%3Aespa%C3%B1ol%2COR%28product.siteId%3ACotoDigital%29%29"
    "&Nrpp=24&format=json&No={offset}"
)

total_pages = 1207      # offsets: 0 .. 28944 (step 24)
offset_step = 24

out = Path("cotodigital_urls.txt")
with out.open("w", encoding="utf-8") as f:
    for page in range(total_pages):
        offset = page * offset_step
        f.write(base_url + params.format(offset=offset) + "\n")

print(f"Wrote {total_pages} URLs -> {out.resolve()}")