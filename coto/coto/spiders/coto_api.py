
import json
from ast import literal_eval
from pathlib import Path
import scrapy


class Coto<PERSON>ISpider(scrapy.Spider):
    name = "coto_api"
    allowed_domains = ["cotodigital.com.ar", "www.cotodigital.com.ar"]

    custom_settings = {
        "USER_AGENT": "Mozilla/5.0 (compatible; coto-scraper/1.0)",
        "DEFAULT_REQUEST_HEADERS": {
            "Accept": "application/json,text/plain,*/*",
            "Referer": "https://www.cotodigital.com.ar/",
        },
        # You can also export via CLI (-O/-o). If you prefer built-in feeds, uncomment:
        # "FEEDS": {"coto_products.jsonl": {"format": "jsonlines", "encoding": "utf-8"}},
        "DOWNLOAD_TIMEOUT": 30,
        "RETRY_ENABLED": True,
        "RETRY_TIMES": 2,
        # tune if needed:
        # "CONCURRENT_REQUESTS": 16,
    }

    # ---------- helpers ----------
    @staticmethod
    def _first(v):
        if isinstance(v, list):
            return v[0] if v else None
        return v

    @classmethod
    def _get_any(cls, d, keys):
        for k in keys:
            if isinstance(d, dict) and k in d:
                return cls._first(d[k])
        return None

    @staticmethod
    def _to_number(x):
        try:
            return float(x)
        except (TypeError, ValueError):
            return None

    @staticmethod
    def _parse_dto_price(s):
        """dtoPrice often comes as a JSON string, sometimes python-literal-ish."""
        if not s:
            return None
        try:
            return json.loads(s)
        except Exception:
            try:
                return literal_eval(s)
            except Exception:
                return None

    def _extract_product_items(self, data):
        """
        Handle the common Endeca/ATG shapes:
        - blocks with "records": each record has "attributes" (dict of name->[values])
        - sometimes a flat list of items; we walk recursively and collect all attribute dicts
        """
        products = []

        def walk(node):
            if isinstance(node, dict):
                # direct records array?
                recs = node.get("records")
                if isinstance(recs, list):
                    for rec in recs:
                        attrs = rec.get("attributes") or rec
                        if isinstance(attrs, dict):
                            products.append(attrs)
                # keep walking
                for v in node.values():
                    walk(v)
            elif isinstance(node, list):
                for v in node:
                    walk(v)

        walk(data)
        return products

    # ---------- scrapy entry ----------
    def start_requests(self):
        # look in project root (where scrapy.cfg is)
        url_list = Path("cotodigital_urls.txt")
        if not url_list.exists():
            raise FileNotFoundError(
                "cotodigital_urls.txt not found. Generate it with: python gen_cotodigital_urls.py"
            )

        self.logger.info(f"Loading URLs from {url_list.resolve()}")
        with url_list.open("r", encoding="utf-8") as f:
            for line in f:
                url = line.strip()
                if url:
                    yield scrapy.Request(url, callback=self.parse)

    def parse(self, response):
        try:
            data = response.json()
        except Exception:
            self.logger.warning("Non-JSON or malformed response at %s", response.url)
            return

        # collect every product-ish attribute dict from the payload
        for attrs in self._extract_product_items(data):
            # ids & name
            product_id = self._get_any(attrs, [
                "sku.repositoryId", "sku.id", "product.repositoryId", "product.id", "record.id", "id"
            ])
            name = self._get_any(attrs, [
                "sku.displayName", "product.displayName", "product.name", "displayName", "name", "title"
            ])

            # media & urls
            image_url = self._get_any(attrs, [
                "product.largeImage.url", "product.mediumImage.url",
                "product.image", "sku.image", "product.thumbnail", "sku.thumbnail"
            ])
            product_url = self._get_any(attrs, ["sku.url", "product.url", "url"])

            # pricing
            dto_raw = self._get_any(attrs, ["sku.dtoPrice", "product.dtoPrice"])
            dto = self._parse_dto_price(dto_raw)

            price_list = price_discount = price_without_tax = None
            if isinstance(dto, dict):
                price_list = dto.get("precioLista")
                price_discount = dto.get("precio")
                price_without_tax = dto.get("precioSinImp")

            # fallbacks if dtoPrice missing
            if price_list is None:
                price_list = self._get_any(attrs, ["sku.activePrice", "product.listPrice", "sku.listPrice"])
            if price_discount is None:
                price_discount = price_list

            # skip thin records with no price & no image (keeps your output clean)
            has_price_signal = bool(dto_raw or self._get_any(attrs, ["sku.activePrice"]))
            has_image_signal = bool(image_url)
            if not (has_price_signal or has_image_signal):
                continue

            yield {
                "source_url": response.url,
                "product_id": product_id,
                "name": name,
                "product_url": product_url,
                "image_url": image_url,
                "price_list": self._to_number(price_list),           # sin descuento
                "price_discount": self._to_number(price_discount),   # con descuento
                "price_without_tax": self._to_number(price_without_tax),
                # keep raw for mapping/debug (optional – remove if you want smaller output)
                # "raw_attributes": attrs,
            }