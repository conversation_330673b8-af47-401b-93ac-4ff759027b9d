#!/usr/bin/env python3
import argparse
import re
from collections import OrderedDict
from pathlib import Path
from typing import Iterable

URL_RE = re.compile(
    r"^(https?://www\.carrefour\.com\.ar)/([^?]+)\?page=(\d+)$", re.IGNORECASE
)

DEFAULT_URLS = """
https://www.carrefour.com.ar/Electro-y-tecnologia?page=1
https://www.carrefour.com.ar/Electro-y-tecnologia?page=50
https://www.carrefour.com.ar/Bazar-y-textil?page=1
https://www.carrefour.com.ar/Bazar-y-textil?page=50
https://www.carrefour.com.ar/Almacen?page=1
https://www.carrefour.com.ar/Almacen?page=50
https://www.carrefour.com.ar/Desayuno-y-merienda?page=1
https://www.carrefour.com.ar/Desayuno-y-merienda?page=50
https://www.carrefour.com.ar/Bebidas?page=1
https://www.carrefour.com.ar/Bebidas?page=50
https://www.carrefour.com.ar/Lacteos-y-productos-frescos?page=1
https://www.carrefour.com.ar/Lacteos-y-productos-frescos?page=50
https://www.carrefour.com.ar/Carnes-y-Pescados?page=1
https://www.carrefour.com.ar/Carnes-y-Pescados?page=14
https://www.carrefour.com.ar/Frutas-y-Verduras?page=1
https://www.carrefour.com.ar/Frutas-y-Verduras?page=12
https://www.carrefour.com.ar/Panaderia?page=1
https://www.carrefour.com.ar/Panaderia?page=22
https://www.carrefour.com.ar/Congelados?page=1
https://www.carrefour.com.ar/Congelados?page=28
https://www.carrefour.com.ar/Limpieza?page=1
https://www.carrefour.com.ar/Limpieza?page=50
https://www.carrefour.com.ar/Perfumeria?page=1
https://www.carrefour.com.ar/Perfumeria?page=50
https://www.carrefour.com.ar/Mundo-bebe?page=1
https://www.carrefour.com.ar/Mundo-bebe?page=23
https://www.carrefour.com.ar/Mascotas?page=1
https://www.carrefour.com.ar/Mascotas?page=23
https://www.carrefour.com.ar/Indumentaria?page=1
https://www.carrefour.com.ar/Indumentaria?page=50
""".strip()


def parse_urls(lines: Iterable[str]):
    """
    Return an OrderedDict mapping (base, category) -> (min_page, max_page)
    Keeps first-seen category order.
    """
    ranges = OrderedDict()
    for raw in lines:
        line = raw.strip()
        if not line:
            continue
        m = URL_RE.match(line)
        if not m:
            # Ignore lines that don't match the expected pattern
            continue
        base, category, page_str = m.group(1), m.group(2), m.group(3)
        page = int(page_str)
        key = (base, category)
        if key not in ranges:
            ranges[key] = [page, page]
        else:
            ranges[key][0] = min(ranges[key][0], page)
            ranges[key][1] = max(ranges[key][1], page)
    return ranges


def expand_ranges(ranges: OrderedDict):
    """Yield all URLs for every (base, category) from min_page to max_page inclusive."""
    for (base, category), (lo, hi) in ranges.items():
        for p in range(lo, hi + 1):
            yield f"{base}/{category}?page={p}"


def main():
    ap = argparse.ArgumentParser(
        description="Expand Carrefour category URLs from first/last into full ranges."
    )
    ap.add_argument(
        "--input",
        "-i",
        type=Path,
        help="Optional input file with URLs (one per line). If omitted, uses the embedded list.",
    )
    ap.add_argument(
        "--output",
        "-o",
        type=Path,
        default=Path("carrefour_full_urls.txt"),
        help="Output .txt file (default: carrefour_full_urls.txt)",
    )
    args = ap.parse_args()

    if args.input and args.input.exists():
        lines = args.input.read_text(encoding="utf-8").splitlines()
    else:
        lines = DEFAULT_URLS.splitlines()

    ranges = parse_urls(lines)
    if not ranges:
        raise SystemExit("No valid Carrefour URLs found. Check input format.")

    all_urls = list(expand_ranges(ranges))
    args.output.write_text("\n".join(all_urls) + "\n", encoding="utf-8")

    print(f"Wrote {len(all_urls)} URLs to {args.output.resolve()}")
    print("Categories and ranges:")
    for (base, category), (lo, hi) in ranges.items():
        print(f"- {category}: {lo} → {hi}")


if __name__ == "__main__":
    main()