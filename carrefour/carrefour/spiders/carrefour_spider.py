# super-scraper/carrefour/carrefour/spiders/carrefour_next.py
import json
from pathlib import Path
from urllib.parse import urlencode, urlparse

import scrapy
from tqdm import tqdm

VERSION = "carrefour_next v1.1 (fix iterable + debug path)"


class CarrefourNextSpider(scrapy.Spider):
    name = "carrefour_next"
    allowed_domains = ["carrefour.com.ar", "www.carrefour.com.ar"]

    custom_settings = {
        "USER_AGENT": "Mozilla/5.0 (compatible; carrefour-scraper/1.0)",
        "DEFAULT_REQUEST_HEADERS": {
            "Accept": "application/json,text/html;q=0.9,*/*;q=0.8",
            "Accept-Language": "es-AR,es;q=0.9,en;q=0.8",
            "Referer": "https://www.carrefour.com.ar/",
        },
        # export wherever you want; override with -O/-o if needed
        "FEEDS": {"carrefour_products.jsonl": {"format": "jsonlines", "encoding": "utf-8"}},
        "DOWNLOAD_TIMEOUT": 30,
        "RETRY_TIMES": 2,
        # "ROBOTSTXT_OBEY": False,  # uncomment if robots blocks and you have permission
    }

    # VTEX paging config
    CHUNK = 24  # VTEX uses inclusive _from/_to; a full page returns 24

    def __init__(self, input_file=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.input_file = input_file
        self.category_map = {}  # '/Indumentaria' -> category_id
        self.urls = []
        self.progress = None
        self.seen_ids = set()
        self._progress_updated_for = set()  # mark each input URL once

    # ---------- Scrapy 2.13+ entry point ----------
    async def start(self):
        # 1) Load URL list (one per line)
        candidates = []
        if self.input_file:
            candidates.append(Path(self.input_file))
        candidates += [
            Path("carrefour_full_urls.txt"),
            Path("carrefour_all_pages.txt"),
            Path("utils") / "carrefour_full_urls.txt",
            Path("utils") / "carrefour_all_pages.txt",
            Path(__file__).resolve().parents[2] / "carrefour_full_urls.txt",
        ]
        for c in candidates:
            if c.exists():
                self.urls = [ln.strip() for ln in c.read_text(encoding="utf-8").splitlines() if ln.strip().startswith("http")]
                break
        if not self.urls:
            raise FileNotFoundError("Could not find carrefour_full_urls.txt (try -a input_file=PATH)")

        # 2) Create progress bar based on KNOWN input URLs (your page list)
        self.progress = tqdm(total=len(self.urls), desc="Scraping Carrefour", unit="page")
        self.logger.info("Spider module path: %s | %s", __file__, VERSION)

        # 3) Build category tree (maps path -> id), then kick off VTEX search per URL
        tree_api = "https://www.carrefour.com.ar/api/catalog_system/pub/category/tree/50"
        yield scrapy.Request(tree_api, headers={"Accept": "application/json"}, callback=self.parse_category_tree, priority=100)

    def close(self, reason):
        if self.progress:
            self.progress.close()

    # ---------- helpers ----------
    @staticmethod
    def _norm_path_from_url(u: str) -> str:
        path = urlparse(u).path or "/"
        if not path.startswith("/"):
            path = "/" + path
        return path.rstrip("/").lower()

    # ---------- parsing ----------
    def parse_category_tree(self, response):
        # Build /{segment} -> category_id map
        try:
            data = json.loads(response.text)
        except Exception:
            data = None
        if not data:
            self.logger.error("Category tree JSON missing/unreadable")
            return

        def walk(nodes):
            for n in nodes or []:
                url_path = n.get("url") or n.get("href") or ""
                if url_path:
                    p = url_path.strip()
                    if not p.startswith("/"):
                        p = "/" + p
                    self.category_map[p.rstrip("/").lower()] = int(n.get("id"))
                for key in ("children", "childrenCategories", "childrenCategoriesTree"):
                    if isinstance(n.get(key), list):
                        walk(n[key])

        if isinstance(data, list):
            walk(data)
        elif isinstance(data, dict):
            walk(data.get("children"))

        self.logger.info("Loaded %d categories from VTEX tree", len(self.category_map))

        # Schedule VTEX search for every input URL (progress bar = number of input URLs)
        for u in self.urls:
            norm = self._norm_path_from_url(u)
            cid = self.category_map.get(norm)
            if not cid:
                # try matching last segment
                norm_tail = norm.split("/")[-1]
                for k, v in self.category_map.items():
                    if k.split("/")[-1] == norm_tail:
                        cid = v
                        break
            if not cid:
                self.logger.warning("No category_id found for %s", norm)
                # still mark progress for that URL (we "processed" it)
                if u not in self._progress_updated_for:
                    self.progress.update(1)
                    self._progress_updated_for.add(u)
                continue

            # First VTEX page for this category
            params = {"fq": f"C:{cid}", "_from": 0, "_to": self.CHUNK - 1}
            api_url = "https://www.carrefour.com.ar/api/catalog_system/pub/products/search?" + urlencode(params)
            yield scrapy.Request(
                api_url,
                headers={"Accept": "application/json"},
                callback=self.parse_vtex_page,
                cb_kwargs={"cid": cid, "from_idx": 0, "source_url": u},
                priority=50,
            )

    def parse_vtex_page(self, response, cid: int, from_idx: int, source_url: str):
        # Mark one unit of progress the FIRST time we touch this input URL
        if source_url not in self._progress_updated_for:
            self.progress.update(1)
            self._progress_updated_for.add(source_url)

        # Parse JSON safely
        try:
            products = json.loads(response.text)
        except Exception:
            self.logger.warning("Non-JSON at %s", response.url)
            products = []

        # VTEX returns a list; if not, bail gracefully
        if not isinstance(products, list):
            self.logger.info("Unexpected VTEX payload (not a list) at %s", response.url)
            products = []

        window_size = len(products)  # <-- use this for pagination decisions
        iterable = products if isinstance(products, list) else []
        emitted = 0  # Initialize counter for products emitted
        for p in iterable:
            product_id = (
                p.get("productId")
                or p.get("productReference")
                or p.get("id")
                or p.get("product_id")
            )

            # Global de-duplication by product_id
            if product_id and product_id in self.seen_ids:
                continue

            name = (
                p.get("productName")
                or p.get("productNameComplete")
                or p.get("productTitle")
                or p.get("product")
            )

            link_text = p.get("linkText") or p.get("slug")
            product_url = f"https://www.carrefour.com.ar/{link_text}/p" if link_text else p.get("url")

            # Image
            image_url = None
            try:
                items = p.get("items") or []
                images = items[0].get("images") or [] if items else []
                if images:
                    image_url = images[0].get("imageUrl")
            except Exception:
                pass

            # Prices
            price_list = price_discount = None
            try:
                items = p.get("items") or []
                sellers = items[0].get("sellers") or [] if items else []
                if sellers:
                    offer = (sellers[0] or {}).get("commertialOffer") or {}
                    price_list = offer.get("ListPrice")
                    price_discount = offer.get("Price")
            except Exception:
                pass

            if product_id:
                self.seen_ids.add(product_id)

            yield {
                "source_category": self._norm_path_from_url(source_url),
                "data_url": response.url,
                "product_id": product_id,
                "name": name,
                "product_url": product_url,
                "image_url": image_url,
                "price_list": price_list,
                "price_discount": price_discount,
            }
            emitted += 1

        # Paginate: rely on VTEX window size (24 means “there may be more”)
        if window_size >= self.CHUNK:
            next_from = from_idx + self.CHUNK
            next_to = next_from + self.CHUNK - 1
            params = {"fq": f"C:{cid}", "_from": next_from, "_to": next_to}
            api_url = "https://www.carrefour.com.ar/api/catalog_system/pub/products/search?" + urlencode(params)
            yield scrapy.Request(
                api_url,
                headers={"Accept": "application/json"},
                callback=self.parse_vtex_page,
                cb_kwargs={"cid": cid, "from_idx": next_from, "source_url": source_url},
                priority=40,
            )
        else:
            self.logger.info(
                "[%s] Finished category: last window=%d, total unique so far=%d",
                source_url, window_size, len(self.seen_ids)
            )