'use client'

import { createContext, useContext, useEffect, useState, ReactNode } from 'react'
import { io, Socket } from 'socket.io-client'
import { useScraperStore } from './store'
import { toast } from '@/components/ui/use-toast'

interface SocketContextType {
  socket: Socket | null
  isConnected: boolean
  joinScraperRoom: (supermarket: string) => void
  leaveScraperRoom: (supermarket: string) => void
}

const SocketContext = createContext<SocketContextType>({
  socket: null,
  isConnected: false,
  joinScraperRoom: () => {},
  leaveScraperRoom: () => {}
})

export const useSocket = () => useContext(SocketContext)

interface SocketProviderProps {
  children: ReactNode
  url: string
}

export function SocketProvider({ children, url }: SocketProviderProps) {
  const [socket, setSocket] = useState<Socket | null>(null)
  const [isConnected, setIsConnected] = useState(false)
  const { updateRun, addRun } = useScraperStore()

  useEffect(() => {
    // Initialize socket connection
    const socketInstance = io(url, {
      transports: ['websocket', 'polling'],
      timeout: 20000,
      forceNew: true
    })

    // Connection event handlers
    socketInstance.on('connect', () => {
      console.log('Socket connected:', socketInstance.id)
      setIsConnected(true)
      toast({
        title: 'Conectado',
        description: 'Conexión en tiempo real establecida',
        duration: 3000
      })
    })

    socketInstance.on('disconnect', (reason) => {
      console.log('Socket disconnected:', reason)
      setIsConnected(false)
      
      if (reason === 'io server disconnect') {
        // Server disconnected, try to reconnect
        socketInstance.connect()
      }
    })

    socketInstance.on('connect_error', (error) => {
      console.error('Socket connection error:', error)
      setIsConnected(false)
      toast({
        title: 'Error de conexión',
        description: 'No se pudo establecer conexión en tiempo real',
        variant: 'destructive',
        duration: 5000
      })
    })

    // Scraper event handlers
    socketInstance.on('scraper-progress', (data: {
      supermarket: string
      progress: number
      runId: string
    }) => {
      console.log('Scraper progress:', data)
      updateRun(data.runId, { progress: data.progress })
      
      toast({
        title: `${data.supermarket} - Progreso`,
        description: `${data.progress}% completado`,
        duration: 2000
      })
    })

    socketInstance.on('scraper-completed', (data: {
      supermarket: string
      status: 'completed' | 'failed'
      productsScraped?: number
      errorMessage?: string
    }) => {
      console.log('Scraper completed:', data)
      
      if (data.status === 'completed') {
        toast({
          title: `${data.supermarket} - Completado`,
          description: `Se procesaron ${data.productsScraped || 0} productos`,
          duration: 5000
        })
      } else {
        toast({
          title: `${data.supermarket} - Error`,
          description: data.errorMessage || 'El scraper falló',
          variant: 'destructive',
          duration: 5000
        })
      }
    })

    socketInstance.on('scraper-started', (data: {
      supermarket: string
      runId: string
      totalUrls: number
    }) => {
      console.log('Scraper started:', data)
      toast({
        title: `${data.supermarket} - Iniciado`,
        description: `Procesando ${data.totalUrls} páginas`,
        duration: 3000
      })
    })

    // Error handlers
    socketInstance.on('error', (error) => {
      console.error('Socket error:', error)
      toast({
        title: 'Error',
        description: 'Error en la conexión en tiempo real',
        variant: 'destructive',
        duration: 3000
      })
    })

    setSocket(socketInstance)

    // Cleanup on unmount
    return () => {
      console.log('Cleaning up socket connection')
      socketInstance.disconnect()
    }
  }, [url, updateRun, addRun])

  const joinScraperRoom = (supermarket: string) => {
    if (socket && isConnected) {
      console.log(`Joining scraper room: ${supermarket}`)
      socket.emit('join-scraper-room', supermarket)
    }
  }

  const leaveScraperRoom = (supermarket: string) => {
    if (socket && isConnected) {
      console.log(`Leaving scraper room: ${supermarket}`)
      socket.emit('leave-scraper-room', supermarket)
    }
  }

  const value = {
    socket,
    isConnected,
    joinScraperRoom,
    leaveScraperRoom
  }

  return (
    <SocketContext.Provider value={value}>
      {children}
    </SocketContext.Provider>
  )
}
