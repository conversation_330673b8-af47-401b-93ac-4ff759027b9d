'use client'

import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000'

// Create Supabase client for auth
const supabase = createClientComponentClient()

// API client class
class ApiClient {
  private baseUrl: string

  constructor(baseUrl: string) {
    this.baseUrl = baseUrl
  }

  private async getAuthHeaders(): Promise<Record<string, string>> {
    const { data: { session } } = await supabase.auth.getSession()
    
    const headers: Record<string, string> = {
      'Content-Type': 'application/json'
    }

    if (session?.access_token) {
      headers.Authorization = `Bearer ${session.access_token}`
    }

    return headers
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseUrl}/api${endpoint}`
    const headers = await this.getAuthHeaders()

    const config: RequestInit = {
      headers: {
        ...headers,
        ...options.headers
      },
      ...options
    }

    try {
      const response = await fetch(url, config)
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`)
      }

      return await response.json()
    } catch (error) {
      console.error(`API request failed: ${endpoint}`, error)
      throw error
    }
  }

  // Products API
  async getProducts(params: {
    q?: string
    category?: string
    supermarket?: string
    page?: number
    limit?: number
    sort?: string
    order?: string
  } = {}) {
    const searchParams = new URLSearchParams()
    
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        searchParams.append(key, value.toString())
      }
    })

    return this.request(`/products?${searchParams.toString()}`)
  }

  async getProduct(id: string) {
    return this.request(`/products/${id}`)
  }

  async compareProduct(id: string) {
    return this.request(`/products/${id}/compare`)
  }

  async getCategories() {
    return this.request('/products/categories')
  }

  async getSupermarkets() {
    return this.request('/products/supermarkets')
  }

  // Cart API
  async getCart() {
    return this.request('/cart')
  }

  async addToCart(productId: string, quantity: number = 1) {
    return this.request('/cart', {
      method: 'POST',
      body: JSON.stringify({ productId, quantity })
    })
  }

  async updateCartItem(itemId: string, quantity: number) {
    return this.request(`/cart/${itemId}`, {
      method: 'PUT',
      body: JSON.stringify({ quantity })
    })
  }

  async removeFromCart(itemId: string) {
    return this.request(`/cart/${itemId}`, {
      method: 'DELETE'
    })
  }

  async clearCart() {
    return this.request('/cart', {
      method: 'DELETE'
    })
  }

  // Scrapers API
  async getScraperStatus() {
    return this.request('/scrapers/status')
  }

  async startScraper(supermarket: 'carrefour' | 'coto') {
    return this.request('/scrapers/start', {
      method: 'POST',
      body: JSON.stringify({ supermarket })
    })
  }

  async stopScraper(supermarket: 'carrefour' | 'coto') {
    return this.request('/scrapers/stop', {
      method: 'POST',
      body: JSON.stringify({ supermarket })
    })
  }

  // Admin API
  async getAdminStats() {
    return this.request('/admin/stats')
  }

  async getSystemHealth() {
    return this.request('/admin/health')
  }

  async deleteAllData() {
    return this.request('/admin/data/all', {
      method: 'DELETE',
      body: JSON.stringify({ confirm: true })
    })
  }

  async deleteProductData() {
    return this.request('/admin/data/products', {
      method: 'DELETE',
      body: JSON.stringify({ confirm: true })
    })
  }

  async deletePriceHistory() {
    return this.request('/admin/data/prices', {
      method: 'DELETE',
      body: JSON.stringify({ confirm: true })
    })
  }
}

// Create and export API client instance
export const api = new ApiClient(API_BASE_URL)

// React hooks for API calls
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'

// Products hooks
export function useProducts(params: Parameters<typeof api.getProducts>[0] = {}) {
  return useQuery({
    queryKey: ['products', params],
    queryFn: () => api.getProducts(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

export function useProduct(id: string) {
  return useQuery({
    queryKey: ['product', id],
    queryFn: () => api.getProduct(id),
    enabled: !!id,
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}

export function useCategories() {
  return useQuery({
    queryKey: ['categories'],
    queryFn: () => api.getCategories(),
    staleTime: 30 * 60 * 1000, // 30 minutes
  })
}

export function useSupermarkets() {
  return useQuery({
    queryKey: ['supermarkets'],
    queryFn: () => api.getSupermarkets(),
    staleTime: 60 * 60 * 1000, // 1 hour
  })
}

// Cart hooks
export function useCart() {
  return useQuery({
    queryKey: ['cart'],
    queryFn: () => api.getCart(),
    staleTime: 0, // Always fresh
  })
}

export function useAddToCart() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: ({ productId, quantity }: { productId: string; quantity?: number }) =>
      api.addToCart(productId, quantity),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['cart'] })
    }
  })
}

// Scraper hooks
export function useScraperStatus() {
  return useQuery({
    queryKey: ['scraper-status'],
    queryFn: () => api.getScraperStatus(),
    refetchInterval: 5000, // Refetch every 5 seconds
    staleTime: 0
  })
}

export function useStartScraper() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (supermarket: 'carrefour' | 'coto') => api.startScraper(supermarket),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['scraper-status'] })
    }
  })
}

// Admin hooks
export function useAdminStats() {
  return useQuery({
    queryKey: ['admin-stats'],
    queryFn: () => api.getAdminStats(),
    staleTime: 60 * 1000, // 1 minute
  })
}
