'use client'

import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'

// Types
export interface Product {
  id: string
  external_id?: string
  supermarket_id: string
  name: string
  normalized_name?: string
  category?: string
  image_url?: string
  product_url?: string
  created_at: string
  updated_at: string
  supermarkets?: {
    name: string
    slug: string
    logo_url?: string
  }
  price_list?: number
  price_discount?: number
  scraped_at?: string
}

export interface CartItem {
  id: string
  product: Product
  quantity: number
  added_at: string
  currentPrice?: {
    price_list?: number
    price_discount?: number
    scraped_at: string
  }
  comparableProducts?: Product[]
  totalPrice: number
}

export interface ScraperRun {
  id: string
  supermarket_id: string
  status: 'pending' | 'running' | 'completed' | 'failed'
  progress: number
  total_urls: number
  products_scraped: number
  started_at: string
  completed_at?: string
  error_message?: string
  supermarkets: {
    name: string
    slug: string
  }
  isActive?: boolean
}

// Cart Store
interface CartStore {
  items: CartItem[]
  isLoading: boolean
  error: string | null
  
  // Actions
  addItem: (product: Product, quantity?: number) => void
  removeItem: (itemId: string) => void
  updateQuantity: (itemId: string, quantity: number) => void
  clearCart: () => void
  setItems: (items: CartItem[]) => void
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
  
  // Computed
  getTotalItems: () => number
  getTotalPrice: () => number
  getTotalsBySuper: () => Record<string, { name: string; total: number; itemCount: number }>
}

export const useCartStore = create<CartStore>()(
  persist(
    (set, get) => ({
      items: [],
      isLoading: false,
      error: null,

      addItem: (product, quantity = 1) => {
        const items = get().items
        const existingItem = items.find(item => item.product.id === product.id)
        
        if (existingItem) {
          set({
            items: items.map(item =>
              item.product.id === product.id
                ? { ...item, quantity: item.quantity + quantity }
                : item
            )
          })
        } else {
          const newItem: CartItem = {
            id: `cart-${Date.now()}-${Math.random()}`,
            product,
            quantity,
            added_at: new Date().toISOString(),
            totalPrice: (product.price_discount || product.price_list || 0) * quantity
          }
          set({ items: [...items, newItem] })
        }
      },

      removeItem: (itemId) => {
        set({
          items: get().items.filter(item => item.id !== itemId)
        })
      },

      updateQuantity: (itemId, quantity) => {
        if (quantity <= 0) {
          get().removeItem(itemId)
          return
        }
        
        set({
          items: get().items.map(item =>
            item.id === itemId
              ? { 
                  ...item, 
                  quantity,
                  totalPrice: (item.currentPrice?.price_discount || item.product.price_discount || item.product.price_list || 0) * quantity
                }
              : item
          )
        })
      },

      clearCart: () => {
        set({ items: [] })
      },

      setItems: (items) => {
        set({ items })
      },

      setLoading: (loading) => {
        set({ isLoading: loading })
      },

      setError: (error) => {
        set({ error })
      },

      getTotalItems: () => {
        return get().items.reduce((total, item) => total + item.quantity, 0)
      },

      getTotalPrice: () => {
        return get().items.reduce((total, item) => total + item.totalPrice, 0)
      },

      getTotalsBySuper: () => {
        const items = get().items
        return items.reduce((acc, item) => {
          const supermarket = item.product.supermarkets?.slug || 'unknown'
          const name = item.product.supermarkets?.name || 'Unknown'
          
          if (!acc[supermarket]) {
            acc[supermarket] = { name, total: 0, itemCount: 0 }
          }
          
          acc[supermarket].total += item.totalPrice
          acc[supermarket].itemCount += item.quantity
          
          return acc
        }, {} as Record<string, { name: string; total: number; itemCount: number }>)
      }
    }),
    {
      name: 'cart-storage',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({ items: state.items })
    }
  )
)

// Scraper Store
interface ScraperStore {
  runs: ScraperRun[]
  isLoading: boolean
  error: string | null
  
  // Actions
  setRuns: (runs: ScraperRun[]) => void
  updateRun: (runId: string, updates: Partial<ScraperRun>) => void
  addRun: (run: ScraperRun) => void
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
  
  // Computed
  getActiveRuns: () => ScraperRun[]
  getRunBySuper: (supermarket: string) => ScraperRun | undefined
}

export const useScraperStore = create<ScraperStore>((set, get) => ({
  runs: [],
  isLoading: false,
  error: null,

  setRuns: (runs) => {
    set({ runs })
  },

  updateRun: (runId, updates) => {
    set({
      runs: get().runs.map(run =>
        run.id === runId ? { ...run, ...updates } : run
      )
    })
  },

  addRun: (run) => {
    set({
      runs: [run, ...get().runs]
    })
  },

  setLoading: (loading) => {
    set({ isLoading: loading })
  },

  setError: (error) => {
    set({ error })
  },

  getActiveRuns: () => {
    return get().runs.filter(run => run.status === 'running' || run.isActive)
  },

  getRunBySuper: (supermarket) => {
    return get().runs.find(run => 
      run.supermarkets.slug === supermarket && 
      (run.status === 'running' || run.isActive)
    )
  }
}))

// UI Store
interface UIStore {
  sidebarOpen: boolean
  theme: 'light' | 'dark' | 'system'
  
  // Actions
  setSidebarOpen: (open: boolean) => void
  toggleSidebar: () => void
  setTheme: (theme: 'light' | 'dark' | 'system') => void
}

export const useUIStore = create<UIStore>()(
  persist(
    (set, get) => ({
      sidebarOpen: false,
      theme: 'system',

      setSidebarOpen: (open) => {
        set({ sidebarOpen: open })
      },

      toggleSidebar: () => {
        set({ sidebarOpen: !get().sidebarOpen })
      },

      setTheme: (theme) => {
        set({ theme })
      }
    }),
    {
      name: 'ui-storage',
      storage: createJSONStorage(() => localStorage)
    }
  )
)
