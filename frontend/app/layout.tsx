import './globals.css'
import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import { Providers } from './providers'
import { Header } from '@/components/layout/Header'
import { Footer } from '@/components/layout/Footer'
import { Toaster } from '@/components/ui/toaster'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'SuperCompare - Compare Supermarket Prices',
  description: 'Compare prices between Carrefour and Coto supermarkets in Argentina. Find the best deals and save money on your groceries.',
  keywords: 'supermarket, prices, comparison, carrefour, coto, argentina, groceries, deals',
  authors: [{ name: 'SuperCompare Team' }],
  creator: 'SuperCompare',
  publisher: 'SuperCompare',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'),
  openGraph: {
    title: 'SuperCompare - Compare Supermarket Prices',
    description: 'Compare prices between Carrefour and Coto supermarkets in Argentina.',
    url: '/',
    siteName: 'SuperCompare',
    locale: 'es_AR',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'SuperCompare - Compare Supermarket Prices',
    description: 'Compare prices between Carrefour and Coto supermarkets in Argentina.',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: process.env.GOOGLE_VERIFICATION_ID,
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="es" suppressHydrationWarning>
      <body className={inter.className}>
        <Providers>
          <div className="min-h-screen flex flex-col">
            <Header />
            <main className="flex-1">
              {children}
            </main>
            <Footer />
          </div>
          <Toaster />
        </Providers>
      </body>
    </html>
  )
}
