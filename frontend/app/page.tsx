import { Suspense } from 'react'
import { ProductGrid } from '@/components/products/ProductGrid'
import { SearchFilters } from '@/components/products/SearchFilters'
import { Hero } from '@/components/home/<USER>'
import { Stats } from '@/components/home/<USER>'
import { Features } from '@/components/home/<USER>'
import { ProductGridSkeleton } from '@/components/products/ProductGridSkeleton'

interface HomePageProps {
  searchParams: {
    q?: string
    category?: string
    supermarket?: string
    page?: string
    sort?: string
    order?: string
  }
}

export default function HomePage({ searchParams }: HomePageProps) {
  const hasFilters = Object.keys(searchParams).length > 0

  return (
    <div className="space-y-8">
      {/* Hero Section - only show when no filters applied */}
      {!hasFilters && (
        <>
          <Hero />
          <Stats />
          <Features />
        </>
      )}

      {/* Products Section */}
      <section className="container-wide py-8">
        <div className="space-y-6">
          {/* Page Title */}
          <div className="text-center space-y-2">
            <h1 className="text-3xl font-bold tracking-tight">
              {hasFilters ? 'Resultados de búsqueda' : 'Todos los productos'}
            </h1>
            <p className="text-muted-foreground">
              {hasFilters 
                ? 'Encuentra los mejores precios para tus productos favoritos'
                : 'Compara precios entre Carrefour y Coto'
              }
            </p>
          </div>

          {/* Search and Filters */}
          <SearchFilters />

          {/* Products Grid */}
          <Suspense fallback={<ProductGridSkeleton />}>
            <ProductGrid searchParams={searchParams} />
          </Suspense>
        </div>
      </section>
    </div>
  )
}
