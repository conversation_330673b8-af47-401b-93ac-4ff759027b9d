'use client'

import { createContext, useContext, useEffect, useState } from 'react'
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'
import { SessionContextProvider } from '@supabase/auth-helpers-react'
import { SocketProvider } from '@/lib/socket'
import { ThemeProvider } from '@/components/theme-provider'

// Create Supabase client
const supabase = createClientComponentClient()

// API Context
interface ApiContextType {
  apiUrl: string
  isOnline: boolean
}

const ApiContext = createContext<ApiContextType>({
  apiUrl: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000',
  isOnline: true
})

export const useApi = () => useContext(ApiContext)

// Online status hook
function useOnlineStatus() {
  const [isOnline, setIsOnline] = useState(true)

  useEffect(() => {
    const handleOnline = () => setIsOnline(true)
    const handleOffline = () => setIsOnline(false)

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    // Check initial status
    setIsOnline(navigator.onLine)

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [])

  return isOnline
}

export function Providers({ children }: { children: React.ReactNode }) {
  const isOnline = useOnlineStatus()
  const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000'

  return (
    <ThemeProvider
      attribute="class"
      defaultTheme="system"
      enableSystem
      disableTransitionOnChange
    >
      <SessionContextProvider supabaseClient={supabase}>
        <ApiContext.Provider value={{ apiUrl, isOnline }}>
          <SocketProvider url={apiUrl}>
            {children}
          </SocketProvider>
        </ApiContext.Provider>
      </SessionContextProvider>
    </ThemeProvider>
  )
}
