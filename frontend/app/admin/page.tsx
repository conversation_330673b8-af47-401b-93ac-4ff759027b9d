'use client'

import { useEffect, useState } from 'react'

export default function AdminPage() {
  const [stats, setStats] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetch('/api/admin/stats')
      .then(res => res.json())
      .then(data => {
        setStats(data)
        setLoading(false)
      })
      .catch(err => {
        setError(err.message)
        setLoading(false)
      })
  }, [])

  if (loading) {
    return (
      <div className="container mx-auto py-8 px-4">
        <h1 className="text-3xl font-bold mb-8">Panel de Administración</h1>
        <p>Cargando...</p>
      </div>
    )
  }

  if (error) {
    return (
      <div className="container mx-auto py-8 px-4">
        <h1 className="text-3xl font-bold mb-8">Panel de Administración</h1>
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          Error: {error}
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Panel de Administración</h1>
        <p className="text-gray-600">
          Monitorea el estado del sistema y gestiona los scrapers de datos.
        </p>
      </div>

      {/* Stats Overview */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4 mb-8">
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="text-2xl font-bold text-blue-600">
            {stats?.totalProducts?.toLocaleString() || '0'}
          </div>
          <div className="text-gray-600">Productos Totales</div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="text-2xl font-bold text-red-600">
            {stats?.productStats?.carrefour?.toLocaleString() || '0'}
          </div>
          <div className="text-gray-600">Productos Carrefour</div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="text-2xl font-bold text-green-600">
            {stats?.productStats?.coto?.toLocaleString() || '0'}
          </div>
          <div className="text-gray-600">Productos Coto</div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="text-2xl font-bold text-purple-600">
            {stats?.totalPriceRecords?.toLocaleString() || '0'}
          </div>
          <div className="text-gray-600">Registros de Precios</div>
        </div>
      </div>

      {/* Recent Scraper Runs */}
      <div className="bg-white p-6 rounded-lg shadow">
        <h2 className="text-xl font-semibold mb-4">Ejecuciones Recientes</h2>
        <div className="space-y-4">
          {stats?.recentScraperRuns?.slice(0, 5).map((run: any) => (
            <div key={run.id} className="flex items-center justify-between p-4 border rounded-lg">
              <div className="flex items-center gap-4">
                <div className={`w-3 h-3 rounded-full ${
                  run.status === 'completed' ? 'bg-green-500' :
                  run.status === 'running' ? 'bg-blue-500' :
                  'bg-red-500'
                }`} />
                <div>
                  <p className="font-medium">{run.supermarkets.name}</p>
                  <p className="text-sm text-gray-600">
                    {run.products_scraped?.toLocaleString()} productos
                  </p>
                </div>
              </div>
              <div className="text-right">
                <span className={`px-2 py-1 rounded text-sm ${
                  run.status === 'completed' ? 'bg-green-100 text-green-800' :
                  run.status === 'running' ? 'bg-blue-100 text-blue-800' :
                  'bg-red-100 text-red-800'
                }`}>
                  {run.status === 'completed' ? 'Completado' :
                   run.status === 'running' ? 'En ejecución' :
                   'Error'}
                </span>
                <p className="text-sm text-gray-600 mt-1">
                  {new Date(run.started_at).toLocaleString()}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
