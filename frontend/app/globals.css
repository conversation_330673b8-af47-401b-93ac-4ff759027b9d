@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer components {
  /* Custom scrollbar */
  .custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--muted-foreground)) hsl(var(--muted));
  }

  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: hsl(var(--muted));
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: hsl(var(--muted-foreground));
    border-radius: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--foreground));
  }

  /* Product card hover effects */
  .product-card {
    @apply transition-all duration-200 hover:shadow-lg hover:-translate-y-1;
  }

  /* Price comparison styles */
  .price-better {
    @apply text-green-600 font-semibold;
  }

  .price-worse {
    @apply text-red-600;
  }

  .price-equal {
    @apply text-gray-600;
  }

  /* Loading skeleton */
  .skeleton {
    @apply animate-pulse bg-muted rounded;
  }

  /* Supermarket brand styles */
  .brand-carrefour {
    @apply bg-carrefour text-white;
  }

  .brand-coto {
    @apply bg-coto text-white;
  }

  /* Progress bar styles */
  .progress-bar {
    @apply w-full bg-muted rounded-full h-2 overflow-hidden;
  }

  .progress-fill {
    @apply h-full bg-primary transition-all duration-300 ease-out;
  }

  /* Cart styles */
  .cart-item {
    @apply border-b border-border last:border-b-0 py-4;
  }

  /* Admin panel styles */
  .admin-card {
    @apply bg-card border border-border rounded-lg p-6 shadow-sm;
  }

  .admin-stat {
    @apply text-center p-4 bg-muted rounded-lg;
  }

  .admin-stat-value {
    @apply text-2xl font-bold text-primary;
  }

  .admin-stat-label {
    @apply text-sm text-muted-foreground mt-1;
  }
}

@layer utilities {
  /* Text utilities */
  .text-balance {
    text-wrap: balance;
  }

  /* Layout utilities */
  .container-narrow {
    @apply max-w-4xl mx-auto px-4;
  }

  .container-wide {
    @apply max-w-7xl mx-auto px-4;
  }

  /* Animation utilities */
  .animate-fade-in {
    @apply animate-fade-in;
  }

  .animate-slide-in {
    @apply animate-slide-in;
  }
}
