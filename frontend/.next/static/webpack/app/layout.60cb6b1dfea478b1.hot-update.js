"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = (\"31738a0bffb6\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9nbG9iYWxzLmNzcyIsIm1hcHBpbmdzIjoiO0FBQUEsK0RBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vYXBwL2dsb2JhbHMuY3NzP2I0MmQiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIzMTczOGEwYmZmYjZcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/providers.tsx":
/*!***************************!*\
  !*** ./app/providers.tsx ***!
  \***************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: function() { return /* binding */ Providers; },\n/* harmony export */   useApi: function() { return /* binding */ useApi; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @supabase/auth-helpers-nextjs */ \"(app-pages-browser)/./node_modules/@supabase/auth-helpers-nextjs/dist/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _supabase_auth_helpers_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @supabase/auth-helpers-react */ \"(app-pages-browser)/./node_modules/@supabase/auth-helpers-react/dist/index.js\");\n/* harmony import */ var _supabase_auth_helpers_react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_supabase_auth_helpers_react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _lib_socket__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/socket */ \"(app-pages-browser)/./lib/socket.tsx\");\n/* harmony import */ var _components_theme_provider__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/theme-provider */ \"(app-pages-browser)/./components/theme-provider.tsx\");\n/* __next_internal_client_entry_do_not_use__ useApi,Providers auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\n\n\n\n// Create Supabase client with fallback values\nconst supabaseUrl = \"https://your-project.supabase.co\" || 0;\nconst supabaseAnonKey = \"your_anon_key_here\" || 0;\nconst supabase = (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2__.createClientComponentClient)({\n    supabaseUrl,\n    supabaseKey: supabaseAnonKey\n});\nconst ApiContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    apiUrl: \"http://localhost:5001\" || 0,\n    isOnline: true\n});\nconst useApi = ()=>{\n    _s();\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ApiContext);\n};\n_s(useApi, \"gDsCjeeItUuvgOWf1v4qoK9RF6k=\");\n// Online status hook\nfunction useOnlineStatus() {\n    _s1();\n    const [isOnline, setIsOnline] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleOnline = ()=>setIsOnline(true);\n        const handleOffline = ()=>setIsOnline(false);\n        window.addEventListener(\"online\", handleOnline);\n        window.addEventListener(\"offline\", handleOffline);\n        // Check initial status\n        setIsOnline(navigator.onLine);\n        return ()=>{\n            window.removeEventListener(\"online\", handleOnline);\n            window.removeEventListener(\"offline\", handleOffline);\n        };\n    }, []);\n    return isOnline;\n}\n_s1(useOnlineStatus, \"mRBquyBAMh60D2Q5WI/A8/L/7j4=\");\nfunction Providers(param) {\n    let { children } = param;\n    _s2();\n    const isOnline = useOnlineStatus();\n    const apiUrl = \"http://localhost:5001\" || 0;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_theme_provider__WEBPACK_IMPORTED_MODULE_5__.ThemeProvider, {\n        attribute: \"class\",\n        defaultTheme: \"system\",\n        enableSystem: true,\n        disableTransitionOnChange: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_supabase_auth_helpers_react__WEBPACK_IMPORTED_MODULE_3__.SessionContextProvider, {\n            supabaseClient: supabase,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ApiContext.Provider, {\n                value: {\n                    apiUrl,\n                    isOnline\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_socket__WEBPACK_IMPORTED_MODULE_4__.SocketProvider, {\n                    url: apiUrl,\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/Volumes/2tb/github/super-scraper/frontend/app/providers.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Volumes/2tb/github/super-scraper/frontend/app/providers.tsx\",\n                lineNumber: 66,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Volumes/2tb/github/super-scraper/frontend/app/providers.tsx\",\n            lineNumber: 65,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Volumes/2tb/github/super-scraper/frontend/app/providers.tsx\",\n        lineNumber: 59,\n        columnNumber: 5\n    }, this);\n}\n_s2(Providers, \"d8TOj6JB44UG9shajNdgfJB5rPM=\", false, function() {\n    return [\n        useOnlineStatus\n    ];\n});\n_c = Providers;\nvar _c;\n$RefreshReg$(_c, \"Providers\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/providers.tsx\n"));

/***/ })

});