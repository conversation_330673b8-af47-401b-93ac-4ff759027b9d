"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./app/admin/page.tsx":
/*!****************************!*\
  !*** ./app/admin/page.tsx ***!
  \****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AdminPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction AdminPage() {\n    var _stats_totalProducts, _stats_productStats_carrefour, _stats_productStats, _stats_productStats_coto, _stats_productStats1, _stats_totalPriceRecords, _stats_recentScraperRuns;\n    _s();\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetch(\"/api/admin/stats\").then((res)=>res.json()).then((data)=>{\n            setStats(data);\n            setLoading(false);\n        }).catch((err)=>{\n            setError(err.message);\n            setLoading(false);\n        });\n    }, []);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto py-8 px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-3xl font-bold mb-8\",\n                    children: \"Panel de Administraci\\xf3n\"\n                }, void 0, false, {\n                    fileName: \"/Volumes/2tb/github/super-scraper/frontend/app/admin/page.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: \"Cargando...\"\n                }, void 0, false, {\n                    fileName: \"/Volumes/2tb/github/super-scraper/frontend/app/admin/page.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Volumes/2tb/github/super-scraper/frontend/app/admin/page.tsx\",\n            lineNumber: 25,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto py-8 px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-3xl font-bold mb-8\",\n                    children: \"Panel de Administraci\\xf3n\"\n                }, void 0, false, {\n                    fileName: \"/Volumes/2tb/github/super-scraper/frontend/app/admin/page.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded\",\n                    children: [\n                        \"Error: \",\n                        error\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/2tb/github/super-scraper/frontend/app/admin/page.tsx\",\n                    lineNumber: 36,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Volumes/2tb/github/super-scraper/frontend/app/admin/page.tsx\",\n            lineNumber: 34,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto py-8 px-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold mb-2\",\n                        children: \"Panel de Administraci\\xf3n\"\n                    }, void 0, false, {\n                        fileName: \"/Volumes/2tb/github/super-scraper/frontend/app/admin/page.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Monitorea el estado del sistema y gestiona los scrapers de datos.\"\n                    }, void 0, false, {\n                        fileName: \"/Volumes/2tb/github/super-scraper/frontend/app/admin/page.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/2tb/github/super-scraper/frontend/app/admin/page.tsx\",\n                lineNumber: 45,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-6 md:grid-cols-2 lg:grid-cols-4 mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white p-6 rounded-lg shadow\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-blue-600\",\n                                children: (stats === null || stats === void 0 ? void 0 : (_stats_totalProducts = stats.totalProducts) === null || _stats_totalProducts === void 0 ? void 0 : _stats_totalProducts.toLocaleString()) || \"0\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/2tb/github/super-scraper/frontend/app/admin/page.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-600\",\n                                children: \"Productos Totales\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/2tb/github/super-scraper/frontend/app/admin/page.tsx\",\n                                lineNumber: 58,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/2tb/github/super-scraper/frontend/app/admin/page.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white p-6 rounded-lg shadow\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-red-600\",\n                                children: (stats === null || stats === void 0 ? void 0 : (_stats_productStats = stats.productStats) === null || _stats_productStats === void 0 ? void 0 : (_stats_productStats_carrefour = _stats_productStats.carrefour) === null || _stats_productStats_carrefour === void 0 ? void 0 : _stats_productStats_carrefour.toLocaleString()) || \"0\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/2tb/github/super-scraper/frontend/app/admin/page.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-600\",\n                                children: \"Productos Carrefour\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/2tb/github/super-scraper/frontend/app/admin/page.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/2tb/github/super-scraper/frontend/app/admin/page.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white p-6 rounded-lg shadow\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-green-600\",\n                                children: (stats === null || stats === void 0 ? void 0 : (_stats_productStats1 = stats.productStats) === null || _stats_productStats1 === void 0 ? void 0 : (_stats_productStats_coto = _stats_productStats1.coto) === null || _stats_productStats_coto === void 0 ? void 0 : _stats_productStats_coto.toLocaleString()) || \"0\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/2tb/github/super-scraper/frontend/app/admin/page.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-600\",\n                                children: \"Productos Coto\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/2tb/github/super-scraper/frontend/app/admin/page.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/2tb/github/super-scraper/frontend/app/admin/page.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white p-6 rounded-lg shadow\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-purple-600\",\n                                children: (stats === null || stats === void 0 ? void 0 : (_stats_totalPriceRecords = stats.totalPriceRecords) === null || _stats_totalPriceRecords === void 0 ? void 0 : _stats_totalPriceRecords.toLocaleString()) || \"0\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/2tb/github/super-scraper/frontend/app/admin/page.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-600\",\n                                children: \"Registros de Precios\"\n                            }, void 0, false, {\n                                fileName: \"/Volumes/2tb/github/super-scraper/frontend/app/admin/page.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/2tb/github/super-scraper/frontend/app/admin/page.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/2tb/github/super-scraper/frontend/app/admin/page.tsx\",\n                lineNumber: 53,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-6 rounded-lg shadow\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold mb-4\",\n                        children: \"Ejecuciones Recientes\"\n                    }, void 0, false, {\n                        fileName: \"/Volumes/2tb/github/super-scraper/frontend/app/admin/page.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: stats === null || stats === void 0 ? void 0 : (_stats_recentScraperRuns = stats.recentScraperRuns) === null || _stats_recentScraperRuns === void 0 ? void 0 : _stats_recentScraperRuns.slice(0, 5).map((run)=>{\n                            var _run_products_scraped;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between p-4 border rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-3 h-3 rounded-full \".concat(run.status === \"completed\" ? \"bg-green-500\" : run.status === \"running\" ? \"bg-blue-500\" : \"bg-red-500\")\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/2tb/github/super-scraper/frontend/app/admin/page.tsx\",\n                                                lineNumber: 90,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium\",\n                                                        children: run.supermarkets.name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/2tb/github/super-scraper/frontend/app/admin/page.tsx\",\n                                                        lineNumber: 96,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: [\n                                                            (_run_products_scraped = run.products_scraped) === null || _run_products_scraped === void 0 ? void 0 : _run_products_scraped.toLocaleString(),\n                                                            \" productos\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/2tb/github/super-scraper/frontend/app/admin/page.tsx\",\n                                                        lineNumber: 97,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/2tb/github/super-scraper/frontend/app/admin/page.tsx\",\n                                                lineNumber: 95,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/2tb/github/super-scraper/frontend/app/admin/page.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-right\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"px-2 py-1 rounded text-sm \".concat(run.status === \"completed\" ? \"bg-green-100 text-green-800\" : run.status === \"running\" ? \"bg-blue-100 text-blue-800\" : \"bg-red-100 text-red-800\"),\n                                                children: run.status === \"completed\" ? \"Completado\" : run.status === \"running\" ? \"En ejecuci\\xf3n\" : \"Error\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/2tb/github/super-scraper/frontend/app/admin/page.tsx\",\n                                                lineNumber: 103,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600 mt-1\",\n                                                children: new Date(run.started_at).toLocaleString()\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/2tb/github/super-scraper/frontend/app/admin/page.tsx\",\n                                                lineNumber: 112,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/2tb/github/super-scraper/frontend/app/admin/page.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, run.id, true, {\n                                fileName: \"/Volumes/2tb/github/super-scraper/frontend/app/admin/page.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 13\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"/Volumes/2tb/github/super-scraper/frontend/app/admin/page.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/2tb/github/super-scraper/frontend/app/admin/page.tsx\",\n                lineNumber: 84,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Volumes/2tb/github/super-scraper/frontend/app/admin/page.tsx\",\n        lineNumber: 44,\n        columnNumber: 5\n    }, this);\n}\n_s(AdminPage, \"wn5GnbEWSpy3babVhLIjrKhKWKg=\");\n_c = AdminPage;\nvar _c;\n$RefreshReg$(_c, \"AdminPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/admin/page.tsx\n"));

/***/ })

});