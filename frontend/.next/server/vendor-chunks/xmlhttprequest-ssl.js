/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/xmlhttprequest-ssl";
exports.ids = ["vendor-chunks/xmlhttprequest-ssl"];
exports.modules = {

/***/ "(ssr)/./node_modules/xmlhttprequest-ssl/lib/XMLHttpRequest.js":
/*!***************************************************************!*\
  !*** ./node_modules/xmlhttprequest-ssl/lib/XMLHttpRequest.js ***!
  \***************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/**\n * Wrapper for built-in http.js to emulate the browser XMLHttpRequest object.\n *\n * This can be used with JS designed for browsers to improve reuse of code and\n * allow the use of existing libraries.\n *\n * Usage: include(\"XMLHttpRequest.js\") and use XMLHttpRequest per W3C specs.\n *\n * <AUTHOR> DeFelippi <<EMAIL>>\n * @contributor David Ellis <<EMAIL>>\n * @license MIT\n */ var fs = __webpack_require__(/*! fs */ \"fs\");\nvar Url = __webpack_require__(/*! url */ \"url\");\nvar spawn = (__webpack_require__(/*! child_process */ \"child_process\").spawn);\n/**\n * Module exports.\n */ module.exports = XMLHttpRequest;\n// backwards-compat\nXMLHttpRequest.XMLHttpRequest = XMLHttpRequest;\n/**\n * `XMLHttpRequest` constructor.\n *\n * Supported options for the `opts` object are:\n *\n *  - `agent`: An http.Agent instance; http.globalAgent may be used; if 'undefined', agent usage is disabled\n *\n * @param {Object} opts optional \"options\" object\n */ function XMLHttpRequest(opts) {\n    \"use strict\";\n    opts = opts || {};\n    /**\n   * Private variables\n   */ var self = this;\n    var http = __webpack_require__(/*! http */ \"http\");\n    var https = __webpack_require__(/*! https */ \"https\");\n    // Holds http.js objects\n    var request;\n    var response;\n    // Request settings\n    var settings = {};\n    // Disable header blacklist.\n    // Not part of XHR specs.\n    var disableHeaderCheck = false;\n    // Set some default headers\n    var defaultHeaders = {\n        \"User-Agent\": \"node-XMLHttpRequest\",\n        \"Accept\": \"*/*\"\n    };\n    var headers = Object.assign({}, defaultHeaders);\n    // These headers are not user setable.\n    // The following are allowed but banned in the spec:\n    // * user-agent\n    var forbiddenRequestHeaders = [\n        \"accept-charset\",\n        \"accept-encoding\",\n        \"access-control-request-headers\",\n        \"access-control-request-method\",\n        \"connection\",\n        \"content-length\",\n        \"content-transfer-encoding\",\n        \"cookie\",\n        \"cookie2\",\n        \"date\",\n        \"expect\",\n        \"host\",\n        \"keep-alive\",\n        \"origin\",\n        \"referer\",\n        \"te\",\n        \"trailer\",\n        \"transfer-encoding\",\n        \"upgrade\",\n        \"via\"\n    ];\n    // These request methods are not allowed\n    var forbiddenRequestMethods = [\n        \"TRACE\",\n        \"TRACK\",\n        \"CONNECT\"\n    ];\n    // Send flag\n    var sendFlag = false;\n    // Error flag, used when errors occur or abort is called\n    var errorFlag = false;\n    var abortedFlag = false;\n    // Event listeners\n    var listeners = {};\n    /**\n   * Constants\n   */ this.UNSENT = 0;\n    this.OPENED = 1;\n    this.HEADERS_RECEIVED = 2;\n    this.LOADING = 3;\n    this.DONE = 4;\n    /**\n   * Public vars\n   */ // Current state\n    this.readyState = this.UNSENT;\n    // default ready state change handler in case one is not set or is set late\n    this.onreadystatechange = null;\n    // Result & response\n    this.responseText = \"\";\n    this.responseXML = \"\";\n    this.response = Buffer.alloc(0);\n    this.status = null;\n    this.statusText = null;\n    /**\n   * Private methods\n   */ /**\n   * Check if the specified header is allowed.\n   *\n   * @param string header Header to validate\n   * @return boolean False if not allowed, otherwise true\n   */ var isAllowedHttpHeader = function(header) {\n        return disableHeaderCheck || header && forbiddenRequestHeaders.indexOf(header.toLowerCase()) === -1;\n    };\n    /**\n   * Check if the specified method is allowed.\n   *\n   * @param string method Request method to validate\n   * @return boolean False if not allowed, otherwise true\n   */ var isAllowedHttpMethod = function(method) {\n        return method && forbiddenRequestMethods.indexOf(method) === -1;\n    };\n    /**\n   * Public methods\n   */ /**\n   * Open the connection. Currently supports local server requests.\n   *\n   * @param string method Connection method (eg GET, POST)\n   * @param string url URL for the connection.\n   * @param boolean async Asynchronous connection. Default is true.\n   * @param string user Username for basic authentication (optional)\n   * @param string password Password for basic authentication (optional)\n   */ this.open = function(method, url, async, user, password) {\n        this.abort();\n        errorFlag = false;\n        abortedFlag = false;\n        // Check for valid request method\n        if (!isAllowedHttpMethod(method)) {\n            throw new Error(\"SecurityError: Request method not allowed\");\n        }\n        settings = {\n            \"method\": method,\n            \"url\": url.toString(),\n            \"async\": typeof async !== \"boolean\" ? true : async,\n            \"user\": user || null,\n            \"password\": password || null\n        };\n        setState(this.OPENED);\n    };\n    /**\n   * Disables or enables isAllowedHttpHeader() check the request. Enabled by default.\n   * This does not conform to the W3C spec.\n   *\n   * @param boolean state Enable or disable header checking.\n   */ this.setDisableHeaderCheck = function(state) {\n        disableHeaderCheck = state;\n    };\n    /**\n   * Sets a header for the request.\n   *\n   * @param string header Header name\n   * @param string value Header value\n   * @return boolean Header added\n   */ this.setRequestHeader = function(header, value) {\n        if (this.readyState != this.OPENED) {\n            throw new Error(\"INVALID_STATE_ERR: setRequestHeader can only be called when state is OPEN\");\n        }\n        if (!isAllowedHttpHeader(header)) {\n            console.warn('Refused to set unsafe header \"' + header + '\"');\n            return false;\n        }\n        if (sendFlag) {\n            throw new Error(\"INVALID_STATE_ERR: send flag is true\");\n        }\n        headers[header] = value;\n        return true;\n    };\n    /**\n   * Gets a header from the server response.\n   *\n   * @param string header Name of header to get.\n   * @return string Text of the header or null if it doesn't exist.\n   */ this.getResponseHeader = function(header) {\n        if (typeof header === \"string\" && this.readyState > this.OPENED && response.headers[header.toLowerCase()] && !errorFlag) {\n            return response.headers[header.toLowerCase()];\n        }\n        return null;\n    };\n    /**\n   * Gets all the response headers.\n   *\n   * @return string A string with all response headers separated by CR+LF\n   */ this.getAllResponseHeaders = function() {\n        if (this.readyState < this.HEADERS_RECEIVED || errorFlag) {\n            return \"\";\n        }\n        var result = \"\";\n        for(var i in response.headers){\n            // Cookie headers are excluded\n            if (i !== \"set-cookie\" && i !== \"set-cookie2\") {\n                result += i + \": \" + response.headers[i] + \"\\r\\n\";\n            }\n        }\n        return result.substr(0, result.length - 2);\n    };\n    /**\n   * Gets a request header\n   *\n   * @param string name Name of header to get\n   * @return string Returns the request header or empty string if not set\n   */ this.getRequestHeader = function(name) {\n        // @TODO Make this case insensitive\n        if (typeof name === \"string\" && headers[name]) {\n            return headers[name];\n        }\n        return \"\";\n    };\n    /**\n   * Sends the request to the server.\n   *\n   * @param string data Optional data to send as request body.\n   */ this.send = function(data) {\n        if (this.readyState != this.OPENED) {\n            throw new Error(\"INVALID_STATE_ERR: connection must be opened before send() is called\");\n        }\n        if (sendFlag) {\n            throw new Error(\"INVALID_STATE_ERR: send has already been called\");\n        }\n        var ssl = false, local = false;\n        var url = Url.parse(settings.url);\n        var host;\n        // Determine the server\n        switch(url.protocol){\n            case \"https:\":\n                ssl = true;\n            // SSL & non-SSL both need host, no break here.\n            case \"http:\":\n                host = url.hostname;\n                break;\n            case \"file:\":\n                local = true;\n                break;\n            case undefined:\n            case \"\":\n                host = \"localhost\";\n                break;\n            default:\n                throw new Error(\"Protocol not supported.\");\n        }\n        // Load files off the local filesystem (file://)\n        if (local) {\n            if (settings.method !== \"GET\") {\n                throw new Error(\"XMLHttpRequest: Only GET method is supported\");\n            }\n            if (settings.async) {\n                fs.readFile(unescape(url.pathname), function(error, data) {\n                    if (error) {\n                        self.handleError(error, error.errno || -1);\n                    } else {\n                        self.status = 200;\n                        self.responseText = data.toString(\"utf8\");\n                        self.response = data;\n                        setState(self.DONE);\n                    }\n                });\n            } else {\n                try {\n                    this.response = fs.readFileSync(unescape(url.pathname));\n                    this.responseText = this.response.toString(\"utf8\");\n                    this.status = 200;\n                    setState(self.DONE);\n                } catch (e) {\n                    this.handleError(e, e.errno || -1);\n                }\n            }\n            return;\n        }\n        // Default to port 80. If accessing localhost on another port be sure\n        // to use http://localhost:port/path\n        var port = url.port || (ssl ? 443 : 80);\n        // Add query string if one is used\n        var uri = url.pathname + (url.search ? url.search : \"\");\n        // Set the Host header or the server may reject the request\n        headers[\"Host\"] = host;\n        if (!(ssl && port === 443 || port === 80)) {\n            headers[\"Host\"] += \":\" + url.port;\n        }\n        // Set Basic Auth if necessary\n        if (settings.user) {\n            if (typeof settings.password == \"undefined\") {\n                settings.password = \"\";\n            }\n            var authBuf = new Buffer(settings.user + \":\" + settings.password);\n            headers[\"Authorization\"] = \"Basic \" + authBuf.toString(\"base64\");\n        }\n        // Set content length header\n        if (settings.method === \"GET\" || settings.method === \"HEAD\") {\n            data = null;\n        } else if (data) {\n            headers[\"Content-Length\"] = Buffer.isBuffer(data) ? data.length : Buffer.byteLength(data);\n            var headersKeys = Object.keys(headers);\n            if (!headersKeys.some(function(h) {\n                return h.toLowerCase() === \"content-type\";\n            })) {\n                headers[\"Content-Type\"] = \"text/plain;charset=UTF-8\";\n            }\n        } else if (settings.method === \"POST\") {\n            // For a post with no data set Content-Length: 0.\n            // This is required by buggy servers that don't meet the specs.\n            headers[\"Content-Length\"] = 0;\n        }\n        var agent = opts.agent || false;\n        var options = {\n            host: host,\n            port: port,\n            path: uri,\n            method: settings.method,\n            headers: headers,\n            agent: agent\n        };\n        if (ssl) {\n            options.pfx = opts.pfx;\n            options.key = opts.key;\n            options.passphrase = opts.passphrase;\n            options.cert = opts.cert;\n            options.ca = opts.ca;\n            options.ciphers = opts.ciphers;\n            options.rejectUnauthorized = opts.rejectUnauthorized === false ? false : true;\n        }\n        // Reset error flag\n        errorFlag = false;\n        // Handle async requests\n        if (settings.async) {\n            // Use the proper protocol\n            var doRequest = ssl ? https.request : http.request;\n            // Request is being sent, set send flag\n            sendFlag = true;\n            // As per spec, this is called here for historical reasons.\n            self.dispatchEvent(\"readystatechange\");\n            // Handler for the response\n            var responseHandler = function(resp) {\n                // Set response var to the response we got back\n                // This is so it remains accessable outside this scope\n                response = resp;\n                // Check for redirect\n                // @TODO Prevent looped redirects\n                if (response.statusCode === 302 || response.statusCode === 303 || response.statusCode === 307) {\n                    // Change URL to the redirect location\n                    settings.url = response.headers.location;\n                    var url = Url.parse(settings.url);\n                    // Set host var in case it's used later\n                    host = url.hostname;\n                    // Options for the new request\n                    var newOptions = {\n                        hostname: url.hostname,\n                        port: url.port,\n                        path: url.path,\n                        method: response.statusCode === 303 ? \"GET\" : settings.method,\n                        headers: headers\n                    };\n                    if (ssl) {\n                        newOptions.pfx = opts.pfx;\n                        newOptions.key = opts.key;\n                        newOptions.passphrase = opts.passphrase;\n                        newOptions.cert = opts.cert;\n                        newOptions.ca = opts.ca;\n                        newOptions.ciphers = opts.ciphers;\n                        newOptions.rejectUnauthorized = opts.rejectUnauthorized === false ? false : true;\n                    }\n                    // Issue the new request\n                    request = doRequest(newOptions, responseHandler).on(\"error\", errorHandler);\n                    request.end();\n                    // @TODO Check if an XHR event needs to be fired here\n                    return;\n                }\n                setState(self.HEADERS_RECEIVED);\n                self.status = response.statusCode;\n                response.on(\"data\", function(chunk) {\n                    // Make sure there's some data\n                    if (chunk) {\n                        var data = Buffer.from(chunk);\n                        self.response = Buffer.concat([\n                            self.response,\n                            data\n                        ]);\n                    }\n                    // Don't emit state changes if the connection has been aborted.\n                    if (sendFlag) {\n                        setState(self.LOADING);\n                    }\n                });\n                response.on(\"end\", function() {\n                    if (sendFlag) {\n                        // The sendFlag needs to be set before setState is called.  Otherwise if we are chaining callbacks\n                        // there can be a timing issue (the callback is called and a new call is made before the flag is reset).\n                        sendFlag = false;\n                        // Discard the 'end' event if the connection has been aborted\n                        setState(self.DONE);\n                        // Construct responseText from response\n                        self.responseText = self.response.toString(\"utf8\");\n                    }\n                });\n                response.on(\"error\", function(error) {\n                    self.handleError(error);\n                });\n            };\n            // Error handler for the request\n            var errorHandler = function(error) {\n                // In the case of https://nodejs.org/api/http.html#requestreusedsocket triggering an ECONNRESET,\n                // don't fail the xhr request, attempt again.\n                if (request.reusedSocket && error.code === \"ECONNRESET\") return doRequest(options, responseHandler).on(\"error\", errorHandler);\n                self.handleError(error);\n            };\n            // Create the request\n            request = doRequest(options, responseHandler).on(\"error\", errorHandler);\n            if (opts.autoUnref) {\n                request.on(\"socket\", (socket)=>{\n                    socket.unref();\n                });\n            }\n            // Node 0.4 and later won't accept empty data. Make sure it's needed.\n            if (data) {\n                request.write(data);\n            }\n            request.end();\n            self.dispatchEvent(\"loadstart\");\n        } else {\n            // Create a temporary file for communication with the other Node process\n            var contentFile = \".node-xmlhttprequest-content-\" + process.pid;\n            var syncFile = \".node-xmlhttprequest-sync-\" + process.pid;\n            fs.writeFileSync(syncFile, \"\", \"utf8\");\n            // The async request the other Node process executes\n            var execString = \"var http = require('http'), https = require('https'), fs = require('fs');\" + \"var doRequest = http\" + (ssl ? \"s\" : \"\") + \".request;\" + \"var options = \" + JSON.stringify(options) + \";\" + \"var responseText = '';\" + \"var responseData = Buffer.alloc(0);\" + \"var req = doRequest(options, function(response) {\" + \"response.on('data', function(chunk) {\" + \"  var data = Buffer.from(chunk);\" + \"  responseText += data.toString('utf8');\" + \"  responseData = Buffer.concat([responseData, data]);\" + \"});\" + \"response.on('end', function() {\" + \"fs.writeFileSync('\" + contentFile + \"', JSON.stringify({err: null, data: {statusCode: response.statusCode, headers: response.headers, text: responseText, data: responseData.toString('base64')}}), 'utf8');\" + \"fs.unlinkSync('\" + syncFile + \"');\" + \"});\" + \"response.on('error', function(error) {\" + \"fs.writeFileSync('\" + contentFile + \"', 'NODE-XMLHTTPREQUEST-ERROR:' + JSON.stringify(error), 'utf8');\" + \"fs.unlinkSync('\" + syncFile + \"');\" + \"});\" + \"}).on('error', function(error) {\" + \"fs.writeFileSync('\" + contentFile + \"', 'NODE-XMLHTTPREQUEST-ERROR:' + JSON.stringify(error), 'utf8');\" + \"fs.unlinkSync('\" + syncFile + \"');\" + \"});\" + (data ? \"req.write('\" + JSON.stringify(data).slice(1, -1).replace(/'/g, \"\\\\'\") + \"');\" : \"\") + \"req.end();\";\n            // Start the other Node Process, executing this string\n            var syncProc = spawn(process.argv[0], [\n                \"-e\",\n                execString\n            ]);\n            var statusText;\n            while(fs.existsSync(syncFile)){\n            // Wait while the sync file is empty\n            }\n            self.responseText = fs.readFileSync(contentFile, \"utf8\");\n            // Kill the child process once the file has data\n            syncProc.stdin.end();\n            // Remove the temporary file\n            fs.unlinkSync(contentFile);\n            if (self.responseText.match(/^NODE-XMLHTTPREQUEST-ERROR:/)) {\n                // If the file returned an error, handle it\n                var errorObj = JSON.parse(self.responseText.replace(/^NODE-XMLHTTPREQUEST-ERROR:/, \"\"));\n                self.handleError(errorObj, 503);\n            } else {\n                // If the file returned okay, parse its data and move to the DONE state\n                self.status = self.responseText.replace(/^NODE-XMLHTTPREQUEST-STATUS:([0-9]*),.*/, \"$1\");\n                var resp = JSON.parse(self.responseText.replace(/^NODE-XMLHTTPREQUEST-STATUS:[0-9]*,(.*)/, \"$1\"));\n                response = {\n                    statusCode: self.status,\n                    headers: resp.data.headers\n                };\n                self.responseText = resp.data.text;\n                self.response = Buffer.from(resp.data.data, \"base64\");\n                setState(self.DONE, true);\n            }\n        }\n    };\n    /**\n   * Called when an error is encountered to deal with it.\n   * @param  status  {number}    HTTP status code to use rather than the default (0) for XHR errors.\n   */ this.handleError = function(error, status) {\n        this.status = status || 0;\n        this.statusText = error;\n        this.responseText = error.stack;\n        errorFlag = true;\n        setState(this.DONE);\n    };\n    /**\n   * Aborts a request.\n   */ this.abort = function() {\n        if (request) {\n            request.abort();\n            request = null;\n        }\n        headers = Object.assign({}, defaultHeaders);\n        this.responseText = \"\";\n        this.responseXML = \"\";\n        this.response = Buffer.alloc(0);\n        errorFlag = abortedFlag = true;\n        if (this.readyState !== this.UNSENT && (this.readyState !== this.OPENED || sendFlag) && this.readyState !== this.DONE) {\n            sendFlag = false;\n            setState(this.DONE);\n        }\n        this.readyState = this.UNSENT;\n    };\n    /**\n   * Adds an event listener. Preferred method of binding to events.\n   */ this.addEventListener = function(event, callback) {\n        if (!(event in listeners)) {\n            listeners[event] = [];\n        }\n        // Currently allows duplicate callbacks. Should it?\n        listeners[event].push(callback);\n    };\n    /**\n   * Remove an event callback that has already been bound.\n   * Only works on the matching funciton, cannot be a copy.\n   */ this.removeEventListener = function(event, callback) {\n        if (event in listeners) {\n            // Filter will return a new array with the callback removed\n            listeners[event] = listeners[event].filter(function(ev) {\n                return ev !== callback;\n            });\n        }\n    };\n    /**\n   * Dispatch any events, including both \"on\" methods and events attached using addEventListener.\n   */ this.dispatchEvent = function(event) {\n        if (typeof self[\"on\" + event] === \"function\") {\n            if (this.readyState === this.DONE && settings.async) setTimeout(function() {\n                self[\"on\" + event]();\n            }, 0);\n            else self[\"on\" + event]();\n        }\n        if (event in listeners) {\n            for(let i = 0, len = listeners[event].length; i < len; i++){\n                if (this.readyState === this.DONE) setTimeout(function() {\n                    listeners[event][i].call(self);\n                }, 0);\n                else listeners[event][i].call(self);\n            }\n        }\n    };\n    /**\n   * Changes readyState and calls onreadystatechange.\n   *\n   * @param int state New state\n   */ var setState = function(state) {\n        if (self.readyState === state || self.readyState === self.UNSENT && abortedFlag) return;\n        self.readyState = state;\n        if (settings.async || self.readyState < self.OPENED || self.readyState === self.DONE) {\n            self.dispatchEvent(\"readystatechange\");\n        }\n        if (self.readyState === self.DONE) {\n            let fire;\n            if (abortedFlag) fire = \"abort\";\n            else if (errorFlag) fire = \"error\";\n            else fire = \"load\";\n            self.dispatchEvent(fire);\n            // @TODO figure out InspectorInstrumentation::didLoadXHR(cookie)\n            self.dispatchEvent(\"loadend\");\n        }\n    };\n}\n;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/xmlhttprequest-ssl/lib/XMLHttpRequest.js\n");

/***/ })

};
;