/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/socket.io-client";
exports.ids = ["vendor-chunks/socket.io-client"];
exports.modules = {

/***/ "(ssr)/./node_modules/socket.io-client/node_modules/debug/src/browser.js":
/*!*************************************************************************!*\
  !*** ./node_modules/socket.io-client/node_modules/debug/src/browser.js ***!
  \*************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval("/* eslint-env browser */ /**\n * This is the web browser implementation of `debug()`.\n */ exports.formatArgs = formatArgs;\nexports.save = save;\nexports.load = load;\nexports.useColors = useColors;\nexports.storage = localstorage();\nexports.destroy = (()=>{\n    let warned = false;\n    return ()=>{\n        if (!warned) {\n            warned = true;\n            console.warn(\"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.\");\n        }\n    };\n})();\n/**\n * Colors.\n */ exports.colors = [\n    \"#0000CC\",\n    \"#0000FF\",\n    \"#0033CC\",\n    \"#0033FF\",\n    \"#0066CC\",\n    \"#0066FF\",\n    \"#0099CC\",\n    \"#0099FF\",\n    \"#00CC00\",\n    \"#00CC33\",\n    \"#00CC66\",\n    \"#00CC99\",\n    \"#00CCCC\",\n    \"#00CCFF\",\n    \"#3300CC\",\n    \"#3300FF\",\n    \"#3333CC\",\n    \"#3333FF\",\n    \"#3366CC\",\n    \"#3366FF\",\n    \"#3399CC\",\n    \"#3399FF\",\n    \"#33CC00\",\n    \"#33CC33\",\n    \"#33CC66\",\n    \"#33CC99\",\n    \"#33CCCC\",\n    \"#33CCFF\",\n    \"#6600CC\",\n    \"#6600FF\",\n    \"#6633CC\",\n    \"#6633FF\",\n    \"#66CC00\",\n    \"#66CC33\",\n    \"#9900CC\",\n    \"#9900FF\",\n    \"#9933CC\",\n    \"#9933FF\",\n    \"#99CC00\",\n    \"#99CC33\",\n    \"#CC0000\",\n    \"#CC0033\",\n    \"#CC0066\",\n    \"#CC0099\",\n    \"#CC00CC\",\n    \"#CC00FF\",\n    \"#CC3300\",\n    \"#CC3333\",\n    \"#CC3366\",\n    \"#CC3399\",\n    \"#CC33CC\",\n    \"#CC33FF\",\n    \"#CC6600\",\n    \"#CC6633\",\n    \"#CC9900\",\n    \"#CC9933\",\n    \"#CCCC00\",\n    \"#CCCC33\",\n    \"#FF0000\",\n    \"#FF0033\",\n    \"#FF0066\",\n    \"#FF0099\",\n    \"#FF00CC\",\n    \"#FF00FF\",\n    \"#FF3300\",\n    \"#FF3333\",\n    \"#FF3366\",\n    \"#FF3399\",\n    \"#FF33CC\",\n    \"#FF33FF\",\n    \"#FF6600\",\n    \"#FF6633\",\n    \"#FF9900\",\n    \"#FF9933\",\n    \"#FFCC00\",\n    \"#FFCC33\"\n];\n/**\n * Currently only WebKit-based Web Inspectors, Firefox >= v31,\n * and the Firebug extension (any Firefox version) are known\n * to support \"%c\" CSS customizations.\n *\n * TODO: add a `localStorage` variable to explicitly enable/disable colors\n */ // eslint-disable-next-line complexity\nfunction useColors() {\n    // NB: In an Electron preload script, document will be defined but not fully\n    // initialized. Since we know we're in Chrome, we'll just detect this case\n    // explicitly\n    if (false) {}\n    // Internet Explorer and Edge do not support colors.\n    if (typeof navigator !== \"undefined\" && navigator.userAgent && navigator.userAgent.toLowerCase().match(/(edge|trident)\\/(\\d+)/)) {\n        return false;\n    }\n    let m;\n    // Is webkit? http://stackoverflow.com/a/16459606/376773\n    // document is undefined in react-native: https://github.com/facebook/react-native/pull/1632\n    return typeof document !== \"undefined\" && document.documentElement && document.documentElement.style && document.documentElement.style.WebkitAppearance || // Is firebug? http://stackoverflow.com/a/398120/376773\n     false && (0) || // Is firefox >= v31?\n    // https://developer.mozilla.org/en-US/docs/Tools/Web_Console#Styling_messages\n    typeof navigator !== \"undefined\" && navigator.userAgent && (m = navigator.userAgent.toLowerCase().match(/firefox\\/(\\d+)/)) && parseInt(m[1], 10) >= 31 || // Double check webkit in userAgent just in case we are in a worker\n    typeof navigator !== \"undefined\" && navigator.userAgent && navigator.userAgent.toLowerCase().match(/applewebkit\\/(\\d+)/);\n}\n/**\n * Colorize log arguments if enabled.\n *\n * @api public\n */ function formatArgs(args) {\n    args[0] = (this.useColors ? \"%c\" : \"\") + this.namespace + (this.useColors ? \" %c\" : \" \") + args[0] + (this.useColors ? \"%c \" : \" \") + \"+\" + module.exports.humanize(this.diff);\n    if (!this.useColors) {\n        return;\n    }\n    const c = \"color: \" + this.color;\n    args.splice(1, 0, c, \"color: inherit\");\n    // The final \"%c\" is somewhat tricky, because there could be other\n    // arguments passed either before or after the %c, so we need to\n    // figure out the correct index to insert the CSS into\n    let index = 0;\n    let lastC = 0;\n    args[0].replace(/%[a-zA-Z%]/g, (match)=>{\n        if (match === \"%%\") {\n            return;\n        }\n        index++;\n        if (match === \"%c\") {\n            // We only are interested in the *last* %c\n            // (the user may have provided their own)\n            lastC = index;\n        }\n    });\n    args.splice(lastC, 0, c);\n}\n/**\n * Invokes `console.debug()` when available.\n * No-op when `console.debug` is not a \"function\".\n * If `console.debug` is not available, falls back\n * to `console.log`.\n *\n * @api public\n */ exports.log = console.debug || console.log || (()=>{});\n/**\n * Save `namespaces`.\n *\n * @param {String} namespaces\n * @api private\n */ function save(namespaces) {\n    try {\n        if (namespaces) {\n            exports.storage.setItem(\"debug\", namespaces);\n        } else {\n            exports.storage.removeItem(\"debug\");\n        }\n    } catch (error) {\n    // Swallow\n    // XXX (@Qix-) should we be logging these?\n    }\n}\n/**\n * Load `namespaces`.\n *\n * @return {String} returns the previously persisted debug modes\n * @api private\n */ function load() {\n    let r;\n    try {\n        r = exports.storage.getItem(\"debug\");\n    } catch (error) {\n    // Swallow\n    // XXX (@Qix-) should we be logging these?\n    }\n    // If debug isn't set in LS, and we're in Electron, try to load $DEBUG\n    if (!r && typeof process !== \"undefined\" && \"env\" in process) {\n        r = process.env.DEBUG;\n    }\n    return r;\n}\n/**\n * Localstorage attempts to return the localstorage.\n *\n * This is necessary because safari throws\n * when a user disables cookies/localstorage\n * and you attempt to access it.\n *\n * @return {LocalStorage}\n * @api private\n */ function localstorage() {\n    try {\n        // TVMLKit (Apple TV JS Runtime) does not have a window object, just localStorage in the global context\n        // The Browser also has localStorage in the global context.\n        return localStorage;\n    } catch (error) {\n    // Swallow\n    // XXX (@Qix-) should we be logging these?\n    }\n}\nmodule.exports = __webpack_require__(/*! ./common */ \"(ssr)/./node_modules/socket.io-client/node_modules/debug/src/common.js\")(exports);\nconst { formatters } = module.exports;\n/**\n * Map %j to `JSON.stringify()`, since no Web Inspectors do that by default.\n */ formatters.j = function(v) {\n    try {\n        return JSON.stringify(v);\n    } catch (error) {\n        return \"[UnexpectedJSONParseError]: \" + error.message;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/socket.io-client/node_modules/debug/src/browser.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/socket.io-client/node_modules/debug/src/common.js":
/*!************************************************************************!*\
  !*** ./node_modules/socket.io-client/node_modules/debug/src/common.js ***!
  \************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/**\n * This is the common logic for both the Node.js and web browser\n * implementations of `debug()`.\n */ function setup(env) {\n    createDebug.debug = createDebug;\n    createDebug.default = createDebug;\n    createDebug.coerce = coerce;\n    createDebug.disable = disable;\n    createDebug.enable = enable;\n    createDebug.enabled = enabled;\n    createDebug.humanize = __webpack_require__(/*! ms */ \"(ssr)/./node_modules/ms/index.js\");\n    createDebug.destroy = destroy;\n    Object.keys(env).forEach((key)=>{\n        createDebug[key] = env[key];\n    });\n    /**\n\t* The currently active debug mode names, and names to skip.\n\t*/ createDebug.names = [];\n    createDebug.skips = [];\n    /**\n\t* Map of special \"%n\" handling functions, for the debug \"format\" argument.\n\t*\n\t* Valid key names are a single, lower or upper-case letter, i.e. \"n\" and \"N\".\n\t*/ createDebug.formatters = {};\n    /**\n\t* Selects a color for a debug namespace\n\t* @param {String} namespace The namespace string for the debug instance to be colored\n\t* @return {Number|String} An ANSI color code for the given namespace\n\t* @api private\n\t*/ function selectColor(namespace) {\n        let hash = 0;\n        for(let i = 0; i < namespace.length; i++){\n            hash = (hash << 5) - hash + namespace.charCodeAt(i);\n            hash |= 0; // Convert to 32bit integer\n        }\n        return createDebug.colors[Math.abs(hash) % createDebug.colors.length];\n    }\n    createDebug.selectColor = selectColor;\n    /**\n\t* Create a debugger with the given `namespace`.\n\t*\n\t* @param {String} namespace\n\t* @return {Function}\n\t* @api public\n\t*/ function createDebug(namespace) {\n        let prevTime;\n        let enableOverride = null;\n        let namespacesCache;\n        let enabledCache;\n        function debug(...args) {\n            // Disabled?\n            if (!debug.enabled) {\n                return;\n            }\n            const self = debug;\n            // Set `diff` timestamp\n            const curr = Number(new Date());\n            const ms = curr - (prevTime || curr);\n            self.diff = ms;\n            self.prev = prevTime;\n            self.curr = curr;\n            prevTime = curr;\n            args[0] = createDebug.coerce(args[0]);\n            if (typeof args[0] !== \"string\") {\n                // Anything else let's inspect with %O\n                args.unshift(\"%O\");\n            }\n            // Apply any `formatters` transformations\n            let index = 0;\n            args[0] = args[0].replace(/%([a-zA-Z%])/g, (match, format)=>{\n                // If we encounter an escaped % then don't increase the array index\n                if (match === \"%%\") {\n                    return \"%\";\n                }\n                index++;\n                const formatter = createDebug.formatters[format];\n                if (typeof formatter === \"function\") {\n                    const val = args[index];\n                    match = formatter.call(self, val);\n                    // Now we need to remove `args[index]` since it's inlined in the `format`\n                    args.splice(index, 1);\n                    index--;\n                }\n                return match;\n            });\n            // Apply env-specific formatting (colors, etc.)\n            createDebug.formatArgs.call(self, args);\n            const logFn = self.log || createDebug.log;\n            logFn.apply(self, args);\n        }\n        debug.namespace = namespace;\n        debug.useColors = createDebug.useColors();\n        debug.color = createDebug.selectColor(namespace);\n        debug.extend = extend;\n        debug.destroy = createDebug.destroy; // XXX Temporary. Will be removed in the next major release.\n        Object.defineProperty(debug, \"enabled\", {\n            enumerable: true,\n            configurable: false,\n            get: ()=>{\n                if (enableOverride !== null) {\n                    return enableOverride;\n                }\n                if (namespacesCache !== createDebug.namespaces) {\n                    namespacesCache = createDebug.namespaces;\n                    enabledCache = createDebug.enabled(namespace);\n                }\n                return enabledCache;\n            },\n            set: (v)=>{\n                enableOverride = v;\n            }\n        });\n        // Env-specific initialization logic for debug instances\n        if (typeof createDebug.init === \"function\") {\n            createDebug.init(debug);\n        }\n        return debug;\n    }\n    function extend(namespace, delimiter) {\n        const newDebug = createDebug(this.namespace + (typeof delimiter === \"undefined\" ? \":\" : delimiter) + namespace);\n        newDebug.log = this.log;\n        return newDebug;\n    }\n    /**\n\t* Enables a debug mode by namespaces. This can include modes\n\t* separated by a colon and wildcards.\n\t*\n\t* @param {String} namespaces\n\t* @api public\n\t*/ function enable(namespaces) {\n        createDebug.save(namespaces);\n        createDebug.namespaces = namespaces;\n        createDebug.names = [];\n        createDebug.skips = [];\n        let i;\n        const split = (typeof namespaces === \"string\" ? namespaces : \"\").split(/[\\s,]+/);\n        const len = split.length;\n        for(i = 0; i < len; i++){\n            if (!split[i]) {\n                continue;\n            }\n            namespaces = split[i].replace(/\\*/g, \".*?\");\n            if (namespaces[0] === \"-\") {\n                createDebug.skips.push(new RegExp(\"^\" + namespaces.slice(1) + \"$\"));\n            } else {\n                createDebug.names.push(new RegExp(\"^\" + namespaces + \"$\"));\n            }\n        }\n    }\n    /**\n\t* Disable debug output.\n\t*\n\t* @return {String} namespaces\n\t* @api public\n\t*/ function disable() {\n        const namespaces = [\n            ...createDebug.names.map(toNamespace),\n            ...createDebug.skips.map(toNamespace).map((namespace)=>\"-\" + namespace)\n        ].join(\",\");\n        createDebug.enable(\"\");\n        return namespaces;\n    }\n    /**\n\t* Returns true if the given mode name is enabled, false otherwise.\n\t*\n\t* @param {String} name\n\t* @return {Boolean}\n\t* @api public\n\t*/ function enabled(name) {\n        if (name[name.length - 1] === \"*\") {\n            return true;\n        }\n        let i;\n        let len;\n        for(i = 0, len = createDebug.skips.length; i < len; i++){\n            if (createDebug.skips[i].test(name)) {\n                return false;\n            }\n        }\n        for(i = 0, len = createDebug.names.length; i < len; i++){\n            if (createDebug.names[i].test(name)) {\n                return true;\n            }\n        }\n        return false;\n    }\n    /**\n\t* Convert regexp to namespace\n\t*\n\t* @param {RegExp} regxep\n\t* @return {String} namespace\n\t* @api private\n\t*/ function toNamespace(regexp) {\n        return regexp.toString().substring(2, regexp.toString().length - 2).replace(/\\.\\*\\?$/, \"*\");\n    }\n    /**\n\t* Coerce `val`.\n\t*\n\t* @param {Mixed} val\n\t* @return {Mixed}\n\t* @api private\n\t*/ function coerce(val) {\n        if (val instanceof Error) {\n            return val.stack || val.message;\n        }\n        return val;\n    }\n    /**\n\t* XXX DO NOT USE. This is a temporary stub function.\n\t* XXX It WILL be removed in the next major release.\n\t*/ function destroy() {\n        console.warn(\"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.\");\n    }\n    createDebug.enable(createDebug.load());\n    return createDebug;\n}\nmodule.exports = setup;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvc29ja2V0LmlvLWNsaWVudC9ub2RlX21vZHVsZXMvZGVidWcvc3JjL2NvbW1vbi5qcyIsIm1hcHBpbmdzIjoiQUFDQTs7O0NBR0MsR0FFRCxTQUFTQSxNQUFNQyxHQUFHO0lBQ2pCQyxZQUFZQyxLQUFLLEdBQUdEO0lBQ3BCQSxZQUFZRSxPQUFPLEdBQUdGO0lBQ3RCQSxZQUFZRyxNQUFNLEdBQUdBO0lBQ3JCSCxZQUFZSSxPQUFPLEdBQUdBO0lBQ3RCSixZQUFZSyxNQUFNLEdBQUdBO0lBQ3JCTCxZQUFZTSxPQUFPLEdBQUdBO0lBQ3RCTixZQUFZTyxRQUFRLEdBQUdDLG1CQUFPQSxDQUFDO0lBQy9CUixZQUFZUyxPQUFPLEdBQUdBO0lBRXRCQyxPQUFPQyxJQUFJLENBQUNaLEtBQUthLE9BQU8sQ0FBQ0MsQ0FBQUE7UUFDeEJiLFdBQVcsQ0FBQ2EsSUFBSSxHQUFHZCxHQUFHLENBQUNjLElBQUk7SUFDNUI7SUFFQTs7Q0FFQSxHQUVBYixZQUFZYyxLQUFLLEdBQUcsRUFBRTtJQUN0QmQsWUFBWWUsS0FBSyxHQUFHLEVBQUU7SUFFdEI7Ozs7Q0FJQSxHQUNBZixZQUFZZ0IsVUFBVSxHQUFHLENBQUM7SUFFMUI7Ozs7O0NBS0EsR0FDQSxTQUFTQyxZQUFZQyxTQUFTO1FBQzdCLElBQUlDLE9BQU87UUFFWCxJQUFLLElBQUlDLElBQUksR0FBR0EsSUFBSUYsVUFBVUcsTUFBTSxFQUFFRCxJQUFLO1lBQzFDRCxPQUFPLENBQUVBLFFBQVEsS0FBS0EsT0FBUUQsVUFBVUksVUFBVSxDQUFDRjtZQUNuREQsUUFBUSxHQUFHLDJCQUEyQjtRQUN2QztRQUVBLE9BQU9uQixZQUFZdUIsTUFBTSxDQUFDQyxLQUFLQyxHQUFHLENBQUNOLFFBQVFuQixZQUFZdUIsTUFBTSxDQUFDRixNQUFNLENBQUM7SUFDdEU7SUFDQXJCLFlBQVlpQixXQUFXLEdBQUdBO0lBRTFCOzs7Ozs7Q0FNQSxHQUNBLFNBQVNqQixZQUFZa0IsU0FBUztRQUM3QixJQUFJUTtRQUNKLElBQUlDLGlCQUFpQjtRQUNyQixJQUFJQztRQUNKLElBQUlDO1FBRUosU0FBUzVCLE1BQU0sR0FBRzZCLElBQUk7WUFDckIsWUFBWTtZQUNaLElBQUksQ0FBQzdCLE1BQU1LLE9BQU8sRUFBRTtnQkFDbkI7WUFDRDtZQUVBLE1BQU15QixPQUFPOUI7WUFFYix1QkFBdUI7WUFDdkIsTUFBTStCLE9BQU9DLE9BQU8sSUFBSUM7WUFDeEIsTUFBTUMsS0FBS0gsT0FBUU4sQ0FBQUEsWUFBWU0sSUFBRztZQUNsQ0QsS0FBS0ssSUFBSSxHQUFHRDtZQUNaSixLQUFLTSxJQUFJLEdBQUdYO1lBQ1pLLEtBQUtDLElBQUksR0FBR0E7WUFDWk4sV0FBV007WUFFWEYsSUFBSSxDQUFDLEVBQUUsR0FBRzlCLFlBQVlHLE1BQU0sQ0FBQzJCLElBQUksQ0FBQyxFQUFFO1lBRXBDLElBQUksT0FBT0EsSUFBSSxDQUFDLEVBQUUsS0FBSyxVQUFVO2dCQUNoQyxzQ0FBc0M7Z0JBQ3RDQSxLQUFLUSxPQUFPLENBQUM7WUFDZDtZQUVBLHlDQUF5QztZQUN6QyxJQUFJQyxRQUFRO1lBQ1pULElBQUksQ0FBQyxFQUFFLEdBQUdBLElBQUksQ0FBQyxFQUFFLENBQUNVLE9BQU8sQ0FBQyxpQkFBaUIsQ0FBQ0MsT0FBT0M7Z0JBQ2xELG1FQUFtRTtnQkFDbkUsSUFBSUQsVUFBVSxNQUFNO29CQUNuQixPQUFPO2dCQUNSO2dCQUNBRjtnQkFDQSxNQUFNSSxZQUFZM0MsWUFBWWdCLFVBQVUsQ0FBQzBCLE9BQU87Z0JBQ2hELElBQUksT0FBT0MsY0FBYyxZQUFZO29CQUNwQyxNQUFNQyxNQUFNZCxJQUFJLENBQUNTLE1BQU07b0JBQ3ZCRSxRQUFRRSxVQUFVRSxJQUFJLENBQUNkLE1BQU1hO29CQUU3Qix5RUFBeUU7b0JBQ3pFZCxLQUFLZ0IsTUFBTSxDQUFDUCxPQUFPO29CQUNuQkE7Z0JBQ0Q7Z0JBQ0EsT0FBT0U7WUFDUjtZQUVBLCtDQUErQztZQUMvQ3pDLFlBQVkrQyxVQUFVLENBQUNGLElBQUksQ0FBQ2QsTUFBTUQ7WUFFbEMsTUFBTWtCLFFBQVFqQixLQUFLa0IsR0FBRyxJQUFJakQsWUFBWWlELEdBQUc7WUFDekNELE1BQU1FLEtBQUssQ0FBQ25CLE1BQU1EO1FBQ25CO1FBRUE3QixNQUFNaUIsU0FBUyxHQUFHQTtRQUNsQmpCLE1BQU1rRCxTQUFTLEdBQUduRCxZQUFZbUQsU0FBUztRQUN2Q2xELE1BQU1tRCxLQUFLLEdBQUdwRCxZQUFZaUIsV0FBVyxDQUFDQztRQUN0Q2pCLE1BQU1vRCxNQUFNLEdBQUdBO1FBQ2ZwRCxNQUFNUSxPQUFPLEdBQUdULFlBQVlTLE9BQU8sRUFBRSw0REFBNEQ7UUFFakdDLE9BQU80QyxjQUFjLENBQUNyRCxPQUFPLFdBQVc7WUFDdkNzRCxZQUFZO1lBQ1pDLGNBQWM7WUFDZEMsS0FBSztnQkFDSixJQUFJOUIsbUJBQW1CLE1BQU07b0JBQzVCLE9BQU9BO2dCQUNSO2dCQUNBLElBQUlDLG9CQUFvQjVCLFlBQVkwRCxVQUFVLEVBQUU7b0JBQy9DOUIsa0JBQWtCNUIsWUFBWTBELFVBQVU7b0JBQ3hDN0IsZUFBZTdCLFlBQVlNLE9BQU8sQ0FBQ1k7Z0JBQ3BDO2dCQUVBLE9BQU9XO1lBQ1I7WUFDQThCLEtBQUtDLENBQUFBO2dCQUNKakMsaUJBQWlCaUM7WUFDbEI7UUFDRDtRQUVBLHdEQUF3RDtRQUN4RCxJQUFJLE9BQU81RCxZQUFZNkQsSUFBSSxLQUFLLFlBQVk7WUFDM0M3RCxZQUFZNkQsSUFBSSxDQUFDNUQ7UUFDbEI7UUFFQSxPQUFPQTtJQUNSO0lBRUEsU0FBU29ELE9BQU9uQyxTQUFTLEVBQUU0QyxTQUFTO1FBQ25DLE1BQU1DLFdBQVcvRCxZQUFZLElBQUksQ0FBQ2tCLFNBQVMsR0FBSSxRQUFPNEMsY0FBYyxjQUFjLE1BQU1BLFNBQVEsSUFBSzVDO1FBQ3JHNkMsU0FBU2QsR0FBRyxHQUFHLElBQUksQ0FBQ0EsR0FBRztRQUN2QixPQUFPYztJQUNSO0lBRUE7Ozs7OztDQU1BLEdBQ0EsU0FBUzFELE9BQU9xRCxVQUFVO1FBQ3pCMUQsWUFBWWdFLElBQUksQ0FBQ047UUFDakIxRCxZQUFZMEQsVUFBVSxHQUFHQTtRQUV6QjFELFlBQVljLEtBQUssR0FBRyxFQUFFO1FBQ3RCZCxZQUFZZSxLQUFLLEdBQUcsRUFBRTtRQUV0QixJQUFJSztRQUNKLE1BQU02QyxRQUFRLENBQUMsT0FBT1AsZUFBZSxXQUFXQSxhQUFhLEVBQUMsRUFBR08sS0FBSyxDQUFDO1FBQ3ZFLE1BQU1DLE1BQU1ELE1BQU01QyxNQUFNO1FBRXhCLElBQUtELElBQUksR0FBR0EsSUFBSThDLEtBQUs5QyxJQUFLO1lBQ3pCLElBQUksQ0FBQzZDLEtBQUssQ0FBQzdDLEVBQUUsRUFBRTtnQkFFZDtZQUNEO1lBRUFzQyxhQUFhTyxLQUFLLENBQUM3QyxFQUFFLENBQUNvQixPQUFPLENBQUMsT0FBTztZQUVyQyxJQUFJa0IsVUFBVSxDQUFDLEVBQUUsS0FBSyxLQUFLO2dCQUMxQjFELFlBQVllLEtBQUssQ0FBQ29ELElBQUksQ0FBQyxJQUFJQyxPQUFPLE1BQU1WLFdBQVdXLEtBQUssQ0FBQyxLQUFLO1lBQy9ELE9BQU87Z0JBQ05yRSxZQUFZYyxLQUFLLENBQUNxRCxJQUFJLENBQUMsSUFBSUMsT0FBTyxNQUFNVixhQUFhO1lBQ3REO1FBQ0Q7SUFDRDtJQUVBOzs7OztDQUtBLEdBQ0EsU0FBU3REO1FBQ1IsTUFBTXNELGFBQWE7ZUFDZjFELFlBQVljLEtBQUssQ0FBQ3dELEdBQUcsQ0FBQ0M7ZUFDdEJ2RSxZQUFZZSxLQUFLLENBQUN1RCxHQUFHLENBQUNDLGFBQWFELEdBQUcsQ0FBQ3BELENBQUFBLFlBQWEsTUFBTUE7U0FDN0QsQ0FBQ3NELElBQUksQ0FBQztRQUNQeEUsWUFBWUssTUFBTSxDQUFDO1FBQ25CLE9BQU9xRDtJQUNSO0lBRUE7Ozs7OztDQU1BLEdBQ0EsU0FBU3BELFFBQVFtRSxJQUFJO1FBQ3BCLElBQUlBLElBQUksQ0FBQ0EsS0FBS3BELE1BQU0sR0FBRyxFQUFFLEtBQUssS0FBSztZQUNsQyxPQUFPO1FBQ1I7UUFFQSxJQUFJRDtRQUNKLElBQUk4QztRQUVKLElBQUs5QyxJQUFJLEdBQUc4QyxNQUFNbEUsWUFBWWUsS0FBSyxDQUFDTSxNQUFNLEVBQUVELElBQUk4QyxLQUFLOUMsSUFBSztZQUN6RCxJQUFJcEIsWUFBWWUsS0FBSyxDQUFDSyxFQUFFLENBQUNzRCxJQUFJLENBQUNELE9BQU87Z0JBQ3BDLE9BQU87WUFDUjtRQUNEO1FBRUEsSUFBS3JELElBQUksR0FBRzhDLE1BQU1sRSxZQUFZYyxLQUFLLENBQUNPLE1BQU0sRUFBRUQsSUFBSThDLEtBQUs5QyxJQUFLO1lBQ3pELElBQUlwQixZQUFZYyxLQUFLLENBQUNNLEVBQUUsQ0FBQ3NELElBQUksQ0FBQ0QsT0FBTztnQkFDcEMsT0FBTztZQUNSO1FBQ0Q7UUFFQSxPQUFPO0lBQ1I7SUFFQTs7Ozs7O0NBTUEsR0FDQSxTQUFTRixZQUFZSSxNQUFNO1FBQzFCLE9BQU9BLE9BQU9DLFFBQVEsR0FDcEJDLFNBQVMsQ0FBQyxHQUFHRixPQUFPQyxRQUFRLEdBQUd2RCxNQUFNLEdBQUcsR0FDeENtQixPQUFPLENBQUMsV0FBVztJQUN0QjtJQUVBOzs7Ozs7Q0FNQSxHQUNBLFNBQVNyQyxPQUFPeUMsR0FBRztRQUNsQixJQUFJQSxlQUFla0MsT0FBTztZQUN6QixPQUFPbEMsSUFBSW1DLEtBQUssSUFBSW5DLElBQUlvQyxPQUFPO1FBQ2hDO1FBQ0EsT0FBT3BDO0lBQ1I7SUFFQTs7O0NBR0EsR0FDQSxTQUFTbkM7UUFDUndFLFFBQVFDLElBQUksQ0FBQztJQUNkO0lBRUFsRixZQUFZSyxNQUFNLENBQUNMLFlBQVltRixJQUFJO0lBRW5DLE9BQU9uRjtBQUNSO0FBRUFvRixPQUFPQyxPQUFPLEdBQUd2RiIsInNvdXJjZXMiOlsid2VicGFjazovL3N1cGVybWFya2V0LWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL3NvY2tldC5pby1jbGllbnQvbm9kZV9tb2R1bGVzL2RlYnVnL3NyYy9jb21tb24uanM/YTExOCJdLCJzb3VyY2VzQ29udGVudCI6WyJcbi8qKlxuICogVGhpcyBpcyB0aGUgY29tbW9uIGxvZ2ljIGZvciBib3RoIHRoZSBOb2RlLmpzIGFuZCB3ZWIgYnJvd3NlclxuICogaW1wbGVtZW50YXRpb25zIG9mIGBkZWJ1ZygpYC5cbiAqL1xuXG5mdW5jdGlvbiBzZXR1cChlbnYpIHtcblx0Y3JlYXRlRGVidWcuZGVidWcgPSBjcmVhdGVEZWJ1Zztcblx0Y3JlYXRlRGVidWcuZGVmYXVsdCA9IGNyZWF0ZURlYnVnO1xuXHRjcmVhdGVEZWJ1Zy5jb2VyY2UgPSBjb2VyY2U7XG5cdGNyZWF0ZURlYnVnLmRpc2FibGUgPSBkaXNhYmxlO1xuXHRjcmVhdGVEZWJ1Zy5lbmFibGUgPSBlbmFibGU7XG5cdGNyZWF0ZURlYnVnLmVuYWJsZWQgPSBlbmFibGVkO1xuXHRjcmVhdGVEZWJ1Zy5odW1hbml6ZSA9IHJlcXVpcmUoJ21zJyk7XG5cdGNyZWF0ZURlYnVnLmRlc3Ryb3kgPSBkZXN0cm95O1xuXG5cdE9iamVjdC5rZXlzKGVudikuZm9yRWFjaChrZXkgPT4ge1xuXHRcdGNyZWF0ZURlYnVnW2tleV0gPSBlbnZba2V5XTtcblx0fSk7XG5cblx0LyoqXG5cdCogVGhlIGN1cnJlbnRseSBhY3RpdmUgZGVidWcgbW9kZSBuYW1lcywgYW5kIG5hbWVzIHRvIHNraXAuXG5cdCovXG5cblx0Y3JlYXRlRGVidWcubmFtZXMgPSBbXTtcblx0Y3JlYXRlRGVidWcuc2tpcHMgPSBbXTtcblxuXHQvKipcblx0KiBNYXAgb2Ygc3BlY2lhbCBcIiVuXCIgaGFuZGxpbmcgZnVuY3Rpb25zLCBmb3IgdGhlIGRlYnVnIFwiZm9ybWF0XCIgYXJndW1lbnQuXG5cdCpcblx0KiBWYWxpZCBrZXkgbmFtZXMgYXJlIGEgc2luZ2xlLCBsb3dlciBvciB1cHBlci1jYXNlIGxldHRlciwgaS5lLiBcIm5cIiBhbmQgXCJOXCIuXG5cdCovXG5cdGNyZWF0ZURlYnVnLmZvcm1hdHRlcnMgPSB7fTtcblxuXHQvKipcblx0KiBTZWxlY3RzIGEgY29sb3IgZm9yIGEgZGVidWcgbmFtZXNwYWNlXG5cdCogQHBhcmFtIHtTdHJpbmd9IG5hbWVzcGFjZSBUaGUgbmFtZXNwYWNlIHN0cmluZyBmb3IgdGhlIGRlYnVnIGluc3RhbmNlIHRvIGJlIGNvbG9yZWRcblx0KiBAcmV0dXJuIHtOdW1iZXJ8U3RyaW5nfSBBbiBBTlNJIGNvbG9yIGNvZGUgZm9yIHRoZSBnaXZlbiBuYW1lc3BhY2Vcblx0KiBAYXBpIHByaXZhdGVcblx0Ki9cblx0ZnVuY3Rpb24gc2VsZWN0Q29sb3IobmFtZXNwYWNlKSB7XG5cdFx0bGV0IGhhc2ggPSAwO1xuXG5cdFx0Zm9yIChsZXQgaSA9IDA7IGkgPCBuYW1lc3BhY2UubGVuZ3RoOyBpKyspIHtcblx0XHRcdGhhc2ggPSAoKGhhc2ggPDwgNSkgLSBoYXNoKSArIG5hbWVzcGFjZS5jaGFyQ29kZUF0KGkpO1xuXHRcdFx0aGFzaCB8PSAwOyAvLyBDb252ZXJ0IHRvIDMyYml0IGludGVnZXJcblx0XHR9XG5cblx0XHRyZXR1cm4gY3JlYXRlRGVidWcuY29sb3JzW01hdGguYWJzKGhhc2gpICUgY3JlYXRlRGVidWcuY29sb3JzLmxlbmd0aF07XG5cdH1cblx0Y3JlYXRlRGVidWcuc2VsZWN0Q29sb3IgPSBzZWxlY3RDb2xvcjtcblxuXHQvKipcblx0KiBDcmVhdGUgYSBkZWJ1Z2dlciB3aXRoIHRoZSBnaXZlbiBgbmFtZXNwYWNlYC5cblx0KlxuXHQqIEBwYXJhbSB7U3RyaW5nfSBuYW1lc3BhY2Vcblx0KiBAcmV0dXJuIHtGdW5jdGlvbn1cblx0KiBAYXBpIHB1YmxpY1xuXHQqL1xuXHRmdW5jdGlvbiBjcmVhdGVEZWJ1ZyhuYW1lc3BhY2UpIHtcblx0XHRsZXQgcHJldlRpbWU7XG5cdFx0bGV0IGVuYWJsZU92ZXJyaWRlID0gbnVsbDtcblx0XHRsZXQgbmFtZXNwYWNlc0NhY2hlO1xuXHRcdGxldCBlbmFibGVkQ2FjaGU7XG5cblx0XHRmdW5jdGlvbiBkZWJ1ZyguLi5hcmdzKSB7XG5cdFx0XHQvLyBEaXNhYmxlZD9cblx0XHRcdGlmICghZGVidWcuZW5hYmxlZCkge1xuXHRcdFx0XHRyZXR1cm47XG5cdFx0XHR9XG5cblx0XHRcdGNvbnN0IHNlbGYgPSBkZWJ1ZztcblxuXHRcdFx0Ly8gU2V0IGBkaWZmYCB0aW1lc3RhbXBcblx0XHRcdGNvbnN0IGN1cnIgPSBOdW1iZXIobmV3IERhdGUoKSk7XG5cdFx0XHRjb25zdCBtcyA9IGN1cnIgLSAocHJldlRpbWUgfHwgY3Vycik7XG5cdFx0XHRzZWxmLmRpZmYgPSBtcztcblx0XHRcdHNlbGYucHJldiA9IHByZXZUaW1lO1xuXHRcdFx0c2VsZi5jdXJyID0gY3Vycjtcblx0XHRcdHByZXZUaW1lID0gY3VycjtcblxuXHRcdFx0YXJnc1swXSA9IGNyZWF0ZURlYnVnLmNvZXJjZShhcmdzWzBdKTtcblxuXHRcdFx0aWYgKHR5cGVvZiBhcmdzWzBdICE9PSAnc3RyaW5nJykge1xuXHRcdFx0XHQvLyBBbnl0aGluZyBlbHNlIGxldCdzIGluc3BlY3Qgd2l0aCAlT1xuXHRcdFx0XHRhcmdzLnVuc2hpZnQoJyVPJyk7XG5cdFx0XHR9XG5cblx0XHRcdC8vIEFwcGx5IGFueSBgZm9ybWF0dGVyc2AgdHJhbnNmb3JtYXRpb25zXG5cdFx0XHRsZXQgaW5kZXggPSAwO1xuXHRcdFx0YXJnc1swXSA9IGFyZ3NbMF0ucmVwbGFjZSgvJShbYS16QS1aJV0pL2csIChtYXRjaCwgZm9ybWF0KSA9PiB7XG5cdFx0XHRcdC8vIElmIHdlIGVuY291bnRlciBhbiBlc2NhcGVkICUgdGhlbiBkb24ndCBpbmNyZWFzZSB0aGUgYXJyYXkgaW5kZXhcblx0XHRcdFx0aWYgKG1hdGNoID09PSAnJSUnKSB7XG5cdFx0XHRcdFx0cmV0dXJuICclJztcblx0XHRcdFx0fVxuXHRcdFx0XHRpbmRleCsrO1xuXHRcdFx0XHRjb25zdCBmb3JtYXR0ZXIgPSBjcmVhdGVEZWJ1Zy5mb3JtYXR0ZXJzW2Zvcm1hdF07XG5cdFx0XHRcdGlmICh0eXBlb2YgZm9ybWF0dGVyID09PSAnZnVuY3Rpb24nKSB7XG5cdFx0XHRcdFx0Y29uc3QgdmFsID0gYXJnc1tpbmRleF07XG5cdFx0XHRcdFx0bWF0Y2ggPSBmb3JtYXR0ZXIuY2FsbChzZWxmLCB2YWwpO1xuXG5cdFx0XHRcdFx0Ly8gTm93IHdlIG5lZWQgdG8gcmVtb3ZlIGBhcmdzW2luZGV4XWAgc2luY2UgaXQncyBpbmxpbmVkIGluIHRoZSBgZm9ybWF0YFxuXHRcdFx0XHRcdGFyZ3Muc3BsaWNlKGluZGV4LCAxKTtcblx0XHRcdFx0XHRpbmRleC0tO1xuXHRcdFx0XHR9XG5cdFx0XHRcdHJldHVybiBtYXRjaDtcblx0XHRcdH0pO1xuXG5cdFx0XHQvLyBBcHBseSBlbnYtc3BlY2lmaWMgZm9ybWF0dGluZyAoY29sb3JzLCBldGMuKVxuXHRcdFx0Y3JlYXRlRGVidWcuZm9ybWF0QXJncy5jYWxsKHNlbGYsIGFyZ3MpO1xuXG5cdFx0XHRjb25zdCBsb2dGbiA9IHNlbGYubG9nIHx8IGNyZWF0ZURlYnVnLmxvZztcblx0XHRcdGxvZ0ZuLmFwcGx5KHNlbGYsIGFyZ3MpO1xuXHRcdH1cblxuXHRcdGRlYnVnLm5hbWVzcGFjZSA9IG5hbWVzcGFjZTtcblx0XHRkZWJ1Zy51c2VDb2xvcnMgPSBjcmVhdGVEZWJ1Zy51c2VDb2xvcnMoKTtcblx0XHRkZWJ1Zy5jb2xvciA9IGNyZWF0ZURlYnVnLnNlbGVjdENvbG9yKG5hbWVzcGFjZSk7XG5cdFx0ZGVidWcuZXh0ZW5kID0gZXh0ZW5kO1xuXHRcdGRlYnVnLmRlc3Ryb3kgPSBjcmVhdGVEZWJ1Zy5kZXN0cm95OyAvLyBYWFggVGVtcG9yYXJ5LiBXaWxsIGJlIHJlbW92ZWQgaW4gdGhlIG5leHQgbWFqb3IgcmVsZWFzZS5cblxuXHRcdE9iamVjdC5kZWZpbmVQcm9wZXJ0eShkZWJ1ZywgJ2VuYWJsZWQnLCB7XG5cdFx0XHRlbnVtZXJhYmxlOiB0cnVlLFxuXHRcdFx0Y29uZmlndXJhYmxlOiBmYWxzZSxcblx0XHRcdGdldDogKCkgPT4ge1xuXHRcdFx0XHRpZiAoZW5hYmxlT3ZlcnJpZGUgIT09IG51bGwpIHtcblx0XHRcdFx0XHRyZXR1cm4gZW5hYmxlT3ZlcnJpZGU7XG5cdFx0XHRcdH1cblx0XHRcdFx0aWYgKG5hbWVzcGFjZXNDYWNoZSAhPT0gY3JlYXRlRGVidWcubmFtZXNwYWNlcykge1xuXHRcdFx0XHRcdG5hbWVzcGFjZXNDYWNoZSA9IGNyZWF0ZURlYnVnLm5hbWVzcGFjZXM7XG5cdFx0XHRcdFx0ZW5hYmxlZENhY2hlID0gY3JlYXRlRGVidWcuZW5hYmxlZChuYW1lc3BhY2UpO1xuXHRcdFx0XHR9XG5cblx0XHRcdFx0cmV0dXJuIGVuYWJsZWRDYWNoZTtcblx0XHRcdH0sXG5cdFx0XHRzZXQ6IHYgPT4ge1xuXHRcdFx0XHRlbmFibGVPdmVycmlkZSA9IHY7XG5cdFx0XHR9XG5cdFx0fSk7XG5cblx0XHQvLyBFbnYtc3BlY2lmaWMgaW5pdGlhbGl6YXRpb24gbG9naWMgZm9yIGRlYnVnIGluc3RhbmNlc1xuXHRcdGlmICh0eXBlb2YgY3JlYXRlRGVidWcuaW5pdCA9PT0gJ2Z1bmN0aW9uJykge1xuXHRcdFx0Y3JlYXRlRGVidWcuaW5pdChkZWJ1Zyk7XG5cdFx0fVxuXG5cdFx0cmV0dXJuIGRlYnVnO1xuXHR9XG5cblx0ZnVuY3Rpb24gZXh0ZW5kKG5hbWVzcGFjZSwgZGVsaW1pdGVyKSB7XG5cdFx0Y29uc3QgbmV3RGVidWcgPSBjcmVhdGVEZWJ1Zyh0aGlzLm5hbWVzcGFjZSArICh0eXBlb2YgZGVsaW1pdGVyID09PSAndW5kZWZpbmVkJyA/ICc6JyA6IGRlbGltaXRlcikgKyBuYW1lc3BhY2UpO1xuXHRcdG5ld0RlYnVnLmxvZyA9IHRoaXMubG9nO1xuXHRcdHJldHVybiBuZXdEZWJ1Zztcblx0fVxuXG5cdC8qKlxuXHQqIEVuYWJsZXMgYSBkZWJ1ZyBtb2RlIGJ5IG5hbWVzcGFjZXMuIFRoaXMgY2FuIGluY2x1ZGUgbW9kZXNcblx0KiBzZXBhcmF0ZWQgYnkgYSBjb2xvbiBhbmQgd2lsZGNhcmRzLlxuXHQqXG5cdCogQHBhcmFtIHtTdHJpbmd9IG5hbWVzcGFjZXNcblx0KiBAYXBpIHB1YmxpY1xuXHQqL1xuXHRmdW5jdGlvbiBlbmFibGUobmFtZXNwYWNlcykge1xuXHRcdGNyZWF0ZURlYnVnLnNhdmUobmFtZXNwYWNlcyk7XG5cdFx0Y3JlYXRlRGVidWcubmFtZXNwYWNlcyA9IG5hbWVzcGFjZXM7XG5cblx0XHRjcmVhdGVEZWJ1Zy5uYW1lcyA9IFtdO1xuXHRcdGNyZWF0ZURlYnVnLnNraXBzID0gW107XG5cblx0XHRsZXQgaTtcblx0XHRjb25zdCBzcGxpdCA9ICh0eXBlb2YgbmFtZXNwYWNlcyA9PT0gJ3N0cmluZycgPyBuYW1lc3BhY2VzIDogJycpLnNwbGl0KC9bXFxzLF0rLyk7XG5cdFx0Y29uc3QgbGVuID0gc3BsaXQubGVuZ3RoO1xuXG5cdFx0Zm9yIChpID0gMDsgaSA8IGxlbjsgaSsrKSB7XG5cdFx0XHRpZiAoIXNwbGl0W2ldKSB7XG5cdFx0XHRcdC8vIGlnbm9yZSBlbXB0eSBzdHJpbmdzXG5cdFx0XHRcdGNvbnRpbnVlO1xuXHRcdFx0fVxuXG5cdFx0XHRuYW1lc3BhY2VzID0gc3BsaXRbaV0ucmVwbGFjZSgvXFwqL2csICcuKj8nKTtcblxuXHRcdFx0aWYgKG5hbWVzcGFjZXNbMF0gPT09ICctJykge1xuXHRcdFx0XHRjcmVhdGVEZWJ1Zy5za2lwcy5wdXNoKG5ldyBSZWdFeHAoJ14nICsgbmFtZXNwYWNlcy5zbGljZSgxKSArICckJykpO1xuXHRcdFx0fSBlbHNlIHtcblx0XHRcdFx0Y3JlYXRlRGVidWcubmFtZXMucHVzaChuZXcgUmVnRXhwKCdeJyArIG5hbWVzcGFjZXMgKyAnJCcpKTtcblx0XHRcdH1cblx0XHR9XG5cdH1cblxuXHQvKipcblx0KiBEaXNhYmxlIGRlYnVnIG91dHB1dC5cblx0KlxuXHQqIEByZXR1cm4ge1N0cmluZ30gbmFtZXNwYWNlc1xuXHQqIEBhcGkgcHVibGljXG5cdCovXG5cdGZ1bmN0aW9uIGRpc2FibGUoKSB7XG5cdFx0Y29uc3QgbmFtZXNwYWNlcyA9IFtcblx0XHRcdC4uLmNyZWF0ZURlYnVnLm5hbWVzLm1hcCh0b05hbWVzcGFjZSksXG5cdFx0XHQuLi5jcmVhdGVEZWJ1Zy5za2lwcy5tYXAodG9OYW1lc3BhY2UpLm1hcChuYW1lc3BhY2UgPT4gJy0nICsgbmFtZXNwYWNlKVxuXHRcdF0uam9pbignLCcpO1xuXHRcdGNyZWF0ZURlYnVnLmVuYWJsZSgnJyk7XG5cdFx0cmV0dXJuIG5hbWVzcGFjZXM7XG5cdH1cblxuXHQvKipcblx0KiBSZXR1cm5zIHRydWUgaWYgdGhlIGdpdmVuIG1vZGUgbmFtZSBpcyBlbmFibGVkLCBmYWxzZSBvdGhlcndpc2UuXG5cdCpcblx0KiBAcGFyYW0ge1N0cmluZ30gbmFtZVxuXHQqIEByZXR1cm4ge0Jvb2xlYW59XG5cdCogQGFwaSBwdWJsaWNcblx0Ki9cblx0ZnVuY3Rpb24gZW5hYmxlZChuYW1lKSB7XG5cdFx0aWYgKG5hbWVbbmFtZS5sZW5ndGggLSAxXSA9PT0gJyonKSB7XG5cdFx0XHRyZXR1cm4gdHJ1ZTtcblx0XHR9XG5cblx0XHRsZXQgaTtcblx0XHRsZXQgbGVuO1xuXG5cdFx0Zm9yIChpID0gMCwgbGVuID0gY3JlYXRlRGVidWcuc2tpcHMubGVuZ3RoOyBpIDwgbGVuOyBpKyspIHtcblx0XHRcdGlmIChjcmVhdGVEZWJ1Zy5za2lwc1tpXS50ZXN0KG5hbWUpKSB7XG5cdFx0XHRcdHJldHVybiBmYWxzZTtcblx0XHRcdH1cblx0XHR9XG5cblx0XHRmb3IgKGkgPSAwLCBsZW4gPSBjcmVhdGVEZWJ1Zy5uYW1lcy5sZW5ndGg7IGkgPCBsZW47IGkrKykge1xuXHRcdFx0aWYgKGNyZWF0ZURlYnVnLm5hbWVzW2ldLnRlc3QobmFtZSkpIHtcblx0XHRcdFx0cmV0dXJuIHRydWU7XG5cdFx0XHR9XG5cdFx0fVxuXG5cdFx0cmV0dXJuIGZhbHNlO1xuXHR9XG5cblx0LyoqXG5cdCogQ29udmVydCByZWdleHAgdG8gbmFtZXNwYWNlXG5cdCpcblx0KiBAcGFyYW0ge1JlZ0V4cH0gcmVneGVwXG5cdCogQHJldHVybiB7U3RyaW5nfSBuYW1lc3BhY2Vcblx0KiBAYXBpIHByaXZhdGVcblx0Ki9cblx0ZnVuY3Rpb24gdG9OYW1lc3BhY2UocmVnZXhwKSB7XG5cdFx0cmV0dXJuIHJlZ2V4cC50b1N0cmluZygpXG5cdFx0XHQuc3Vic3RyaW5nKDIsIHJlZ2V4cC50b1N0cmluZygpLmxlbmd0aCAtIDIpXG5cdFx0XHQucmVwbGFjZSgvXFwuXFwqXFw/JC8sICcqJyk7XG5cdH1cblxuXHQvKipcblx0KiBDb2VyY2UgYHZhbGAuXG5cdCpcblx0KiBAcGFyYW0ge01peGVkfSB2YWxcblx0KiBAcmV0dXJuIHtNaXhlZH1cblx0KiBAYXBpIHByaXZhdGVcblx0Ki9cblx0ZnVuY3Rpb24gY29lcmNlKHZhbCkge1xuXHRcdGlmICh2YWwgaW5zdGFuY2VvZiBFcnJvcikge1xuXHRcdFx0cmV0dXJuIHZhbC5zdGFjayB8fCB2YWwubWVzc2FnZTtcblx0XHR9XG5cdFx0cmV0dXJuIHZhbDtcblx0fVxuXG5cdC8qKlxuXHQqIFhYWCBETyBOT1QgVVNFLiBUaGlzIGlzIGEgdGVtcG9yYXJ5IHN0dWIgZnVuY3Rpb24uXG5cdCogWFhYIEl0IFdJTEwgYmUgcmVtb3ZlZCBpbiB0aGUgbmV4dCBtYWpvciByZWxlYXNlLlxuXHQqL1xuXHRmdW5jdGlvbiBkZXN0cm95KCkge1xuXHRcdGNvbnNvbGUud2FybignSW5zdGFuY2UgbWV0aG9kIGBkZWJ1Zy5kZXN0cm95KClgIGlzIGRlcHJlY2F0ZWQgYW5kIG5vIGxvbmdlciBkb2VzIGFueXRoaW5nLiBJdCB3aWxsIGJlIHJlbW92ZWQgaW4gdGhlIG5leHQgbWFqb3IgdmVyc2lvbiBvZiBgZGVidWdgLicpO1xuXHR9XG5cblx0Y3JlYXRlRGVidWcuZW5hYmxlKGNyZWF0ZURlYnVnLmxvYWQoKSk7XG5cblx0cmV0dXJuIGNyZWF0ZURlYnVnO1xufVxuXG5tb2R1bGUuZXhwb3J0cyA9IHNldHVwO1xuIl0sIm5hbWVzIjpbInNldHVwIiwiZW52IiwiY3JlYXRlRGVidWciLCJkZWJ1ZyIsImRlZmF1bHQiLCJjb2VyY2UiLCJkaXNhYmxlIiwiZW5hYmxlIiwiZW5hYmxlZCIsImh1bWFuaXplIiwicmVxdWlyZSIsImRlc3Ryb3kiLCJPYmplY3QiLCJrZXlzIiwiZm9yRWFjaCIsImtleSIsIm5hbWVzIiwic2tpcHMiLCJmb3JtYXR0ZXJzIiwic2VsZWN0Q29sb3IiLCJuYW1lc3BhY2UiLCJoYXNoIiwiaSIsImxlbmd0aCIsImNoYXJDb2RlQXQiLCJjb2xvcnMiLCJNYXRoIiwiYWJzIiwicHJldlRpbWUiLCJlbmFibGVPdmVycmlkZSIsIm5hbWVzcGFjZXNDYWNoZSIsImVuYWJsZWRDYWNoZSIsImFyZ3MiLCJzZWxmIiwiY3VyciIsIk51bWJlciIsIkRhdGUiLCJtcyIsImRpZmYiLCJwcmV2IiwidW5zaGlmdCIsImluZGV4IiwicmVwbGFjZSIsIm1hdGNoIiwiZm9ybWF0IiwiZm9ybWF0dGVyIiwidmFsIiwiY2FsbCIsInNwbGljZSIsImZvcm1hdEFyZ3MiLCJsb2dGbiIsImxvZyIsImFwcGx5IiwidXNlQ29sb3JzIiwiY29sb3IiLCJleHRlbmQiLCJkZWZpbmVQcm9wZXJ0eSIsImVudW1lcmFibGUiLCJjb25maWd1cmFibGUiLCJnZXQiLCJuYW1lc3BhY2VzIiwic2V0IiwidiIsImluaXQiLCJkZWxpbWl0ZXIiLCJuZXdEZWJ1ZyIsInNhdmUiLCJzcGxpdCIsImxlbiIsInB1c2giLCJSZWdFeHAiLCJzbGljZSIsIm1hcCIsInRvTmFtZXNwYWNlIiwiam9pbiIsIm5hbWUiLCJ0ZXN0IiwicmVnZXhwIiwidG9TdHJpbmciLCJzdWJzdHJpbmciLCJFcnJvciIsInN0YWNrIiwibWVzc2FnZSIsImNvbnNvbGUiLCJ3YXJuIiwibG9hZCIsIm1vZHVsZSIsImV4cG9ydHMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/socket.io-client/node_modules/debug/src/common.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/socket.io-client/node_modules/debug/src/index.js":
/*!***********************************************************************!*\
  !*** ./node_modules/socket.io-client/node_modules/debug/src/index.js ***!
  \***********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/**\n * Detect Electron renderer / nwjs process, which is node, but we should\n * treat as a browser.\n */ if (typeof process === \"undefined\" || process.type === \"renderer\" || false === true || process.__nwjs) {\n    module.exports = __webpack_require__(/*! ./browser.js */ \"(ssr)/./node_modules/socket.io-client/node_modules/debug/src/browser.js\");\n} else {\n    module.exports = __webpack_require__(/*! ./node.js */ \"(ssr)/./node_modules/socket.io-client/node_modules/debug/src/node.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvc29ja2V0LmlvLWNsaWVudC9ub2RlX21vZHVsZXMvZGVidWcvc3JjL2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFBOzs7Q0FHQyxHQUVELElBQUksT0FBT0EsWUFBWSxlQUFlQSxRQUFRQyxJQUFJLEtBQUssY0FBY0QsS0FBZSxLQUFLLFFBQVFBLFFBQVFHLE1BQU0sRUFBRTtJQUNoSEMsbUlBQXlCO0FBQzFCLE9BQU87SUFDTkEsNkhBQXlCO0FBQzFCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3VwZXJtYXJrZXQtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvc29ja2V0LmlvLWNsaWVudC9ub2RlX21vZHVsZXMvZGVidWcvc3JjL2luZGV4LmpzPzA4MmYiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBEZXRlY3QgRWxlY3Ryb24gcmVuZGVyZXIgLyBud2pzIHByb2Nlc3MsIHdoaWNoIGlzIG5vZGUsIGJ1dCB3ZSBzaG91bGRcbiAqIHRyZWF0IGFzIGEgYnJvd3Nlci5cbiAqL1xuXG5pZiAodHlwZW9mIHByb2Nlc3MgPT09ICd1bmRlZmluZWQnIHx8IHByb2Nlc3MudHlwZSA9PT0gJ3JlbmRlcmVyJyB8fCBwcm9jZXNzLmJyb3dzZXIgPT09IHRydWUgfHwgcHJvY2Vzcy5fX253anMpIHtcblx0bW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Jyb3dzZXIuanMnKTtcbn0gZWxzZSB7XG5cdG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9ub2RlLmpzJyk7XG59XG4iXSwibmFtZXMiOlsicHJvY2VzcyIsInR5cGUiLCJicm93c2VyIiwiX19ud2pzIiwibW9kdWxlIiwiZXhwb3J0cyIsInJlcXVpcmUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/socket.io-client/node_modules/debug/src/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/socket.io-client/node_modules/debug/src/node.js":
/*!**********************************************************************!*\
  !*** ./node_modules/socket.io-client/node_modules/debug/src/node.js ***!
  \**********************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval("/**\n * Module dependencies.\n */ const tty = __webpack_require__(/*! tty */ \"tty\");\nconst util = __webpack_require__(/*! util */ \"util\");\n/**\n * This is the Node.js implementation of `debug()`.\n */ exports.init = init;\nexports.log = log;\nexports.formatArgs = formatArgs;\nexports.save = save;\nexports.load = load;\nexports.useColors = useColors;\nexports.destroy = util.deprecate(()=>{}, \"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.\");\n/**\n * Colors.\n */ exports.colors = [\n    6,\n    2,\n    3,\n    4,\n    5,\n    1\n];\ntry {\n    // Optional dependency (as in, doesn't need to be installed, NOT like optionalDependencies in package.json)\n    // eslint-disable-next-line import/no-extraneous-dependencies\n    const supportsColor = __webpack_require__(/*! supports-color */ \"(ssr)/./node_modules/supports-color/index.js\");\n    if (supportsColor && (supportsColor.stderr || supportsColor).level >= 2) {\n        exports.colors = [\n            20,\n            21,\n            26,\n            27,\n            32,\n            33,\n            38,\n            39,\n            40,\n            41,\n            42,\n            43,\n            44,\n            45,\n            56,\n            57,\n            62,\n            63,\n            68,\n            69,\n            74,\n            75,\n            76,\n            77,\n            78,\n            79,\n            80,\n            81,\n            92,\n            93,\n            98,\n            99,\n            112,\n            113,\n            128,\n            129,\n            134,\n            135,\n            148,\n            149,\n            160,\n            161,\n            162,\n            163,\n            164,\n            165,\n            166,\n            167,\n            168,\n            169,\n            170,\n            171,\n            172,\n            173,\n            178,\n            179,\n            184,\n            185,\n            196,\n            197,\n            198,\n            199,\n            200,\n            201,\n            202,\n            203,\n            204,\n            205,\n            206,\n            207,\n            208,\n            209,\n            214,\n            215,\n            220,\n            221\n        ];\n    }\n} catch (error) {\n// Swallow - we only care if `supports-color` is available; it doesn't have to be.\n}\n/**\n * Build up the default `inspectOpts` object from the environment variables.\n *\n *   $ DEBUG_COLORS=no DEBUG_DEPTH=10 DEBUG_SHOW_HIDDEN=enabled node script.js\n */ exports.inspectOpts = Object.keys(process.env).filter((key)=>{\n    return /^debug_/i.test(key);\n}).reduce((obj, key)=>{\n    // Camel-case\n    const prop = key.substring(6).toLowerCase().replace(/_([a-z])/g, (_, k)=>{\n        return k.toUpperCase();\n    });\n    // Coerce string value into JS value\n    let val = process.env[key];\n    if (/^(yes|on|true|enabled)$/i.test(val)) {\n        val = true;\n    } else if (/^(no|off|false|disabled)$/i.test(val)) {\n        val = false;\n    } else if (val === \"null\") {\n        val = null;\n    } else {\n        val = Number(val);\n    }\n    obj[prop] = val;\n    return obj;\n}, {});\n/**\n * Is stdout a TTY? Colored output is enabled when `true`.\n */ function useColors() {\n    return \"colors\" in exports.inspectOpts ? Boolean(exports.inspectOpts.colors) : tty.isatty(process.stderr.fd);\n}\n/**\n * Adds ANSI color escape codes if enabled.\n *\n * @api public\n */ function formatArgs(args) {\n    const { namespace: name, useColors } = this;\n    if (useColors) {\n        const c = this.color;\n        const colorCode = \"\\x1b[3\" + (c < 8 ? c : \"8;5;\" + c);\n        const prefix = `  ${colorCode};1m${name} \\u001B[0m`;\n        args[0] = prefix + args[0].split(\"\\n\").join(\"\\n\" + prefix);\n        args.push(colorCode + \"m+\" + module.exports.humanize(this.diff) + \"\\x1b[0m\");\n    } else {\n        args[0] = getDate() + name + \" \" + args[0];\n    }\n}\nfunction getDate() {\n    if (exports.inspectOpts.hideDate) {\n        return \"\";\n    }\n    return new Date().toISOString() + \" \";\n}\n/**\n * Invokes `util.formatWithOptions()` with the specified arguments and writes to stderr.\n */ function log(...args) {\n    return process.stderr.write(util.formatWithOptions(exports.inspectOpts, ...args) + \"\\n\");\n}\n/**\n * Save `namespaces`.\n *\n * @param {String} namespaces\n * @api private\n */ function save(namespaces) {\n    if (namespaces) {\n        process.env.DEBUG = namespaces;\n    } else {\n        // If you set a process.env field to null or undefined, it gets cast to the\n        // string 'null' or 'undefined'. Just delete instead.\n        delete process.env.DEBUG;\n    }\n}\n/**\n * Load `namespaces`.\n *\n * @return {String} returns the previously persisted debug modes\n * @api private\n */ function load() {\n    return process.env.DEBUG;\n}\n/**\n * Init logic for `debug` instances.\n *\n * Create a new `inspectOpts` object in case `useColors` is set\n * differently for a particular `debug` instance.\n */ function init(debug) {\n    debug.inspectOpts = {};\n    const keys = Object.keys(exports.inspectOpts);\n    for(let i = 0; i < keys.length; i++){\n        debug.inspectOpts[keys[i]] = exports.inspectOpts[keys[i]];\n    }\n}\nmodule.exports = __webpack_require__(/*! ./common */ \"(ssr)/./node_modules/socket.io-client/node_modules/debug/src/common.js\")(exports);\nconst { formatters } = module.exports;\n/**\n * Map %o to `util.inspect()`, all on a single line.\n */ formatters.o = function(v) {\n    this.inspectOpts.colors = this.useColors;\n    return util.inspect(v, this.inspectOpts).split(\"\\n\").map((str)=>str.trim()).join(\" \");\n};\n/**\n * Map %O to `util.inspect()`, allowing multiple lines if needed.\n */ formatters.O = function(v) {\n    this.inspectOpts.colors = this.useColors;\n    return util.inspect(v, this.inspectOpts);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/socket.io-client/node_modules/debug/src/node.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/socket.io-client/build/esm-debug/contrib/backo2.js":
/*!*************************************************************************!*\
  !*** ./node_modules/socket.io-client/build/esm-debug/contrib/backo2.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Backoff: () => (/* binding */ Backoff)\n/* harmony export */ });\n/**\n * Initialize backoff timer with `opts`.\n *\n * - `min` initial timeout in milliseconds [100]\n * - `max` max timeout [10000]\n * - `jitter` [0]\n * - `factor` [2]\n *\n * @param {Object} opts\n * @api public\n */ function Backoff(opts) {\n    opts = opts || {};\n    this.ms = opts.min || 100;\n    this.max = opts.max || 10000;\n    this.factor = opts.factor || 2;\n    this.jitter = opts.jitter > 0 && opts.jitter <= 1 ? opts.jitter : 0;\n    this.attempts = 0;\n}\n/**\n * Return the backoff duration.\n *\n * @return {Number}\n * @api public\n */ Backoff.prototype.duration = function() {\n    var ms = this.ms * Math.pow(this.factor, this.attempts++);\n    if (this.jitter) {\n        var rand = Math.random();\n        var deviation = Math.floor(rand * this.jitter * ms);\n        ms = (Math.floor(rand * 10) & 1) == 0 ? ms - deviation : ms + deviation;\n    }\n    return Math.min(ms, this.max) | 0;\n};\n/**\n * Reset the number of attempts.\n *\n * @api public\n */ Backoff.prototype.reset = function() {\n    this.attempts = 0;\n};\n/**\n * Set the minimum duration\n *\n * @api public\n */ Backoff.prototype.setMin = function(min) {\n    this.ms = min;\n};\n/**\n * Set the maximum duration\n *\n * @api public\n */ Backoff.prototype.setMax = function(max) {\n    this.max = max;\n};\n/**\n * Set the jitter\n *\n * @api public\n */ Backoff.prototype.setJitter = function(jitter) {\n    this.jitter = jitter;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/socket.io-client/build/esm-debug/contrib/backo2.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/socket.io-client/build/esm-debug/index.js":
/*!****************************************************************!*\
  !*** ./node_modules/socket.io-client/build/esm-debug/index.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Fetch: () => (/* reexport safe */ engine_io_client__WEBPACK_IMPORTED_MODULE_5__.Fetch),\n/* harmony export */   Manager: () => (/* reexport safe */ _manager_js__WEBPACK_IMPORTED_MODULE_1__.Manager),\n/* harmony export */   NodeWebSocket: () => (/* reexport safe */ engine_io_client__WEBPACK_IMPORTED_MODULE_5__.NodeWebSocket),\n/* harmony export */   NodeXHR: () => (/* reexport safe */ engine_io_client__WEBPACK_IMPORTED_MODULE_5__.NodeXHR),\n/* harmony export */   Socket: () => (/* reexport safe */ _socket_js__WEBPACK_IMPORTED_MODULE_2__.Socket),\n/* harmony export */   WebSocket: () => (/* reexport safe */ engine_io_client__WEBPACK_IMPORTED_MODULE_5__.WebSocket),\n/* harmony export */   WebTransport: () => (/* reexport safe */ engine_io_client__WEBPACK_IMPORTED_MODULE_5__.WebTransport),\n/* harmony export */   XHR: () => (/* reexport safe */ engine_io_client__WEBPACK_IMPORTED_MODULE_5__.XHR),\n/* harmony export */   connect: () => (/* binding */ lookup),\n/* harmony export */   \"default\": () => (/* binding */ lookup),\n/* harmony export */   io: () => (/* binding */ lookup),\n/* harmony export */   protocol: () => (/* reexport safe */ socket_io_parser__WEBPACK_IMPORTED_MODULE_4__.protocol)\n/* harmony export */ });\n/* harmony import */ var _url_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./url.js */ \"(ssr)/./node_modules/socket.io-client/build/esm-debug/url.js\");\n/* harmony import */ var _manager_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./manager.js */ \"(ssr)/./node_modules/socket.io-client/build/esm-debug/manager.js\");\n/* harmony import */ var _socket_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./socket.js */ \"(ssr)/./node_modules/socket.io-client/build/esm-debug/socket.js\");\n/* harmony import */ var debug__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! debug */ \"(ssr)/./node_modules/socket.io-client/node_modules/debug/src/index.js\");\n/* harmony import */ var socket_io_parser__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! socket.io-parser */ \"(ssr)/./node_modules/socket.io-parser/build/esm-debug/index.js\");\n/* harmony import */ var engine_io_client__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! engine.io-client */ \"(ssr)/./node_modules/engine.io-client/build/esm-debug/index.js\");\n\n\n\n // debug()\nconst debug = debug__WEBPACK_IMPORTED_MODULE_3__(\"socket.io-client\"); // debug()\n/**\n * Managers cache.\n */ const cache = {};\nfunction lookup(uri, opts) {\n    if (typeof uri === \"object\") {\n        opts = uri;\n        uri = undefined;\n    }\n    opts = opts || {};\n    const parsed = (0,_url_js__WEBPACK_IMPORTED_MODULE_0__.url)(uri, opts.path || \"/socket.io\");\n    const source = parsed.source;\n    const id = parsed.id;\n    const path = parsed.path;\n    const sameNamespace = cache[id] && path in cache[id][\"nsps\"];\n    const newConnection = opts.forceNew || opts[\"force new connection\"] || false === opts.multiplex || sameNamespace;\n    let io;\n    if (newConnection) {\n        debug(\"ignoring socket cache for %s\", source);\n        io = new _manager_js__WEBPACK_IMPORTED_MODULE_1__.Manager(source, opts);\n    } else {\n        if (!cache[id]) {\n            debug(\"new io instance for %s\", source);\n            cache[id] = new _manager_js__WEBPACK_IMPORTED_MODULE_1__.Manager(source, opts);\n        }\n        io = cache[id];\n    }\n    if (parsed.query && !opts.query) {\n        opts.query = parsed.queryKey;\n    }\n    return io.socket(parsed.path, opts);\n}\n// so that \"lookup\" can be used both as a function (e.g. `io(...)`) and as a\n// namespace (e.g. `io.connect(...)`), for backward compatibility\nObject.assign(lookup, {\n    Manager: _manager_js__WEBPACK_IMPORTED_MODULE_1__.Manager,\n    Socket: _socket_js__WEBPACK_IMPORTED_MODULE_2__.Socket,\n    io: lookup,\n    connect: lookup\n});\n/**\n * Protocol version.\n *\n * @public\n */ \n/**\n * Expose constructors for standalone build.\n *\n * @public\n */ \n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/socket.io-client/build/esm-debug/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/socket.io-client/build/esm-debug/manager.js":
/*!******************************************************************!*\
  !*** ./node_modules/socket.io-client/build/esm-debug/manager.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Manager: () => (/* binding */ Manager)\n/* harmony export */ });\n/* harmony import */ var engine_io_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! engine.io-client */ \"(ssr)/./node_modules/engine.io-client/build/esm-debug/index.js\");\n/* harmony import */ var _socket_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./socket.js */ \"(ssr)/./node_modules/socket.io-client/build/esm-debug/socket.js\");\n/* harmony import */ var socket_io_parser__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! socket.io-parser */ \"(ssr)/./node_modules/socket.io-parser/build/esm-debug/index.js\");\n/* harmony import */ var _on_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./on.js */ \"(ssr)/./node_modules/socket.io-client/build/esm-debug/on.js\");\n/* harmony import */ var _contrib_backo2_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./contrib/backo2.js */ \"(ssr)/./node_modules/socket.io-client/build/esm-debug/contrib/backo2.js\");\n/* harmony import */ var _socket_io_component_emitter__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @socket.io/component-emitter */ \"(ssr)/./node_modules/@socket.io/component-emitter/lib/esm/index.js\");\n/* harmony import */ var debug__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! debug */ \"(ssr)/./node_modules/socket.io-client/node_modules/debug/src/index.js\");\n\n\n\n\n\n\n // debug()\nconst debug = debug__WEBPACK_IMPORTED_MODULE_6__(\"socket.io-client:manager\"); // debug()\nclass Manager extends _socket_io_component_emitter__WEBPACK_IMPORTED_MODULE_5__.Emitter {\n    constructor(uri, opts){\n        var _a;\n        super();\n        this.nsps = {};\n        this.subs = [];\n        if (uri && \"object\" === typeof uri) {\n            opts = uri;\n            uri = undefined;\n        }\n        opts = opts || {};\n        opts.path = opts.path || \"/socket.io\";\n        this.opts = opts;\n        (0,engine_io_client__WEBPACK_IMPORTED_MODULE_0__.installTimerFunctions)(this, opts);\n        this.reconnection(opts.reconnection !== false);\n        this.reconnectionAttempts(opts.reconnectionAttempts || Infinity);\n        this.reconnectionDelay(opts.reconnectionDelay || 1000);\n        this.reconnectionDelayMax(opts.reconnectionDelayMax || 5000);\n        this.randomizationFactor((_a = opts.randomizationFactor) !== null && _a !== void 0 ? _a : 0.5);\n        this.backoff = new _contrib_backo2_js__WEBPACK_IMPORTED_MODULE_4__.Backoff({\n            min: this.reconnectionDelay(),\n            max: this.reconnectionDelayMax(),\n            jitter: this.randomizationFactor()\n        });\n        this.timeout(null == opts.timeout ? 20000 : opts.timeout);\n        this._readyState = \"closed\";\n        this.uri = uri;\n        const _parser = opts.parser || socket_io_parser__WEBPACK_IMPORTED_MODULE_2__;\n        this.encoder = new _parser.Encoder();\n        this.decoder = new _parser.Decoder();\n        this._autoConnect = opts.autoConnect !== false;\n        if (this._autoConnect) this.open();\n    }\n    reconnection(v) {\n        if (!arguments.length) return this._reconnection;\n        this._reconnection = !!v;\n        if (!v) {\n            this.skipReconnect = true;\n        }\n        return this;\n    }\n    reconnectionAttempts(v) {\n        if (v === undefined) return this._reconnectionAttempts;\n        this._reconnectionAttempts = v;\n        return this;\n    }\n    reconnectionDelay(v) {\n        var _a;\n        if (v === undefined) return this._reconnectionDelay;\n        this._reconnectionDelay = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setMin(v);\n        return this;\n    }\n    randomizationFactor(v) {\n        var _a;\n        if (v === undefined) return this._randomizationFactor;\n        this._randomizationFactor = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setJitter(v);\n        return this;\n    }\n    reconnectionDelayMax(v) {\n        var _a;\n        if (v === undefined) return this._reconnectionDelayMax;\n        this._reconnectionDelayMax = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setMax(v);\n        return this;\n    }\n    timeout(v) {\n        if (!arguments.length) return this._timeout;\n        this._timeout = v;\n        return this;\n    }\n    /**\n     * Starts trying to reconnect if reconnection is enabled and we have not\n     * started reconnecting yet\n     *\n     * @private\n     */ maybeReconnectOnOpen() {\n        // Only try to reconnect if it's the first time we're connecting\n        if (!this._reconnecting && this._reconnection && this.backoff.attempts === 0) {\n            // keeps reconnection from firing twice for the same reconnection loop\n            this.reconnect();\n        }\n    }\n    /**\n     * Sets the current transport `socket`.\n     *\n     * @param {Function} fn - optional, callback\n     * @return self\n     * @public\n     */ open(fn) {\n        debug(\"readyState %s\", this._readyState);\n        if (~this._readyState.indexOf(\"open\")) return this;\n        debug(\"opening %s\", this.uri);\n        this.engine = new engine_io_client__WEBPACK_IMPORTED_MODULE_0__.Socket(this.uri, this.opts);\n        const socket = this.engine;\n        const self = this;\n        this._readyState = \"opening\";\n        this.skipReconnect = false;\n        // emit `open`\n        const openSubDestroy = (0,_on_js__WEBPACK_IMPORTED_MODULE_3__.on)(socket, \"open\", function() {\n            self.onopen();\n            fn && fn();\n        });\n        const onError = (err)=>{\n            debug(\"error\");\n            this.cleanup();\n            this._readyState = \"closed\";\n            this.emitReserved(\"error\", err);\n            if (fn) {\n                fn(err);\n            } else {\n                // Only do this if there is no fn to handle the error\n                this.maybeReconnectOnOpen();\n            }\n        };\n        // emit `error`\n        const errorSub = (0,_on_js__WEBPACK_IMPORTED_MODULE_3__.on)(socket, \"error\", onError);\n        if (false !== this._timeout) {\n            const timeout = this._timeout;\n            debug(\"connect attempt will timeout after %d\", timeout);\n            // set timer\n            const timer = this.setTimeoutFn(()=>{\n                debug(\"connect attempt timed out after %d\", timeout);\n                openSubDestroy();\n                onError(new Error(\"timeout\"));\n                socket.close();\n            }, timeout);\n            if (this.opts.autoUnref) {\n                timer.unref();\n            }\n            this.subs.push(()=>{\n                this.clearTimeoutFn(timer);\n            });\n        }\n        this.subs.push(openSubDestroy);\n        this.subs.push(errorSub);\n        return this;\n    }\n    /**\n     * Alias for open()\n     *\n     * @return self\n     * @public\n     */ connect(fn) {\n        return this.open(fn);\n    }\n    /**\n     * Called upon transport open.\n     *\n     * @private\n     */ onopen() {\n        debug(\"open\");\n        // clear old subs\n        this.cleanup();\n        // mark as open\n        this._readyState = \"open\";\n        this.emitReserved(\"open\");\n        // add new subs\n        const socket = this.engine;\n        this.subs.push((0,_on_js__WEBPACK_IMPORTED_MODULE_3__.on)(socket, \"ping\", this.onping.bind(this)), (0,_on_js__WEBPACK_IMPORTED_MODULE_3__.on)(socket, \"data\", this.ondata.bind(this)), (0,_on_js__WEBPACK_IMPORTED_MODULE_3__.on)(socket, \"error\", this.onerror.bind(this)), (0,_on_js__WEBPACK_IMPORTED_MODULE_3__.on)(socket, \"close\", this.onclose.bind(this)), // @ts-ignore\n        (0,_on_js__WEBPACK_IMPORTED_MODULE_3__.on)(this.decoder, \"decoded\", this.ondecoded.bind(this)));\n    }\n    /**\n     * Called upon a ping.\n     *\n     * @private\n     */ onping() {\n        this.emitReserved(\"ping\");\n    }\n    /**\n     * Called with data.\n     *\n     * @private\n     */ ondata(data) {\n        try {\n            this.decoder.add(data);\n        } catch (e) {\n            this.onclose(\"parse error\", e);\n        }\n    }\n    /**\n     * Called when parser fully decodes a packet.\n     *\n     * @private\n     */ ondecoded(packet) {\n        // the nextTick call prevents an exception in a user-provided event listener from triggering a disconnection due to a \"parse error\"\n        (0,engine_io_client__WEBPACK_IMPORTED_MODULE_0__.nextTick)(()=>{\n            this.emitReserved(\"packet\", packet);\n        }, this.setTimeoutFn);\n    }\n    /**\n     * Called upon socket error.\n     *\n     * @private\n     */ onerror(err) {\n        debug(\"error\", err);\n        this.emitReserved(\"error\", err);\n    }\n    /**\n     * Creates a new socket for the given `nsp`.\n     *\n     * @return {Socket}\n     * @public\n     */ socket(nsp, opts) {\n        let socket = this.nsps[nsp];\n        if (!socket) {\n            socket = new _socket_js__WEBPACK_IMPORTED_MODULE_1__.Socket(this, nsp, opts);\n            this.nsps[nsp] = socket;\n        } else if (this._autoConnect && !socket.active) {\n            socket.connect();\n        }\n        return socket;\n    }\n    /**\n     * Called upon a socket close.\n     *\n     * @param socket\n     * @private\n     */ _destroy(socket) {\n        const nsps = Object.keys(this.nsps);\n        for (const nsp of nsps){\n            const socket = this.nsps[nsp];\n            if (socket.active) {\n                debug(\"socket %s is still active, skipping close\", nsp);\n                return;\n            }\n        }\n        this._close();\n    }\n    /**\n     * Writes a packet.\n     *\n     * @param packet\n     * @private\n     */ _packet(packet) {\n        debug(\"writing packet %j\", packet);\n        const encodedPackets = this.encoder.encode(packet);\n        for(let i = 0; i < encodedPackets.length; i++){\n            this.engine.write(encodedPackets[i], packet.options);\n        }\n    }\n    /**\n     * Clean up transport subscriptions and packet buffer.\n     *\n     * @private\n     */ cleanup() {\n        debug(\"cleanup\");\n        this.subs.forEach((subDestroy)=>subDestroy());\n        this.subs.length = 0;\n        this.decoder.destroy();\n    }\n    /**\n     * Close the current socket.\n     *\n     * @private\n     */ _close() {\n        debug(\"disconnect\");\n        this.skipReconnect = true;\n        this._reconnecting = false;\n        this.onclose(\"forced close\");\n    }\n    /**\n     * Alias for close()\n     *\n     * @private\n     */ disconnect() {\n        return this._close();\n    }\n    /**\n     * Called when:\n     *\n     * - the low-level engine is closed\n     * - the parser encountered a badly formatted packet\n     * - all sockets are disconnected\n     *\n     * @private\n     */ onclose(reason, description) {\n        var _a;\n        debug(\"closed due to %s\", reason);\n        this.cleanup();\n        (_a = this.engine) === null || _a === void 0 ? void 0 : _a.close();\n        this.backoff.reset();\n        this._readyState = \"closed\";\n        this.emitReserved(\"close\", reason, description);\n        if (this._reconnection && !this.skipReconnect) {\n            this.reconnect();\n        }\n    }\n    /**\n     * Attempt a reconnection.\n     *\n     * @private\n     */ reconnect() {\n        if (this._reconnecting || this.skipReconnect) return this;\n        const self = this;\n        if (this.backoff.attempts >= this._reconnectionAttempts) {\n            debug(\"reconnect failed\");\n            this.backoff.reset();\n            this.emitReserved(\"reconnect_failed\");\n            this._reconnecting = false;\n        } else {\n            const delay = this.backoff.duration();\n            debug(\"will wait %dms before reconnect attempt\", delay);\n            this._reconnecting = true;\n            const timer = this.setTimeoutFn(()=>{\n                if (self.skipReconnect) return;\n                debug(\"attempting reconnect\");\n                this.emitReserved(\"reconnect_attempt\", self.backoff.attempts);\n                // check again for the case socket closed in above events\n                if (self.skipReconnect) return;\n                self.open((err)=>{\n                    if (err) {\n                        debug(\"reconnect attempt error\");\n                        self._reconnecting = false;\n                        self.reconnect();\n                        this.emitReserved(\"reconnect_error\", err);\n                    } else {\n                        debug(\"reconnect success\");\n                        self.onreconnect();\n                    }\n                });\n            }, delay);\n            if (this.opts.autoUnref) {\n                timer.unref();\n            }\n            this.subs.push(()=>{\n                this.clearTimeoutFn(timer);\n            });\n        }\n    }\n    /**\n     * Called upon successful reconnect.\n     *\n     * @private\n     */ onreconnect() {\n        const attempt = this.backoff.attempts;\n        this._reconnecting = false;\n        this.backoff.reset();\n        this.emitReserved(\"reconnect\", attempt);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvc29ja2V0LmlvLWNsaWVudC9idWlsZC9lc20tZGVidWcvbWFuYWdlci5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUFzRjtBQUNqRDtBQUNNO0FBQ2Q7QUFDaUI7QUFDVTtBQUN4QixDQUFDLFVBQVU7QUFDM0MsTUFBTVMsUUFBUUQsa0NBQVdBLENBQUMsNkJBQTZCLFVBQVU7QUFDMUQsTUFBTUUsZ0JBQWdCSCxpRUFBT0E7SUFDaENJLFlBQVlDLEdBQUcsRUFBRUMsSUFBSSxDQUFFO1FBQ25CLElBQUlDO1FBQ0osS0FBSztRQUNMLElBQUksQ0FBQ0MsSUFBSSxHQUFHLENBQUM7UUFDYixJQUFJLENBQUNDLElBQUksR0FBRyxFQUFFO1FBQ2QsSUFBSUosT0FBTyxhQUFhLE9BQU9BLEtBQUs7WUFDaENDLE9BQU9EO1lBQ1BBLE1BQU1LO1FBQ1Y7UUFDQUosT0FBT0EsUUFBUSxDQUFDO1FBQ2hCQSxLQUFLSyxJQUFJLEdBQUdMLEtBQUtLLElBQUksSUFBSTtRQUN6QixJQUFJLENBQUNMLElBQUksR0FBR0E7UUFDWlgsdUVBQXFCQSxDQUFDLElBQUksRUFBRVc7UUFDNUIsSUFBSSxDQUFDTSxZQUFZLENBQUNOLEtBQUtNLFlBQVksS0FBSztRQUN4QyxJQUFJLENBQUNDLG9CQUFvQixDQUFDUCxLQUFLTyxvQkFBb0IsSUFBSUM7UUFDdkQsSUFBSSxDQUFDQyxpQkFBaUIsQ0FBQ1QsS0FBS1MsaUJBQWlCLElBQUk7UUFDakQsSUFBSSxDQUFDQyxvQkFBb0IsQ0FBQ1YsS0FBS1Usb0JBQW9CLElBQUk7UUFDdkQsSUFBSSxDQUFDQyxtQkFBbUIsQ0FBQyxDQUFDVixLQUFLRCxLQUFLVyxtQkFBbUIsTUFBTSxRQUFRVixPQUFPLEtBQUssSUFBSUEsS0FBSztRQUMxRixJQUFJLENBQUNXLE9BQU8sR0FBRyxJQUFJbkIsdURBQU9BLENBQUM7WUFDdkJvQixLQUFLLElBQUksQ0FBQ0osaUJBQWlCO1lBQzNCSyxLQUFLLElBQUksQ0FBQ0osb0JBQW9CO1lBQzlCSyxRQUFRLElBQUksQ0FBQ0osbUJBQW1CO1FBQ3BDO1FBQ0EsSUFBSSxDQUFDSyxPQUFPLENBQUMsUUFBUWhCLEtBQUtnQixPQUFPLEdBQUcsUUFBUWhCLEtBQUtnQixPQUFPO1FBQ3hELElBQUksQ0FBQ0MsV0FBVyxHQUFHO1FBQ25CLElBQUksQ0FBQ2xCLEdBQUcsR0FBR0E7UUFDWCxNQUFNbUIsVUFBVWxCLEtBQUtULE1BQU0sSUFBSUEsNkNBQU1BO1FBQ3JDLElBQUksQ0FBQzRCLE9BQU8sR0FBRyxJQUFJRCxRQUFRRSxPQUFPO1FBQ2xDLElBQUksQ0FBQ0MsT0FBTyxHQUFHLElBQUlILFFBQVFJLE9BQU87UUFDbEMsSUFBSSxDQUFDQyxZQUFZLEdBQUd2QixLQUFLd0IsV0FBVyxLQUFLO1FBQ3pDLElBQUksSUFBSSxDQUFDRCxZQUFZLEVBQ2pCLElBQUksQ0FBQ0UsSUFBSTtJQUNqQjtJQUNBbkIsYUFBYW9CLENBQUMsRUFBRTtRQUNaLElBQUksQ0FBQ0MsVUFBVUMsTUFBTSxFQUNqQixPQUFPLElBQUksQ0FBQ0MsYUFBYTtRQUM3QixJQUFJLENBQUNBLGFBQWEsR0FBRyxDQUFDLENBQUNIO1FBQ3ZCLElBQUksQ0FBQ0EsR0FBRztZQUNKLElBQUksQ0FBQ0ksYUFBYSxHQUFHO1FBQ3pCO1FBQ0EsT0FBTyxJQUFJO0lBQ2Y7SUFDQXZCLHFCQUFxQm1CLENBQUMsRUFBRTtRQUNwQixJQUFJQSxNQUFNdEIsV0FDTixPQUFPLElBQUksQ0FBQzJCLHFCQUFxQjtRQUNyQyxJQUFJLENBQUNBLHFCQUFxQixHQUFHTDtRQUM3QixPQUFPLElBQUk7SUFDZjtJQUNBakIsa0JBQWtCaUIsQ0FBQyxFQUFFO1FBQ2pCLElBQUl6QjtRQUNKLElBQUl5QixNQUFNdEIsV0FDTixPQUFPLElBQUksQ0FBQzRCLGtCQUFrQjtRQUNsQyxJQUFJLENBQUNBLGtCQUFrQixHQUFHTjtRQUN6QnpCLENBQUFBLEtBQUssSUFBSSxDQUFDVyxPQUFPLE1BQU0sUUFBUVgsT0FBTyxLQUFLLElBQUksS0FBSyxJQUFJQSxHQUFHZ0MsTUFBTSxDQUFDUDtRQUNuRSxPQUFPLElBQUk7SUFDZjtJQUNBZixvQkFBb0JlLENBQUMsRUFBRTtRQUNuQixJQUFJekI7UUFDSixJQUFJeUIsTUFBTXRCLFdBQ04sT0FBTyxJQUFJLENBQUM4QixvQkFBb0I7UUFDcEMsSUFBSSxDQUFDQSxvQkFBb0IsR0FBR1I7UUFDM0J6QixDQUFBQSxLQUFLLElBQUksQ0FBQ1csT0FBTyxNQUFNLFFBQVFYLE9BQU8sS0FBSyxJQUFJLEtBQUssSUFBSUEsR0FBR2tDLFNBQVMsQ0FBQ1Q7UUFDdEUsT0FBTyxJQUFJO0lBQ2Y7SUFDQWhCLHFCQUFxQmdCLENBQUMsRUFBRTtRQUNwQixJQUFJekI7UUFDSixJQUFJeUIsTUFBTXRCLFdBQ04sT0FBTyxJQUFJLENBQUNnQyxxQkFBcUI7UUFDckMsSUFBSSxDQUFDQSxxQkFBcUIsR0FBR1Y7UUFDNUJ6QixDQUFBQSxLQUFLLElBQUksQ0FBQ1csT0FBTyxNQUFNLFFBQVFYLE9BQU8sS0FBSyxJQUFJLEtBQUssSUFBSUEsR0FBR29DLE1BQU0sQ0FBQ1g7UUFDbkUsT0FBTyxJQUFJO0lBQ2Y7SUFDQVYsUUFBUVUsQ0FBQyxFQUFFO1FBQ1AsSUFBSSxDQUFDQyxVQUFVQyxNQUFNLEVBQ2pCLE9BQU8sSUFBSSxDQUFDVSxRQUFRO1FBQ3hCLElBQUksQ0FBQ0EsUUFBUSxHQUFHWjtRQUNoQixPQUFPLElBQUk7SUFDZjtJQUNBOzs7OztLQUtDLEdBQ0RhLHVCQUF1QjtRQUNuQixnRUFBZ0U7UUFDaEUsSUFBSSxDQUFDLElBQUksQ0FBQ0MsYUFBYSxJQUNuQixJQUFJLENBQUNYLGFBQWEsSUFDbEIsSUFBSSxDQUFDakIsT0FBTyxDQUFDNkIsUUFBUSxLQUFLLEdBQUc7WUFDN0Isc0VBQXNFO1lBQ3RFLElBQUksQ0FBQ0MsU0FBUztRQUNsQjtJQUNKO0lBQ0E7Ozs7OztLQU1DLEdBQ0RqQixLQUFLa0IsRUFBRSxFQUFFO1FBQ0wvQyxNQUFNLGlCQUFpQixJQUFJLENBQUNxQixXQUFXO1FBQ3ZDLElBQUksQ0FBQyxJQUFJLENBQUNBLFdBQVcsQ0FBQzJCLE9BQU8sQ0FBQyxTQUMxQixPQUFPLElBQUk7UUFDZmhELE1BQU0sY0FBYyxJQUFJLENBQUNHLEdBQUc7UUFDNUIsSUFBSSxDQUFDOEMsTUFBTSxHQUFHLElBQUl6RCxvREFBTUEsQ0FBQyxJQUFJLENBQUNXLEdBQUcsRUFBRSxJQUFJLENBQUNDLElBQUk7UUFDNUMsTUFBTThDLFNBQVMsSUFBSSxDQUFDRCxNQUFNO1FBQzFCLE1BQU1FLE9BQU8sSUFBSTtRQUNqQixJQUFJLENBQUM5QixXQUFXLEdBQUc7UUFDbkIsSUFBSSxDQUFDYSxhQUFhLEdBQUc7UUFDckIsY0FBYztRQUNkLE1BQU1rQixpQkFBaUJ4RCwwQ0FBRUEsQ0FBQ3NELFFBQVEsUUFBUTtZQUN0Q0MsS0FBS0UsTUFBTTtZQUNYTixNQUFNQTtRQUNWO1FBQ0EsTUFBTU8sVUFBVSxDQUFDQztZQUNidkQsTUFBTTtZQUNOLElBQUksQ0FBQ3dELE9BQU87WUFDWixJQUFJLENBQUNuQyxXQUFXLEdBQUc7WUFDbkIsSUFBSSxDQUFDb0MsWUFBWSxDQUFDLFNBQVNGO1lBQzNCLElBQUlSLElBQUk7Z0JBQ0pBLEdBQUdRO1lBQ1AsT0FDSztnQkFDRCxxREFBcUQ7Z0JBQ3JELElBQUksQ0FBQ1osb0JBQW9CO1lBQzdCO1FBQ0o7UUFDQSxlQUFlO1FBQ2YsTUFBTWUsV0FBVzlELDBDQUFFQSxDQUFDc0QsUUFBUSxTQUFTSTtRQUNyQyxJQUFJLFVBQVUsSUFBSSxDQUFDWixRQUFRLEVBQUU7WUFDekIsTUFBTXRCLFVBQVUsSUFBSSxDQUFDc0IsUUFBUTtZQUM3QjFDLE1BQU0seUNBQXlDb0I7WUFDL0MsWUFBWTtZQUNaLE1BQU11QyxRQUFRLElBQUksQ0FBQ0MsWUFBWSxDQUFDO2dCQUM1QjVELE1BQU0sc0NBQXNDb0I7Z0JBQzVDZ0M7Z0JBQ0FFLFFBQVEsSUFBSU8sTUFBTTtnQkFDbEJYLE9BQU9ZLEtBQUs7WUFDaEIsR0FBRzFDO1lBQ0gsSUFBSSxJQUFJLENBQUNoQixJQUFJLENBQUMyRCxTQUFTLEVBQUU7Z0JBQ3JCSixNQUFNSyxLQUFLO1lBQ2Y7WUFDQSxJQUFJLENBQUN6RCxJQUFJLENBQUMwRCxJQUFJLENBQUM7Z0JBQ1gsSUFBSSxDQUFDQyxjQUFjLENBQUNQO1lBQ3hCO1FBQ0o7UUFDQSxJQUFJLENBQUNwRCxJQUFJLENBQUMwRCxJQUFJLENBQUNiO1FBQ2YsSUFBSSxDQUFDN0MsSUFBSSxDQUFDMEQsSUFBSSxDQUFDUDtRQUNmLE9BQU8sSUFBSTtJQUNmO0lBQ0E7Ozs7O0tBS0MsR0FDRFMsUUFBUXBCLEVBQUUsRUFBRTtRQUNSLE9BQU8sSUFBSSxDQUFDbEIsSUFBSSxDQUFDa0I7SUFDckI7SUFDQTs7OztLQUlDLEdBQ0RNLFNBQVM7UUFDTHJELE1BQU07UUFDTixpQkFBaUI7UUFDakIsSUFBSSxDQUFDd0QsT0FBTztRQUNaLGVBQWU7UUFDZixJQUFJLENBQUNuQyxXQUFXLEdBQUc7UUFDbkIsSUFBSSxDQUFDb0MsWUFBWSxDQUFDO1FBQ2xCLGVBQWU7UUFDZixNQUFNUCxTQUFTLElBQUksQ0FBQ0QsTUFBTTtRQUMxQixJQUFJLENBQUMxQyxJQUFJLENBQUMwRCxJQUFJLENBQUNyRSwwQ0FBRUEsQ0FBQ3NELFFBQVEsUUFBUSxJQUFJLENBQUNrQixNQUFNLENBQUNDLElBQUksQ0FBQyxJQUFJLElBQUl6RSwwQ0FBRUEsQ0FBQ3NELFFBQVEsUUFBUSxJQUFJLENBQUNvQixNQUFNLENBQUNELElBQUksQ0FBQyxJQUFJLElBQUl6RSwwQ0FBRUEsQ0FBQ3NELFFBQVEsU0FBUyxJQUFJLENBQUNxQixPQUFPLENBQUNGLElBQUksQ0FBQyxJQUFJLElBQUl6RSwwQ0FBRUEsQ0FBQ3NELFFBQVEsU0FBUyxJQUFJLENBQUNzQixPQUFPLENBQUNILElBQUksQ0FBQyxJQUFJLElBQy9MLGFBQWE7UUFDYnpFLDBDQUFFQSxDQUFDLElBQUksQ0FBQzZCLE9BQU8sRUFBRSxXQUFXLElBQUksQ0FBQ2dELFNBQVMsQ0FBQ0osSUFBSSxDQUFDLElBQUk7SUFDeEQ7SUFDQTs7OztLQUlDLEdBQ0RELFNBQVM7UUFDTCxJQUFJLENBQUNYLFlBQVksQ0FBQztJQUN0QjtJQUNBOzs7O0tBSUMsR0FDRGEsT0FBT0ksSUFBSSxFQUFFO1FBQ1QsSUFBSTtZQUNBLElBQUksQ0FBQ2pELE9BQU8sQ0FBQ2tELEdBQUcsQ0FBQ0Q7UUFDckIsRUFDQSxPQUFPRSxHQUFHO1lBQ04sSUFBSSxDQUFDSixPQUFPLENBQUMsZUFBZUk7UUFDaEM7SUFDSjtJQUNBOzs7O0tBSUMsR0FDREgsVUFBVUksTUFBTSxFQUFFO1FBQ2QsbUlBQW1JO1FBQ25JbkYsMERBQVFBLENBQUM7WUFDTCxJQUFJLENBQUMrRCxZQUFZLENBQUMsVUFBVW9CO1FBQ2hDLEdBQUcsSUFBSSxDQUFDakIsWUFBWTtJQUN4QjtJQUNBOzs7O0tBSUMsR0FDRFcsUUFBUWhCLEdBQUcsRUFBRTtRQUNUdkQsTUFBTSxTQUFTdUQ7UUFDZixJQUFJLENBQUNFLFlBQVksQ0FBQyxTQUFTRjtJQUMvQjtJQUNBOzs7OztLQUtDLEdBQ0RMLE9BQU80QixHQUFHLEVBQUUxRSxJQUFJLEVBQUU7UUFDZCxJQUFJOEMsU0FBUyxJQUFJLENBQUM1QyxJQUFJLENBQUN3RSxJQUFJO1FBQzNCLElBQUksQ0FBQzVCLFFBQVE7WUFDVEEsU0FBUyxJQUFJM0QsOENBQU1BLENBQUMsSUFBSSxFQUFFdUYsS0FBSzFFO1lBQy9CLElBQUksQ0FBQ0UsSUFBSSxDQUFDd0UsSUFBSSxHQUFHNUI7UUFDckIsT0FDSyxJQUFJLElBQUksQ0FBQ3ZCLFlBQVksSUFBSSxDQUFDdUIsT0FBTzZCLE1BQU0sRUFBRTtZQUMxQzdCLE9BQU9pQixPQUFPO1FBQ2xCO1FBQ0EsT0FBT2pCO0lBQ1g7SUFDQTs7Ozs7S0FLQyxHQUNEOEIsU0FBUzlCLE1BQU0sRUFBRTtRQUNiLE1BQU01QyxPQUFPMkUsT0FBT0MsSUFBSSxDQUFDLElBQUksQ0FBQzVFLElBQUk7UUFDbEMsS0FBSyxNQUFNd0UsT0FBT3hFLEtBQU07WUFDcEIsTUFBTTRDLFNBQVMsSUFBSSxDQUFDNUMsSUFBSSxDQUFDd0UsSUFBSTtZQUM3QixJQUFJNUIsT0FBTzZCLE1BQU0sRUFBRTtnQkFDZi9FLE1BQU0sNkNBQTZDOEU7Z0JBQ25EO1lBQ0o7UUFDSjtRQUNBLElBQUksQ0FBQ0ssTUFBTTtJQUNmO0lBQ0E7Ozs7O0tBS0MsR0FDREMsUUFBUVAsTUFBTSxFQUFFO1FBQ1o3RSxNQUFNLHFCQUFxQjZFO1FBQzNCLE1BQU1RLGlCQUFpQixJQUFJLENBQUM5RCxPQUFPLENBQUMrRCxNQUFNLENBQUNUO1FBQzNDLElBQUssSUFBSVUsSUFBSSxHQUFHQSxJQUFJRixlQUFlckQsTUFBTSxFQUFFdUQsSUFBSztZQUM1QyxJQUFJLENBQUN0QyxNQUFNLENBQUN1QyxLQUFLLENBQUNILGNBQWMsQ0FBQ0UsRUFBRSxFQUFFVixPQUFPWSxPQUFPO1FBQ3ZEO0lBQ0o7SUFDQTs7OztLQUlDLEdBQ0RqQyxVQUFVO1FBQ054RCxNQUFNO1FBQ04sSUFBSSxDQUFDTyxJQUFJLENBQUNtRixPQUFPLENBQUMsQ0FBQ0MsYUFBZUE7UUFDbEMsSUFBSSxDQUFDcEYsSUFBSSxDQUFDeUIsTUFBTSxHQUFHO1FBQ25CLElBQUksQ0FBQ1AsT0FBTyxDQUFDbUUsT0FBTztJQUN4QjtJQUNBOzs7O0tBSUMsR0FDRFQsU0FBUztRQUNMbkYsTUFBTTtRQUNOLElBQUksQ0FBQ2tDLGFBQWEsR0FBRztRQUNyQixJQUFJLENBQUNVLGFBQWEsR0FBRztRQUNyQixJQUFJLENBQUM0QixPQUFPLENBQUM7SUFDakI7SUFDQTs7OztLQUlDLEdBQ0RxQixhQUFhO1FBQ1QsT0FBTyxJQUFJLENBQUNWLE1BQU07SUFDdEI7SUFDQTs7Ozs7Ozs7S0FRQyxHQUNEWCxRQUFRc0IsTUFBTSxFQUFFQyxXQUFXLEVBQUU7UUFDekIsSUFBSTFGO1FBQ0pMLE1BQU0sb0JBQW9COEY7UUFDMUIsSUFBSSxDQUFDdEMsT0FBTztRQUNYbkQsQ0FBQUEsS0FBSyxJQUFJLENBQUM0QyxNQUFNLE1BQU0sUUFBUTVDLE9BQU8sS0FBSyxJQUFJLEtBQUssSUFBSUEsR0FBR3lELEtBQUs7UUFDaEUsSUFBSSxDQUFDOUMsT0FBTyxDQUFDZ0YsS0FBSztRQUNsQixJQUFJLENBQUMzRSxXQUFXLEdBQUc7UUFDbkIsSUFBSSxDQUFDb0MsWUFBWSxDQUFDLFNBQVNxQyxRQUFRQztRQUNuQyxJQUFJLElBQUksQ0FBQzlELGFBQWEsSUFBSSxDQUFDLElBQUksQ0FBQ0MsYUFBYSxFQUFFO1lBQzNDLElBQUksQ0FBQ1ksU0FBUztRQUNsQjtJQUNKO0lBQ0E7Ozs7S0FJQyxHQUNEQSxZQUFZO1FBQ1IsSUFBSSxJQUFJLENBQUNGLGFBQWEsSUFBSSxJQUFJLENBQUNWLGFBQWEsRUFDeEMsT0FBTyxJQUFJO1FBQ2YsTUFBTWlCLE9BQU8sSUFBSTtRQUNqQixJQUFJLElBQUksQ0FBQ25DLE9BQU8sQ0FBQzZCLFFBQVEsSUFBSSxJQUFJLENBQUNWLHFCQUFxQixFQUFFO1lBQ3JEbkMsTUFBTTtZQUNOLElBQUksQ0FBQ2dCLE9BQU8sQ0FBQ2dGLEtBQUs7WUFDbEIsSUFBSSxDQUFDdkMsWUFBWSxDQUFDO1lBQ2xCLElBQUksQ0FBQ2IsYUFBYSxHQUFHO1FBQ3pCLE9BQ0s7WUFDRCxNQUFNcUQsUUFBUSxJQUFJLENBQUNqRixPQUFPLENBQUNrRixRQUFRO1lBQ25DbEcsTUFBTSwyQ0FBMkNpRztZQUNqRCxJQUFJLENBQUNyRCxhQUFhLEdBQUc7WUFDckIsTUFBTWUsUUFBUSxJQUFJLENBQUNDLFlBQVksQ0FBQztnQkFDNUIsSUFBSVQsS0FBS2pCLGFBQWEsRUFDbEI7Z0JBQ0psQyxNQUFNO2dCQUNOLElBQUksQ0FBQ3lELFlBQVksQ0FBQyxxQkFBcUJOLEtBQUtuQyxPQUFPLENBQUM2QixRQUFRO2dCQUM1RCx5REFBeUQ7Z0JBQ3pELElBQUlNLEtBQUtqQixhQUFhLEVBQ2xCO2dCQUNKaUIsS0FBS3RCLElBQUksQ0FBQyxDQUFDMEI7b0JBQ1AsSUFBSUEsS0FBSzt3QkFDTHZELE1BQU07d0JBQ05tRCxLQUFLUCxhQUFhLEdBQUc7d0JBQ3JCTyxLQUFLTCxTQUFTO3dCQUNkLElBQUksQ0FBQ1csWUFBWSxDQUFDLG1CQUFtQkY7b0JBQ3pDLE9BQ0s7d0JBQ0R2RCxNQUFNO3dCQUNObUQsS0FBS2dELFdBQVc7b0JBQ3BCO2dCQUNKO1lBQ0osR0FBR0Y7WUFDSCxJQUFJLElBQUksQ0FBQzdGLElBQUksQ0FBQzJELFNBQVMsRUFBRTtnQkFDckJKLE1BQU1LLEtBQUs7WUFDZjtZQUNBLElBQUksQ0FBQ3pELElBQUksQ0FBQzBELElBQUksQ0FBQztnQkFDWCxJQUFJLENBQUNDLGNBQWMsQ0FBQ1A7WUFDeEI7UUFDSjtJQUNKO0lBQ0E7Ozs7S0FJQyxHQUNEd0MsY0FBYztRQUNWLE1BQU1DLFVBQVUsSUFBSSxDQUFDcEYsT0FBTyxDQUFDNkIsUUFBUTtRQUNyQyxJQUFJLENBQUNELGFBQWEsR0FBRztRQUNyQixJQUFJLENBQUM1QixPQUFPLENBQUNnRixLQUFLO1FBQ2xCLElBQUksQ0FBQ3ZDLFlBQVksQ0FBQyxhQUFhMkM7SUFDbkM7QUFDSiIsInNvdXJjZXMiOlsid2VicGFjazovL3N1cGVybWFya2V0LWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL3NvY2tldC5pby1jbGllbnQvYnVpbGQvZXNtLWRlYnVnL21hbmFnZXIuanM/ODUyMCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBTb2NrZXQgYXMgRW5naW5lLCBpbnN0YWxsVGltZXJGdW5jdGlvbnMsIG5leHRUaWNrLCB9IGZyb20gXCJlbmdpbmUuaW8tY2xpZW50XCI7XG5pbXBvcnQgeyBTb2NrZXQgfSBmcm9tIFwiLi9zb2NrZXQuanNcIjtcbmltcG9ydCAqIGFzIHBhcnNlciBmcm9tIFwic29ja2V0LmlvLXBhcnNlclwiO1xuaW1wb3J0IHsgb24gfSBmcm9tIFwiLi9vbi5qc1wiO1xuaW1wb3J0IHsgQmFja29mZiB9IGZyb20gXCIuL2NvbnRyaWIvYmFja28yLmpzXCI7XG5pbXBvcnQgeyBFbWl0dGVyLCB9IGZyb20gXCJAc29ja2V0LmlvL2NvbXBvbmVudC1lbWl0dGVyXCI7XG5pbXBvcnQgZGVidWdNb2R1bGUgZnJvbSBcImRlYnVnXCI7IC8vIGRlYnVnKClcbmNvbnN0IGRlYnVnID0gZGVidWdNb2R1bGUoXCJzb2NrZXQuaW8tY2xpZW50Om1hbmFnZXJcIik7IC8vIGRlYnVnKClcbmV4cG9ydCBjbGFzcyBNYW5hZ2VyIGV4dGVuZHMgRW1pdHRlciB7XG4gICAgY29uc3RydWN0b3IodXJpLCBvcHRzKSB7XG4gICAgICAgIHZhciBfYTtcbiAgICAgICAgc3VwZXIoKTtcbiAgICAgICAgdGhpcy5uc3BzID0ge307XG4gICAgICAgIHRoaXMuc3VicyA9IFtdO1xuICAgICAgICBpZiAodXJpICYmIFwib2JqZWN0XCIgPT09IHR5cGVvZiB1cmkpIHtcbiAgICAgICAgICAgIG9wdHMgPSB1cmk7XG4gICAgICAgICAgICB1cmkgPSB1bmRlZmluZWQ7XG4gICAgICAgIH1cbiAgICAgICAgb3B0cyA9IG9wdHMgfHwge307XG4gICAgICAgIG9wdHMucGF0aCA9IG9wdHMucGF0aCB8fCBcIi9zb2NrZXQuaW9cIjtcbiAgICAgICAgdGhpcy5vcHRzID0gb3B0cztcbiAgICAgICAgaW5zdGFsbFRpbWVyRnVuY3Rpb25zKHRoaXMsIG9wdHMpO1xuICAgICAgICB0aGlzLnJlY29ubmVjdGlvbihvcHRzLnJlY29ubmVjdGlvbiAhPT0gZmFsc2UpO1xuICAgICAgICB0aGlzLnJlY29ubmVjdGlvbkF0dGVtcHRzKG9wdHMucmVjb25uZWN0aW9uQXR0ZW1wdHMgfHwgSW5maW5pdHkpO1xuICAgICAgICB0aGlzLnJlY29ubmVjdGlvbkRlbGF5KG9wdHMucmVjb25uZWN0aW9uRGVsYXkgfHwgMTAwMCk7XG4gICAgICAgIHRoaXMucmVjb25uZWN0aW9uRGVsYXlNYXgob3B0cy5yZWNvbm5lY3Rpb25EZWxheU1heCB8fCA1MDAwKTtcbiAgICAgICAgdGhpcy5yYW5kb21pemF0aW9uRmFjdG9yKChfYSA9IG9wdHMucmFuZG9taXphdGlvbkZhY3RvcikgIT09IG51bGwgJiYgX2EgIT09IHZvaWQgMCA/IF9hIDogMC41KTtcbiAgICAgICAgdGhpcy5iYWNrb2ZmID0gbmV3IEJhY2tvZmYoe1xuICAgICAgICAgICAgbWluOiB0aGlzLnJlY29ubmVjdGlvbkRlbGF5KCksXG4gICAgICAgICAgICBtYXg6IHRoaXMucmVjb25uZWN0aW9uRGVsYXlNYXgoKSxcbiAgICAgICAgICAgIGppdHRlcjogdGhpcy5yYW5kb21pemF0aW9uRmFjdG9yKCksXG4gICAgICAgIH0pO1xuICAgICAgICB0aGlzLnRpbWVvdXQobnVsbCA9PSBvcHRzLnRpbWVvdXQgPyAyMDAwMCA6IG9wdHMudGltZW91dCk7XG4gICAgICAgIHRoaXMuX3JlYWR5U3RhdGUgPSBcImNsb3NlZFwiO1xuICAgICAgICB0aGlzLnVyaSA9IHVyaTtcbiAgICAgICAgY29uc3QgX3BhcnNlciA9IG9wdHMucGFyc2VyIHx8IHBhcnNlcjtcbiAgICAgICAgdGhpcy5lbmNvZGVyID0gbmV3IF9wYXJzZXIuRW5jb2RlcigpO1xuICAgICAgICB0aGlzLmRlY29kZXIgPSBuZXcgX3BhcnNlci5EZWNvZGVyKCk7XG4gICAgICAgIHRoaXMuX2F1dG9Db25uZWN0ID0gb3B0cy5hdXRvQ29ubmVjdCAhPT0gZmFsc2U7XG4gICAgICAgIGlmICh0aGlzLl9hdXRvQ29ubmVjdClcbiAgICAgICAgICAgIHRoaXMub3BlbigpO1xuICAgIH1cbiAgICByZWNvbm5lY3Rpb24odikge1xuICAgICAgICBpZiAoIWFyZ3VtZW50cy5sZW5ndGgpXG4gICAgICAgICAgICByZXR1cm4gdGhpcy5fcmVjb25uZWN0aW9uO1xuICAgICAgICB0aGlzLl9yZWNvbm5lY3Rpb24gPSAhIXY7XG4gICAgICAgIGlmICghdikge1xuICAgICAgICAgICAgdGhpcy5za2lwUmVjb25uZWN0ID0gdHJ1ZTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gdGhpcztcbiAgICB9XG4gICAgcmVjb25uZWN0aW9uQXR0ZW1wdHModikge1xuICAgICAgICBpZiAodiA9PT0gdW5kZWZpbmVkKVxuICAgICAgICAgICAgcmV0dXJuIHRoaXMuX3JlY29ubmVjdGlvbkF0dGVtcHRzO1xuICAgICAgICB0aGlzLl9yZWNvbm5lY3Rpb25BdHRlbXB0cyA9IHY7XG4gICAgICAgIHJldHVybiB0aGlzO1xuICAgIH1cbiAgICByZWNvbm5lY3Rpb25EZWxheSh2KSB7XG4gICAgICAgIHZhciBfYTtcbiAgICAgICAgaWYgKHYgPT09IHVuZGVmaW5lZClcbiAgICAgICAgICAgIHJldHVybiB0aGlzLl9yZWNvbm5lY3Rpb25EZWxheTtcbiAgICAgICAgdGhpcy5fcmVjb25uZWN0aW9uRGVsYXkgPSB2O1xuICAgICAgICAoX2EgPSB0aGlzLmJhY2tvZmYpID09PSBudWxsIHx8IF9hID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfYS5zZXRNaW4odik7XG4gICAgICAgIHJldHVybiB0aGlzO1xuICAgIH1cbiAgICByYW5kb21pemF0aW9uRmFjdG9yKHYpIHtcbiAgICAgICAgdmFyIF9hO1xuICAgICAgICBpZiAodiA9PT0gdW5kZWZpbmVkKVxuICAgICAgICAgICAgcmV0dXJuIHRoaXMuX3JhbmRvbWl6YXRpb25GYWN0b3I7XG4gICAgICAgIHRoaXMuX3JhbmRvbWl6YXRpb25GYWN0b3IgPSB2O1xuICAgICAgICAoX2EgPSB0aGlzLmJhY2tvZmYpID09PSBudWxsIHx8IF9hID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfYS5zZXRKaXR0ZXIodik7XG4gICAgICAgIHJldHVybiB0aGlzO1xuICAgIH1cbiAgICByZWNvbm5lY3Rpb25EZWxheU1heCh2KSB7XG4gICAgICAgIHZhciBfYTtcbiAgICAgICAgaWYgKHYgPT09IHVuZGVmaW5lZClcbiAgICAgICAgICAgIHJldHVybiB0aGlzLl9yZWNvbm5lY3Rpb25EZWxheU1heDtcbiAgICAgICAgdGhpcy5fcmVjb25uZWN0aW9uRGVsYXlNYXggPSB2O1xuICAgICAgICAoX2EgPSB0aGlzLmJhY2tvZmYpID09PSBudWxsIHx8IF9hID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfYS5zZXRNYXgodik7XG4gICAgICAgIHJldHVybiB0aGlzO1xuICAgIH1cbiAgICB0aW1lb3V0KHYpIHtcbiAgICAgICAgaWYgKCFhcmd1bWVudHMubGVuZ3RoKVxuICAgICAgICAgICAgcmV0dXJuIHRoaXMuX3RpbWVvdXQ7XG4gICAgICAgIHRoaXMuX3RpbWVvdXQgPSB2O1xuICAgICAgICByZXR1cm4gdGhpcztcbiAgICB9XG4gICAgLyoqXG4gICAgICogU3RhcnRzIHRyeWluZyB0byByZWNvbm5lY3QgaWYgcmVjb25uZWN0aW9uIGlzIGVuYWJsZWQgYW5kIHdlIGhhdmUgbm90XG4gICAgICogc3RhcnRlZCByZWNvbm5lY3RpbmcgeWV0XG4gICAgICpcbiAgICAgKiBAcHJpdmF0ZVxuICAgICAqL1xuICAgIG1heWJlUmVjb25uZWN0T25PcGVuKCkge1xuICAgICAgICAvLyBPbmx5IHRyeSB0byByZWNvbm5lY3QgaWYgaXQncyB0aGUgZmlyc3QgdGltZSB3ZSdyZSBjb25uZWN0aW5nXG4gICAgICAgIGlmICghdGhpcy5fcmVjb25uZWN0aW5nICYmXG4gICAgICAgICAgICB0aGlzLl9yZWNvbm5lY3Rpb24gJiZcbiAgICAgICAgICAgIHRoaXMuYmFja29mZi5hdHRlbXB0cyA9PT0gMCkge1xuICAgICAgICAgICAgLy8ga2VlcHMgcmVjb25uZWN0aW9uIGZyb20gZmlyaW5nIHR3aWNlIGZvciB0aGUgc2FtZSByZWNvbm5lY3Rpb24gbG9vcFxuICAgICAgICAgICAgdGhpcy5yZWNvbm5lY3QoKTtcbiAgICAgICAgfVxuICAgIH1cbiAgICAvKipcbiAgICAgKiBTZXRzIHRoZSBjdXJyZW50IHRyYW5zcG9ydCBgc29ja2V0YC5cbiAgICAgKlxuICAgICAqIEBwYXJhbSB7RnVuY3Rpb259IGZuIC0gb3B0aW9uYWwsIGNhbGxiYWNrXG4gICAgICogQHJldHVybiBzZWxmXG4gICAgICogQHB1YmxpY1xuICAgICAqL1xuICAgIG9wZW4oZm4pIHtcbiAgICAgICAgZGVidWcoXCJyZWFkeVN0YXRlICVzXCIsIHRoaXMuX3JlYWR5U3RhdGUpO1xuICAgICAgICBpZiAofnRoaXMuX3JlYWR5U3RhdGUuaW5kZXhPZihcIm9wZW5cIikpXG4gICAgICAgICAgICByZXR1cm4gdGhpcztcbiAgICAgICAgZGVidWcoXCJvcGVuaW5nICVzXCIsIHRoaXMudXJpKTtcbiAgICAgICAgdGhpcy5lbmdpbmUgPSBuZXcgRW5naW5lKHRoaXMudXJpLCB0aGlzLm9wdHMpO1xuICAgICAgICBjb25zdCBzb2NrZXQgPSB0aGlzLmVuZ2luZTtcbiAgICAgICAgY29uc3Qgc2VsZiA9IHRoaXM7XG4gICAgICAgIHRoaXMuX3JlYWR5U3RhdGUgPSBcIm9wZW5pbmdcIjtcbiAgICAgICAgdGhpcy5za2lwUmVjb25uZWN0ID0gZmFsc2U7XG4gICAgICAgIC8vIGVtaXQgYG9wZW5gXG4gICAgICAgIGNvbnN0IG9wZW5TdWJEZXN0cm95ID0gb24oc29ja2V0LCBcIm9wZW5cIiwgZnVuY3Rpb24gKCkge1xuICAgICAgICAgICAgc2VsZi5vbm9wZW4oKTtcbiAgICAgICAgICAgIGZuICYmIGZuKCk7XG4gICAgICAgIH0pO1xuICAgICAgICBjb25zdCBvbkVycm9yID0gKGVycikgPT4ge1xuICAgICAgICAgICAgZGVidWcoXCJlcnJvclwiKTtcbiAgICAgICAgICAgIHRoaXMuY2xlYW51cCgpO1xuICAgICAgICAgICAgdGhpcy5fcmVhZHlTdGF0ZSA9IFwiY2xvc2VkXCI7XG4gICAgICAgICAgICB0aGlzLmVtaXRSZXNlcnZlZChcImVycm9yXCIsIGVycik7XG4gICAgICAgICAgICBpZiAoZm4pIHtcbiAgICAgICAgICAgICAgICBmbihlcnIpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgLy8gT25seSBkbyB0aGlzIGlmIHRoZXJlIGlzIG5vIGZuIHRvIGhhbmRsZSB0aGUgZXJyb3JcbiAgICAgICAgICAgICAgICB0aGlzLm1heWJlUmVjb25uZWN0T25PcGVuKCk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH07XG4gICAgICAgIC8vIGVtaXQgYGVycm9yYFxuICAgICAgICBjb25zdCBlcnJvclN1YiA9IG9uKHNvY2tldCwgXCJlcnJvclwiLCBvbkVycm9yKTtcbiAgICAgICAgaWYgKGZhbHNlICE9PSB0aGlzLl90aW1lb3V0KSB7XG4gICAgICAgICAgICBjb25zdCB0aW1lb3V0ID0gdGhpcy5fdGltZW91dDtcbiAgICAgICAgICAgIGRlYnVnKFwiY29ubmVjdCBhdHRlbXB0IHdpbGwgdGltZW91dCBhZnRlciAlZFwiLCB0aW1lb3V0KTtcbiAgICAgICAgICAgIC8vIHNldCB0aW1lclxuICAgICAgICAgICAgY29uc3QgdGltZXIgPSB0aGlzLnNldFRpbWVvdXRGbigoKSA9PiB7XG4gICAgICAgICAgICAgICAgZGVidWcoXCJjb25uZWN0IGF0dGVtcHQgdGltZWQgb3V0IGFmdGVyICVkXCIsIHRpbWVvdXQpO1xuICAgICAgICAgICAgICAgIG9wZW5TdWJEZXN0cm95KCk7XG4gICAgICAgICAgICAgICAgb25FcnJvcihuZXcgRXJyb3IoXCJ0aW1lb3V0XCIpKTtcbiAgICAgICAgICAgICAgICBzb2NrZXQuY2xvc2UoKTtcbiAgICAgICAgICAgIH0sIHRpbWVvdXQpO1xuICAgICAgICAgICAgaWYgKHRoaXMub3B0cy5hdXRvVW5yZWYpIHtcbiAgICAgICAgICAgICAgICB0aW1lci51bnJlZigpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgdGhpcy5zdWJzLnB1c2goKCkgPT4ge1xuICAgICAgICAgICAgICAgIHRoaXMuY2xlYXJUaW1lb3V0Rm4odGltZXIpO1xuICAgICAgICAgICAgfSk7XG4gICAgICAgIH1cbiAgICAgICAgdGhpcy5zdWJzLnB1c2gob3BlblN1YkRlc3Ryb3kpO1xuICAgICAgICB0aGlzLnN1YnMucHVzaChlcnJvclN1Yik7XG4gICAgICAgIHJldHVybiB0aGlzO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBBbGlhcyBmb3Igb3BlbigpXG4gICAgICpcbiAgICAgKiBAcmV0dXJuIHNlbGZcbiAgICAgKiBAcHVibGljXG4gICAgICovXG4gICAgY29ubmVjdChmbikge1xuICAgICAgICByZXR1cm4gdGhpcy5vcGVuKGZuKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogQ2FsbGVkIHVwb24gdHJhbnNwb3J0IG9wZW4uXG4gICAgICpcbiAgICAgKiBAcHJpdmF0ZVxuICAgICAqL1xuICAgIG9ub3BlbigpIHtcbiAgICAgICAgZGVidWcoXCJvcGVuXCIpO1xuICAgICAgICAvLyBjbGVhciBvbGQgc3Vic1xuICAgICAgICB0aGlzLmNsZWFudXAoKTtcbiAgICAgICAgLy8gbWFyayBhcyBvcGVuXG4gICAgICAgIHRoaXMuX3JlYWR5U3RhdGUgPSBcIm9wZW5cIjtcbiAgICAgICAgdGhpcy5lbWl0UmVzZXJ2ZWQoXCJvcGVuXCIpO1xuICAgICAgICAvLyBhZGQgbmV3IHN1YnNcbiAgICAgICAgY29uc3Qgc29ja2V0ID0gdGhpcy5lbmdpbmU7XG4gICAgICAgIHRoaXMuc3Vicy5wdXNoKG9uKHNvY2tldCwgXCJwaW5nXCIsIHRoaXMub25waW5nLmJpbmQodGhpcykpLCBvbihzb2NrZXQsIFwiZGF0YVwiLCB0aGlzLm9uZGF0YS5iaW5kKHRoaXMpKSwgb24oc29ja2V0LCBcImVycm9yXCIsIHRoaXMub25lcnJvci5iaW5kKHRoaXMpKSwgb24oc29ja2V0LCBcImNsb3NlXCIsIHRoaXMub25jbG9zZS5iaW5kKHRoaXMpKSwgXG4gICAgICAgIC8vIEB0cy1pZ25vcmVcbiAgICAgICAgb24odGhpcy5kZWNvZGVyLCBcImRlY29kZWRcIiwgdGhpcy5vbmRlY29kZWQuYmluZCh0aGlzKSkpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBDYWxsZWQgdXBvbiBhIHBpbmcuXG4gICAgICpcbiAgICAgKiBAcHJpdmF0ZVxuICAgICAqL1xuICAgIG9ucGluZygpIHtcbiAgICAgICAgdGhpcy5lbWl0UmVzZXJ2ZWQoXCJwaW5nXCIpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBDYWxsZWQgd2l0aCBkYXRhLlxuICAgICAqXG4gICAgICogQHByaXZhdGVcbiAgICAgKi9cbiAgICBvbmRhdGEoZGF0YSkge1xuICAgICAgICB0cnkge1xuICAgICAgICAgICAgdGhpcy5kZWNvZGVyLmFkZChkYXRhKTtcbiAgICAgICAgfVxuICAgICAgICBjYXRjaCAoZSkge1xuICAgICAgICAgICAgdGhpcy5vbmNsb3NlKFwicGFyc2UgZXJyb3JcIiwgZSk7XG4gICAgICAgIH1cbiAgICB9XG4gICAgLyoqXG4gICAgICogQ2FsbGVkIHdoZW4gcGFyc2VyIGZ1bGx5IGRlY29kZXMgYSBwYWNrZXQuXG4gICAgICpcbiAgICAgKiBAcHJpdmF0ZVxuICAgICAqL1xuICAgIG9uZGVjb2RlZChwYWNrZXQpIHtcbiAgICAgICAgLy8gdGhlIG5leHRUaWNrIGNhbGwgcHJldmVudHMgYW4gZXhjZXB0aW9uIGluIGEgdXNlci1wcm92aWRlZCBldmVudCBsaXN0ZW5lciBmcm9tIHRyaWdnZXJpbmcgYSBkaXNjb25uZWN0aW9uIGR1ZSB0byBhIFwicGFyc2UgZXJyb3JcIlxuICAgICAgICBuZXh0VGljaygoKSA9PiB7XG4gICAgICAgICAgICB0aGlzLmVtaXRSZXNlcnZlZChcInBhY2tldFwiLCBwYWNrZXQpO1xuICAgICAgICB9LCB0aGlzLnNldFRpbWVvdXRGbik7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIENhbGxlZCB1cG9uIHNvY2tldCBlcnJvci5cbiAgICAgKlxuICAgICAqIEBwcml2YXRlXG4gICAgICovXG4gICAgb25lcnJvcihlcnIpIHtcbiAgICAgICAgZGVidWcoXCJlcnJvclwiLCBlcnIpO1xuICAgICAgICB0aGlzLmVtaXRSZXNlcnZlZChcImVycm9yXCIsIGVycik7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIENyZWF0ZXMgYSBuZXcgc29ja2V0IGZvciB0aGUgZ2l2ZW4gYG5zcGAuXG4gICAgICpcbiAgICAgKiBAcmV0dXJuIHtTb2NrZXR9XG4gICAgICogQHB1YmxpY1xuICAgICAqL1xuICAgIHNvY2tldChuc3AsIG9wdHMpIHtcbiAgICAgICAgbGV0IHNvY2tldCA9IHRoaXMubnNwc1tuc3BdO1xuICAgICAgICBpZiAoIXNvY2tldCkge1xuICAgICAgICAgICAgc29ja2V0ID0gbmV3IFNvY2tldCh0aGlzLCBuc3AsIG9wdHMpO1xuICAgICAgICAgICAgdGhpcy5uc3BzW25zcF0gPSBzb2NrZXQ7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSBpZiAodGhpcy5fYXV0b0Nvbm5lY3QgJiYgIXNvY2tldC5hY3RpdmUpIHtcbiAgICAgICAgICAgIHNvY2tldC5jb25uZWN0KCk7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHNvY2tldDtcbiAgICB9XG4gICAgLyoqXG4gICAgICogQ2FsbGVkIHVwb24gYSBzb2NrZXQgY2xvc2UuXG4gICAgICpcbiAgICAgKiBAcGFyYW0gc29ja2V0XG4gICAgICogQHByaXZhdGVcbiAgICAgKi9cbiAgICBfZGVzdHJveShzb2NrZXQpIHtcbiAgICAgICAgY29uc3QgbnNwcyA9IE9iamVjdC5rZXlzKHRoaXMubnNwcyk7XG4gICAgICAgIGZvciAoY29uc3QgbnNwIG9mIG5zcHMpIHtcbiAgICAgICAgICAgIGNvbnN0IHNvY2tldCA9IHRoaXMubnNwc1tuc3BdO1xuICAgICAgICAgICAgaWYgKHNvY2tldC5hY3RpdmUpIHtcbiAgICAgICAgICAgICAgICBkZWJ1ZyhcInNvY2tldCAlcyBpcyBzdGlsbCBhY3RpdmUsIHNraXBwaW5nIGNsb3NlXCIsIG5zcCk7XG4gICAgICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIHRoaXMuX2Nsb3NlKCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFdyaXRlcyBhIHBhY2tldC5cbiAgICAgKlxuICAgICAqIEBwYXJhbSBwYWNrZXRcbiAgICAgKiBAcHJpdmF0ZVxuICAgICAqL1xuICAgIF9wYWNrZXQocGFja2V0KSB7XG4gICAgICAgIGRlYnVnKFwid3JpdGluZyBwYWNrZXQgJWpcIiwgcGFja2V0KTtcbiAgICAgICAgY29uc3QgZW5jb2RlZFBhY2tldHMgPSB0aGlzLmVuY29kZXIuZW5jb2RlKHBhY2tldCk7XG4gICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgZW5jb2RlZFBhY2tldHMubGVuZ3RoOyBpKyspIHtcbiAgICAgICAgICAgIHRoaXMuZW5naW5lLndyaXRlKGVuY29kZWRQYWNrZXRzW2ldLCBwYWNrZXQub3B0aW9ucyk7XG4gICAgICAgIH1cbiAgICB9XG4gICAgLyoqXG4gICAgICogQ2xlYW4gdXAgdHJhbnNwb3J0IHN1YnNjcmlwdGlvbnMgYW5kIHBhY2tldCBidWZmZXIuXG4gICAgICpcbiAgICAgKiBAcHJpdmF0ZVxuICAgICAqL1xuICAgIGNsZWFudXAoKSB7XG4gICAgICAgIGRlYnVnKFwiY2xlYW51cFwiKTtcbiAgICAgICAgdGhpcy5zdWJzLmZvckVhY2goKHN1YkRlc3Ryb3kpID0+IHN1YkRlc3Ryb3koKSk7XG4gICAgICAgIHRoaXMuc3Vicy5sZW5ndGggPSAwO1xuICAgICAgICB0aGlzLmRlY29kZXIuZGVzdHJveSgpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBDbG9zZSB0aGUgY3VycmVudCBzb2NrZXQuXG4gICAgICpcbiAgICAgKiBAcHJpdmF0ZVxuICAgICAqL1xuICAgIF9jbG9zZSgpIHtcbiAgICAgICAgZGVidWcoXCJkaXNjb25uZWN0XCIpO1xuICAgICAgICB0aGlzLnNraXBSZWNvbm5lY3QgPSB0cnVlO1xuICAgICAgICB0aGlzLl9yZWNvbm5lY3RpbmcgPSBmYWxzZTtcbiAgICAgICAgdGhpcy5vbmNsb3NlKFwiZm9yY2VkIGNsb3NlXCIpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBBbGlhcyBmb3IgY2xvc2UoKVxuICAgICAqXG4gICAgICogQHByaXZhdGVcbiAgICAgKi9cbiAgICBkaXNjb25uZWN0KCkge1xuICAgICAgICByZXR1cm4gdGhpcy5fY2xvc2UoKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogQ2FsbGVkIHdoZW46XG4gICAgICpcbiAgICAgKiAtIHRoZSBsb3ctbGV2ZWwgZW5naW5lIGlzIGNsb3NlZFxuICAgICAqIC0gdGhlIHBhcnNlciBlbmNvdW50ZXJlZCBhIGJhZGx5IGZvcm1hdHRlZCBwYWNrZXRcbiAgICAgKiAtIGFsbCBzb2NrZXRzIGFyZSBkaXNjb25uZWN0ZWRcbiAgICAgKlxuICAgICAqIEBwcml2YXRlXG4gICAgICovXG4gICAgb25jbG9zZShyZWFzb24sIGRlc2NyaXB0aW9uKSB7XG4gICAgICAgIHZhciBfYTtcbiAgICAgICAgZGVidWcoXCJjbG9zZWQgZHVlIHRvICVzXCIsIHJlYXNvbik7XG4gICAgICAgIHRoaXMuY2xlYW51cCgpO1xuICAgICAgICAoX2EgPSB0aGlzLmVuZ2luZSkgPT09IG51bGwgfHwgX2EgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9hLmNsb3NlKCk7XG4gICAgICAgIHRoaXMuYmFja29mZi5yZXNldCgpO1xuICAgICAgICB0aGlzLl9yZWFkeVN0YXRlID0gXCJjbG9zZWRcIjtcbiAgICAgICAgdGhpcy5lbWl0UmVzZXJ2ZWQoXCJjbG9zZVwiLCByZWFzb24sIGRlc2NyaXB0aW9uKTtcbiAgICAgICAgaWYgKHRoaXMuX3JlY29ubmVjdGlvbiAmJiAhdGhpcy5za2lwUmVjb25uZWN0KSB7XG4gICAgICAgICAgICB0aGlzLnJlY29ubmVjdCgpO1xuICAgICAgICB9XG4gICAgfVxuICAgIC8qKlxuICAgICAqIEF0dGVtcHQgYSByZWNvbm5lY3Rpb24uXG4gICAgICpcbiAgICAgKiBAcHJpdmF0ZVxuICAgICAqL1xuICAgIHJlY29ubmVjdCgpIHtcbiAgICAgICAgaWYgKHRoaXMuX3JlY29ubmVjdGluZyB8fCB0aGlzLnNraXBSZWNvbm5lY3QpXG4gICAgICAgICAgICByZXR1cm4gdGhpcztcbiAgICAgICAgY29uc3Qgc2VsZiA9IHRoaXM7XG4gICAgICAgIGlmICh0aGlzLmJhY2tvZmYuYXR0ZW1wdHMgPj0gdGhpcy5fcmVjb25uZWN0aW9uQXR0ZW1wdHMpIHtcbiAgICAgICAgICAgIGRlYnVnKFwicmVjb25uZWN0IGZhaWxlZFwiKTtcbiAgICAgICAgICAgIHRoaXMuYmFja29mZi5yZXNldCgpO1xuICAgICAgICAgICAgdGhpcy5lbWl0UmVzZXJ2ZWQoXCJyZWNvbm5lY3RfZmFpbGVkXCIpO1xuICAgICAgICAgICAgdGhpcy5fcmVjb25uZWN0aW5nID0gZmFsc2U7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICBjb25zdCBkZWxheSA9IHRoaXMuYmFja29mZi5kdXJhdGlvbigpO1xuICAgICAgICAgICAgZGVidWcoXCJ3aWxsIHdhaXQgJWRtcyBiZWZvcmUgcmVjb25uZWN0IGF0dGVtcHRcIiwgZGVsYXkpO1xuICAgICAgICAgICAgdGhpcy5fcmVjb25uZWN0aW5nID0gdHJ1ZTtcbiAgICAgICAgICAgIGNvbnN0IHRpbWVyID0gdGhpcy5zZXRUaW1lb3V0Rm4oKCkgPT4ge1xuICAgICAgICAgICAgICAgIGlmIChzZWxmLnNraXBSZWNvbm5lY3QpXG4gICAgICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgICAgICBkZWJ1ZyhcImF0dGVtcHRpbmcgcmVjb25uZWN0XCIpO1xuICAgICAgICAgICAgICAgIHRoaXMuZW1pdFJlc2VydmVkKFwicmVjb25uZWN0X2F0dGVtcHRcIiwgc2VsZi5iYWNrb2ZmLmF0dGVtcHRzKTtcbiAgICAgICAgICAgICAgICAvLyBjaGVjayBhZ2FpbiBmb3IgdGhlIGNhc2Ugc29ja2V0IGNsb3NlZCBpbiBhYm92ZSBldmVudHNcbiAgICAgICAgICAgICAgICBpZiAoc2VsZi5za2lwUmVjb25uZWN0KVxuICAgICAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICAgICAgc2VsZi5vcGVuKChlcnIpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgaWYgKGVycikge1xuICAgICAgICAgICAgICAgICAgICAgICAgZGVidWcoXCJyZWNvbm5lY3QgYXR0ZW1wdCBlcnJvclwiKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIHNlbGYuX3JlY29ubmVjdGluZyA9IGZhbHNlO1xuICAgICAgICAgICAgICAgICAgICAgICAgc2VsZi5yZWNvbm5lY3QoKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuZW1pdFJlc2VydmVkKFwicmVjb25uZWN0X2Vycm9yXCIsIGVycik7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBkZWJ1ZyhcInJlY29ubmVjdCBzdWNjZXNzXCIpO1xuICAgICAgICAgICAgICAgICAgICAgICAgc2VsZi5vbnJlY29ubmVjdCgpO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICB9LCBkZWxheSk7XG4gICAgICAgICAgICBpZiAodGhpcy5vcHRzLmF1dG9VbnJlZikge1xuICAgICAgICAgICAgICAgIHRpbWVyLnVucmVmKCk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICB0aGlzLnN1YnMucHVzaCgoKSA9PiB7XG4gICAgICAgICAgICAgICAgdGhpcy5jbGVhclRpbWVvdXRGbih0aW1lcik7XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgfVxuICAgIH1cbiAgICAvKipcbiAgICAgKiBDYWxsZWQgdXBvbiBzdWNjZXNzZnVsIHJlY29ubmVjdC5cbiAgICAgKlxuICAgICAqIEBwcml2YXRlXG4gICAgICovXG4gICAgb25yZWNvbm5lY3QoKSB7XG4gICAgICAgIGNvbnN0IGF0dGVtcHQgPSB0aGlzLmJhY2tvZmYuYXR0ZW1wdHM7XG4gICAgICAgIHRoaXMuX3JlY29ubmVjdGluZyA9IGZhbHNlO1xuICAgICAgICB0aGlzLmJhY2tvZmYucmVzZXQoKTtcbiAgICAgICAgdGhpcy5lbWl0UmVzZXJ2ZWQoXCJyZWNvbm5lY3RcIiwgYXR0ZW1wdCk7XG4gICAgfVxufVxuIl0sIm5hbWVzIjpbIlNvY2tldCIsIkVuZ2luZSIsImluc3RhbGxUaW1lckZ1bmN0aW9ucyIsIm5leHRUaWNrIiwicGFyc2VyIiwib24iLCJCYWNrb2ZmIiwiRW1pdHRlciIsImRlYnVnTW9kdWxlIiwiZGVidWciLCJNYW5hZ2VyIiwiY29uc3RydWN0b3IiLCJ1cmkiLCJvcHRzIiwiX2EiLCJuc3BzIiwic3VicyIsInVuZGVmaW5lZCIsInBhdGgiLCJyZWNvbm5lY3Rpb24iLCJyZWNvbm5lY3Rpb25BdHRlbXB0cyIsIkluZmluaXR5IiwicmVjb25uZWN0aW9uRGVsYXkiLCJyZWNvbm5lY3Rpb25EZWxheU1heCIsInJhbmRvbWl6YXRpb25GYWN0b3IiLCJiYWNrb2ZmIiwibWluIiwibWF4Iiwiaml0dGVyIiwidGltZW91dCIsIl9yZWFkeVN0YXRlIiwiX3BhcnNlciIsImVuY29kZXIiLCJFbmNvZGVyIiwiZGVjb2RlciIsIkRlY29kZXIiLCJfYXV0b0Nvbm5lY3QiLCJhdXRvQ29ubmVjdCIsIm9wZW4iLCJ2IiwiYXJndW1lbnRzIiwibGVuZ3RoIiwiX3JlY29ubmVjdGlvbiIsInNraXBSZWNvbm5lY3QiLCJfcmVjb25uZWN0aW9uQXR0ZW1wdHMiLCJfcmVjb25uZWN0aW9uRGVsYXkiLCJzZXRNaW4iLCJfcmFuZG9taXphdGlvbkZhY3RvciIsInNldEppdHRlciIsIl9yZWNvbm5lY3Rpb25EZWxheU1heCIsInNldE1heCIsIl90aW1lb3V0IiwibWF5YmVSZWNvbm5lY3RPbk9wZW4iLCJfcmVjb25uZWN0aW5nIiwiYXR0ZW1wdHMiLCJyZWNvbm5lY3QiLCJmbiIsImluZGV4T2YiLCJlbmdpbmUiLCJzb2NrZXQiLCJzZWxmIiwib3BlblN1YkRlc3Ryb3kiLCJvbm9wZW4iLCJvbkVycm9yIiwiZXJyIiwiY2xlYW51cCIsImVtaXRSZXNlcnZlZCIsImVycm9yU3ViIiwidGltZXIiLCJzZXRUaW1lb3V0Rm4iLCJFcnJvciIsImNsb3NlIiwiYXV0b1VucmVmIiwidW5yZWYiLCJwdXNoIiwiY2xlYXJUaW1lb3V0Rm4iLCJjb25uZWN0Iiwib25waW5nIiwiYmluZCIsIm9uZGF0YSIsIm9uZXJyb3IiLCJvbmNsb3NlIiwib25kZWNvZGVkIiwiZGF0YSIsImFkZCIsImUiLCJwYWNrZXQiLCJuc3AiLCJhY3RpdmUiLCJfZGVzdHJveSIsIk9iamVjdCIsImtleXMiLCJfY2xvc2UiLCJfcGFja2V0IiwiZW5jb2RlZFBhY2tldHMiLCJlbmNvZGUiLCJpIiwid3JpdGUiLCJvcHRpb25zIiwiZm9yRWFjaCIsInN1YkRlc3Ryb3kiLCJkZXN0cm95IiwiZGlzY29ubmVjdCIsInJlYXNvbiIsImRlc2NyaXB0aW9uIiwicmVzZXQiLCJkZWxheSIsImR1cmF0aW9uIiwib25yZWNvbm5lY3QiLCJhdHRlbXB0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/socket.io-client/build/esm-debug/manager.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/socket.io-client/build/esm-debug/on.js":
/*!*************************************************************!*\
  !*** ./node_modules/socket.io-client/build/esm-debug/on.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   on: () => (/* binding */ on)\n/* harmony export */ });\nfunction on(obj, ev, fn) {\n    obj.on(ev, fn);\n    return function subDestroy() {\n        obj.off(ev, fn);\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvc29ja2V0LmlvLWNsaWVudC9idWlsZC9lc20tZGVidWcvb24uanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPLFNBQVNBLEdBQUdDLEdBQUcsRUFBRUMsRUFBRSxFQUFFQyxFQUFFO0lBQzFCRixJQUFJRCxFQUFFLENBQUNFLElBQUlDO0lBQ1gsT0FBTyxTQUFTQztRQUNaSCxJQUFJSSxHQUFHLENBQUNILElBQUlDO0lBQ2hCO0FBQ0oiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdXBlcm1hcmtldC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9zb2NrZXQuaW8tY2xpZW50L2J1aWxkL2VzbS1kZWJ1Zy9vbi5qcz9mNDQzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBmdW5jdGlvbiBvbihvYmosIGV2LCBmbikge1xuICAgIG9iai5vbihldiwgZm4pO1xuICAgIHJldHVybiBmdW5jdGlvbiBzdWJEZXN0cm95KCkge1xuICAgICAgICBvYmoub2ZmKGV2LCBmbik7XG4gICAgfTtcbn1cbiJdLCJuYW1lcyI6WyJvbiIsIm9iaiIsImV2IiwiZm4iLCJzdWJEZXN0cm95Iiwib2ZmIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/socket.io-client/build/esm-debug/on.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/socket.io-client/build/esm-debug/socket.js":
/*!*****************************************************************!*\
  !*** ./node_modules/socket.io-client/build/esm-debug/socket.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Socket: () => (/* binding */ Socket)\n/* harmony export */ });\n/* harmony import */ var socket_io_parser__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! socket.io-parser */ \"(ssr)/./node_modules/socket.io-parser/build/esm-debug/index.js\");\n/* harmony import */ var _on_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./on.js */ \"(ssr)/./node_modules/socket.io-client/build/esm-debug/on.js\");\n/* harmony import */ var _socket_io_component_emitter__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @socket.io/component-emitter */ \"(ssr)/./node_modules/@socket.io/component-emitter/lib/esm/index.js\");\n/* harmony import */ var debug__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! debug */ \"(ssr)/./node_modules/socket.io-client/node_modules/debug/src/index.js\");\n\n\n\n // debug()\nconst debug = debug__WEBPACK_IMPORTED_MODULE_3__(\"socket.io-client:socket\"); // debug()\n/**\n * Internal events.\n * These events can't be emitted by the user.\n */ const RESERVED_EVENTS = Object.freeze({\n    connect: 1,\n    connect_error: 1,\n    disconnect: 1,\n    disconnecting: 1,\n    // EventEmitter reserved events: https://nodejs.org/api/events.html#events_event_newlistener\n    newListener: 1,\n    removeListener: 1\n});\n/**\n * A Socket is the fundamental class for interacting with the server.\n *\n * A Socket belongs to a certain Namespace (by default /) and uses an underlying {@link Manager} to communicate.\n *\n * @example\n * const socket = io();\n *\n * socket.on(\"connect\", () => {\n *   console.log(\"connected\");\n * });\n *\n * // send an event to the server\n * socket.emit(\"foo\", \"bar\");\n *\n * socket.on(\"foobar\", () => {\n *   // an event was received from the server\n * });\n *\n * // upon disconnection\n * socket.on(\"disconnect\", (reason) => {\n *   console.log(`disconnected due to ${reason}`);\n * });\n */ class Socket extends _socket_io_component_emitter__WEBPACK_IMPORTED_MODULE_2__.Emitter {\n    /**\n     * `Socket` constructor.\n     */ constructor(io, nsp, opts){\n        super();\n        /**\n         * Whether the socket is currently connected to the server.\n         *\n         * @example\n         * const socket = io();\n         *\n         * socket.on(\"connect\", () => {\n         *   console.log(socket.connected); // true\n         * });\n         *\n         * socket.on(\"disconnect\", () => {\n         *   console.log(socket.connected); // false\n         * });\n         */ this.connected = false;\n        /**\n         * Whether the connection state was recovered after a temporary disconnection. In that case, any missed packets will\n         * be transmitted by the server.\n         */ this.recovered = false;\n        /**\n         * Buffer for packets received before the CONNECT packet\n         */ this.receiveBuffer = [];\n        /**\n         * Buffer for packets that will be sent once the socket is connected\n         */ this.sendBuffer = [];\n        /**\n         * The queue of packets to be sent with retry in case of failure.\n         *\n         * Packets are sent one by one, each waiting for the server acknowledgement, in order to guarantee the delivery order.\n         * @private\n         */ this._queue = [];\n        /**\n         * A sequence to generate the ID of the {@link QueuedPacket}.\n         * @private\n         */ this._queueSeq = 0;\n        this.ids = 0;\n        /**\n         * A map containing acknowledgement handlers.\n         *\n         * The `withError` attribute is used to differentiate handlers that accept an error as first argument:\n         *\n         * - `socket.emit(\"test\", (err, value) => { ... })` with `ackTimeout` option\n         * - `socket.timeout(5000).emit(\"test\", (err, value) => { ... })`\n         * - `const value = await socket.emitWithAck(\"test\")`\n         *\n         * From those that don't:\n         *\n         * - `socket.emit(\"test\", (value) => { ... });`\n         *\n         * In the first case, the handlers will be called with an error when:\n         *\n         * - the timeout is reached\n         * - the socket gets disconnected\n         *\n         * In the second case, the handlers will be simply discarded upon disconnection, since the client will never receive\n         * an acknowledgement from the server.\n         *\n         * @private\n         */ this.acks = {};\n        this.flags = {};\n        this.io = io;\n        this.nsp = nsp;\n        if (opts && opts.auth) {\n            this.auth = opts.auth;\n        }\n        this._opts = Object.assign({}, opts);\n        if (this.io._autoConnect) this.open();\n    }\n    /**\n     * Whether the socket is currently disconnected\n     *\n     * @example\n     * const socket = io();\n     *\n     * socket.on(\"connect\", () => {\n     *   console.log(socket.disconnected); // false\n     * });\n     *\n     * socket.on(\"disconnect\", () => {\n     *   console.log(socket.disconnected); // true\n     * });\n     */ get disconnected() {\n        return !this.connected;\n    }\n    /**\n     * Subscribe to open, close and packet events\n     *\n     * @private\n     */ subEvents() {\n        if (this.subs) return;\n        const io = this.io;\n        this.subs = [\n            (0,_on_js__WEBPACK_IMPORTED_MODULE_1__.on)(io, \"open\", this.onopen.bind(this)),\n            (0,_on_js__WEBPACK_IMPORTED_MODULE_1__.on)(io, \"packet\", this.onpacket.bind(this)),\n            (0,_on_js__WEBPACK_IMPORTED_MODULE_1__.on)(io, \"error\", this.onerror.bind(this)),\n            (0,_on_js__WEBPACK_IMPORTED_MODULE_1__.on)(io, \"close\", this.onclose.bind(this))\n        ];\n    }\n    /**\n     * Whether the Socket will try to reconnect when its Manager connects or reconnects.\n     *\n     * @example\n     * const socket = io();\n     *\n     * console.log(socket.active); // true\n     *\n     * socket.on(\"disconnect\", (reason) => {\n     *   if (reason === \"io server disconnect\") {\n     *     // the disconnection was initiated by the server, you need to manually reconnect\n     *     console.log(socket.active); // false\n     *   }\n     *   // else the socket will automatically try to reconnect\n     *   console.log(socket.active); // true\n     * });\n     */ get active() {\n        return !!this.subs;\n    }\n    /**\n     * \"Opens\" the socket.\n     *\n     * @example\n     * const socket = io({\n     *   autoConnect: false\n     * });\n     *\n     * socket.connect();\n     */ connect() {\n        if (this.connected) return this;\n        this.subEvents();\n        if (!this.io[\"_reconnecting\"]) this.io.open(); // ensure open\n        if (\"open\" === this.io._readyState) this.onopen();\n        return this;\n    }\n    /**\n     * Alias for {@link connect()}.\n     */ open() {\n        return this.connect();\n    }\n    /**\n     * Sends a `message` event.\n     *\n     * This method mimics the WebSocket.send() method.\n     *\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/WebSocket/send\n     *\n     * @example\n     * socket.send(\"hello\");\n     *\n     * // this is equivalent to\n     * socket.emit(\"message\", \"hello\");\n     *\n     * @return self\n     */ send(...args) {\n        args.unshift(\"message\");\n        this.emit.apply(this, args);\n        return this;\n    }\n    /**\n     * Override `emit`.\n     * If the event is in `events`, it's emitted normally.\n     *\n     * @example\n     * socket.emit(\"hello\", \"world\");\n     *\n     * // all serializable datastructures are supported (no need to call JSON.stringify)\n     * socket.emit(\"hello\", 1, \"2\", { 3: [\"4\"], 5: Uint8Array.from([6]) });\n     *\n     * // with an acknowledgement from the server\n     * socket.emit(\"hello\", \"world\", (val) => {\n     *   // ...\n     * });\n     *\n     * @return self\n     */ emit(ev, ...args) {\n        var _a, _b, _c;\n        if (RESERVED_EVENTS.hasOwnProperty(ev)) {\n            throw new Error('\"' + ev.toString() + '\" is a reserved event name');\n        }\n        args.unshift(ev);\n        if (this._opts.retries && !this.flags.fromQueue && !this.flags.volatile) {\n            this._addToQueue(args);\n            return this;\n        }\n        const packet = {\n            type: socket_io_parser__WEBPACK_IMPORTED_MODULE_0__.PacketType.EVENT,\n            data: args\n        };\n        packet.options = {};\n        packet.options.compress = this.flags.compress !== false;\n        // event ack callback\n        if (\"function\" === typeof args[args.length - 1]) {\n            const id = this.ids++;\n            debug(\"emitting packet with ack id %d\", id);\n            const ack = args.pop();\n            this._registerAckCallback(id, ack);\n            packet.id = id;\n        }\n        const isTransportWritable = (_b = (_a = this.io.engine) === null || _a === void 0 ? void 0 : _a.transport) === null || _b === void 0 ? void 0 : _b.writable;\n        const isConnected = this.connected && !((_c = this.io.engine) === null || _c === void 0 ? void 0 : _c._hasPingExpired());\n        const discardPacket = this.flags.volatile && !isTransportWritable;\n        if (discardPacket) {\n            debug(\"discard packet as the transport is not currently writable\");\n        } else if (isConnected) {\n            this.notifyOutgoingListeners(packet);\n            this.packet(packet);\n        } else {\n            this.sendBuffer.push(packet);\n        }\n        this.flags = {};\n        return this;\n    }\n    /**\n     * @private\n     */ _registerAckCallback(id, ack) {\n        var _a;\n        const timeout = (_a = this.flags.timeout) !== null && _a !== void 0 ? _a : this._opts.ackTimeout;\n        if (timeout === undefined) {\n            this.acks[id] = ack;\n            return;\n        }\n        // @ts-ignore\n        const timer = this.io.setTimeoutFn(()=>{\n            delete this.acks[id];\n            for(let i = 0; i < this.sendBuffer.length; i++){\n                if (this.sendBuffer[i].id === id) {\n                    debug(\"removing packet with ack id %d from the buffer\", id);\n                    this.sendBuffer.splice(i, 1);\n                }\n            }\n            debug(\"event with ack id %d has timed out after %d ms\", id, timeout);\n            ack.call(this, new Error(\"operation has timed out\"));\n        }, timeout);\n        const fn = (...args)=>{\n            // @ts-ignore\n            this.io.clearTimeoutFn(timer);\n            ack.apply(this, args);\n        };\n        fn.withError = true;\n        this.acks[id] = fn;\n    }\n    /**\n     * Emits an event and waits for an acknowledgement\n     *\n     * @example\n     * // without timeout\n     * const response = await socket.emitWithAck(\"hello\", \"world\");\n     *\n     * // with a specific timeout\n     * try {\n     *   const response = await socket.timeout(1000).emitWithAck(\"hello\", \"world\");\n     * } catch (err) {\n     *   // the server did not acknowledge the event in the given delay\n     * }\n     *\n     * @return a Promise that will be fulfilled when the server acknowledges the event\n     */ emitWithAck(ev, ...args) {\n        return new Promise((resolve, reject)=>{\n            const fn = (arg1, arg2)=>{\n                return arg1 ? reject(arg1) : resolve(arg2);\n            };\n            fn.withError = true;\n            args.push(fn);\n            this.emit(ev, ...args);\n        });\n    }\n    /**\n     * Add the packet to the queue.\n     * @param args\n     * @private\n     */ _addToQueue(args) {\n        let ack;\n        if (typeof args[args.length - 1] === \"function\") {\n            ack = args.pop();\n        }\n        const packet = {\n            id: this._queueSeq++,\n            tryCount: 0,\n            pending: false,\n            args,\n            flags: Object.assign({\n                fromQueue: true\n            }, this.flags)\n        };\n        args.push((err, ...responseArgs)=>{\n            if (packet !== this._queue[0]) {\n                // the packet has already been acknowledged\n                return;\n            }\n            const hasError = err !== null;\n            if (hasError) {\n                if (packet.tryCount > this._opts.retries) {\n                    debug(\"packet [%d] is discarded after %d tries\", packet.id, packet.tryCount);\n                    this._queue.shift();\n                    if (ack) {\n                        ack(err);\n                    }\n                }\n            } else {\n                debug(\"packet [%d] was successfully sent\", packet.id);\n                this._queue.shift();\n                if (ack) {\n                    ack(null, ...responseArgs);\n                }\n            }\n            packet.pending = false;\n            return this._drainQueue();\n        });\n        this._queue.push(packet);\n        this._drainQueue();\n    }\n    /**\n     * Send the first packet of the queue, and wait for an acknowledgement from the server.\n     * @param force - whether to resend a packet that has not been acknowledged yet\n     *\n     * @private\n     */ _drainQueue(force = false) {\n        debug(\"draining queue\");\n        if (!this.connected || this._queue.length === 0) {\n            return;\n        }\n        const packet = this._queue[0];\n        if (packet.pending && !force) {\n            debug(\"packet [%d] has already been sent and is waiting for an ack\", packet.id);\n            return;\n        }\n        packet.pending = true;\n        packet.tryCount++;\n        debug(\"sending packet [%d] (try n\\xb0%d)\", packet.id, packet.tryCount);\n        this.flags = packet.flags;\n        this.emit.apply(this, packet.args);\n    }\n    /**\n     * Sends a packet.\n     *\n     * @param packet\n     * @private\n     */ packet(packet) {\n        packet.nsp = this.nsp;\n        this.io._packet(packet);\n    }\n    /**\n     * Called upon engine `open`.\n     *\n     * @private\n     */ onopen() {\n        debug(\"transport is open - connecting\");\n        if (typeof this.auth == \"function\") {\n            this.auth((data)=>{\n                this._sendConnectPacket(data);\n            });\n        } else {\n            this._sendConnectPacket(this.auth);\n        }\n    }\n    /**\n     * Sends a CONNECT packet to initiate the Socket.IO session.\n     *\n     * @param data\n     * @private\n     */ _sendConnectPacket(data) {\n        this.packet({\n            type: socket_io_parser__WEBPACK_IMPORTED_MODULE_0__.PacketType.CONNECT,\n            data: this._pid ? Object.assign({\n                pid: this._pid,\n                offset: this._lastOffset\n            }, data) : data\n        });\n    }\n    /**\n     * Called upon engine or manager `error`.\n     *\n     * @param err\n     * @private\n     */ onerror(err) {\n        if (!this.connected) {\n            this.emitReserved(\"connect_error\", err);\n        }\n    }\n    /**\n     * Called upon engine `close`.\n     *\n     * @param reason\n     * @param description\n     * @private\n     */ onclose(reason, description) {\n        debug(\"close (%s)\", reason);\n        this.connected = false;\n        delete this.id;\n        this.emitReserved(\"disconnect\", reason, description);\n        this._clearAcks();\n    }\n    /**\n     * Clears the acknowledgement handlers upon disconnection, since the client will never receive an acknowledgement from\n     * the server.\n     *\n     * @private\n     */ _clearAcks() {\n        Object.keys(this.acks).forEach((id)=>{\n            const isBuffered = this.sendBuffer.some((packet)=>String(packet.id) === id);\n            if (!isBuffered) {\n                // note: handlers that do not accept an error as first argument are ignored here\n                const ack = this.acks[id];\n                delete this.acks[id];\n                if (ack.withError) {\n                    ack.call(this, new Error(\"socket has been disconnected\"));\n                }\n            }\n        });\n    }\n    /**\n     * Called with socket packet.\n     *\n     * @param packet\n     * @private\n     */ onpacket(packet) {\n        const sameNamespace = packet.nsp === this.nsp;\n        if (!sameNamespace) return;\n        switch(packet.type){\n            case socket_io_parser__WEBPACK_IMPORTED_MODULE_0__.PacketType.CONNECT:\n                if (packet.data && packet.data.sid) {\n                    this.onconnect(packet.data.sid, packet.data.pid);\n                } else {\n                    this.emitReserved(\"connect_error\", new Error(\"It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)\"));\n                }\n                break;\n            case socket_io_parser__WEBPACK_IMPORTED_MODULE_0__.PacketType.EVENT:\n            case socket_io_parser__WEBPACK_IMPORTED_MODULE_0__.PacketType.BINARY_EVENT:\n                this.onevent(packet);\n                break;\n            case socket_io_parser__WEBPACK_IMPORTED_MODULE_0__.PacketType.ACK:\n            case socket_io_parser__WEBPACK_IMPORTED_MODULE_0__.PacketType.BINARY_ACK:\n                this.onack(packet);\n                break;\n            case socket_io_parser__WEBPACK_IMPORTED_MODULE_0__.PacketType.DISCONNECT:\n                this.ondisconnect();\n                break;\n            case socket_io_parser__WEBPACK_IMPORTED_MODULE_0__.PacketType.CONNECT_ERROR:\n                this.destroy();\n                const err = new Error(packet.data.message);\n                // @ts-ignore\n                err.data = packet.data.data;\n                this.emitReserved(\"connect_error\", err);\n                break;\n        }\n    }\n    /**\n     * Called upon a server event.\n     *\n     * @param packet\n     * @private\n     */ onevent(packet) {\n        const args = packet.data || [];\n        debug(\"emitting event %j\", args);\n        if (null != packet.id) {\n            debug(\"attaching ack callback to event\");\n            args.push(this.ack(packet.id));\n        }\n        if (this.connected) {\n            this.emitEvent(args);\n        } else {\n            this.receiveBuffer.push(Object.freeze(args));\n        }\n    }\n    emitEvent(args) {\n        if (this._anyListeners && this._anyListeners.length) {\n            const listeners = this._anyListeners.slice();\n            for (const listener of listeners){\n                listener.apply(this, args);\n            }\n        }\n        super.emit.apply(this, args);\n        if (this._pid && args.length && typeof args[args.length - 1] === \"string\") {\n            this._lastOffset = args[args.length - 1];\n        }\n    }\n    /**\n     * Produces an ack callback to emit with an event.\n     *\n     * @private\n     */ ack(id) {\n        const self = this;\n        let sent = false;\n        return function(...args) {\n            // prevent double callbacks\n            if (sent) return;\n            sent = true;\n            debug(\"sending ack %j\", args);\n            self.packet({\n                type: socket_io_parser__WEBPACK_IMPORTED_MODULE_0__.PacketType.ACK,\n                id: id,\n                data: args\n            });\n        };\n    }\n    /**\n     * Called upon a server acknowledgement.\n     *\n     * @param packet\n     * @private\n     */ onack(packet) {\n        const ack = this.acks[packet.id];\n        if (typeof ack !== \"function\") {\n            debug(\"bad ack %s\", packet.id);\n            return;\n        }\n        delete this.acks[packet.id];\n        debug(\"calling ack %s with %j\", packet.id, packet.data);\n        // @ts-ignore FIXME ack is incorrectly inferred as 'never'\n        if (ack.withError) {\n            packet.data.unshift(null);\n        }\n        // @ts-ignore\n        ack.apply(this, packet.data);\n    }\n    /**\n     * Called upon server connect.\n     *\n     * @private\n     */ onconnect(id, pid) {\n        debug(\"socket connected with id %s\", id);\n        this.id = id;\n        this.recovered = pid && this._pid === pid;\n        this._pid = pid; // defined only if connection state recovery is enabled\n        this.connected = true;\n        this.emitBuffered();\n        this.emitReserved(\"connect\");\n        this._drainQueue(true);\n    }\n    /**\n     * Emit buffered events (received and emitted).\n     *\n     * @private\n     */ emitBuffered() {\n        this.receiveBuffer.forEach((args)=>this.emitEvent(args));\n        this.receiveBuffer = [];\n        this.sendBuffer.forEach((packet)=>{\n            this.notifyOutgoingListeners(packet);\n            this.packet(packet);\n        });\n        this.sendBuffer = [];\n    }\n    /**\n     * Called upon server disconnect.\n     *\n     * @private\n     */ ondisconnect() {\n        debug(\"server disconnect (%s)\", this.nsp);\n        this.destroy();\n        this.onclose(\"io server disconnect\");\n    }\n    /**\n     * Called upon forced client/server side disconnections,\n     * this method ensures the manager stops tracking us and\n     * that reconnections don't get triggered for this.\n     *\n     * @private\n     */ destroy() {\n        if (this.subs) {\n            // clean subscriptions to avoid reconnections\n            this.subs.forEach((subDestroy)=>subDestroy());\n            this.subs = undefined;\n        }\n        this.io[\"_destroy\"](this);\n    }\n    /**\n     * Disconnects the socket manually. In that case, the socket will not try to reconnect.\n     *\n     * If this is the last active Socket instance of the {@link Manager}, the low-level connection will be closed.\n     *\n     * @example\n     * const socket = io();\n     *\n     * socket.on(\"disconnect\", (reason) => {\n     *   // console.log(reason); prints \"io client disconnect\"\n     * });\n     *\n     * socket.disconnect();\n     *\n     * @return self\n     */ disconnect() {\n        if (this.connected) {\n            debug(\"performing disconnect (%s)\", this.nsp);\n            this.packet({\n                type: socket_io_parser__WEBPACK_IMPORTED_MODULE_0__.PacketType.DISCONNECT\n            });\n        }\n        // remove socket from pool\n        this.destroy();\n        if (this.connected) {\n            // fire events\n            this.onclose(\"io client disconnect\");\n        }\n        return this;\n    }\n    /**\n     * Alias for {@link disconnect()}.\n     *\n     * @return self\n     */ close() {\n        return this.disconnect();\n    }\n    /**\n     * Sets the compress flag.\n     *\n     * @example\n     * socket.compress(false).emit(\"hello\");\n     *\n     * @param compress - if `true`, compresses the sending data\n     * @return self\n     */ compress(compress) {\n        this.flags.compress = compress;\n        return this;\n    }\n    /**\n     * Sets a modifier for a subsequent event emission that the event message will be dropped when this socket is not\n     * ready to send messages.\n     *\n     * @example\n     * socket.volatile.emit(\"hello\"); // the server may or may not receive it\n     *\n     * @returns self\n     */ get volatile() {\n        this.flags.volatile = true;\n        return this;\n    }\n    /**\n     * Sets a modifier for a subsequent event emission that the callback will be called with an error when the\n     * given number of milliseconds have elapsed without an acknowledgement from the server:\n     *\n     * @example\n     * socket.timeout(5000).emit(\"my-event\", (err) => {\n     *   if (err) {\n     *     // the server did not acknowledge the event in the given delay\n     *   }\n     * });\n     *\n     * @returns self\n     */ timeout(timeout) {\n        this.flags.timeout = timeout;\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback.\n     *\n     * @example\n     * socket.onAny((event, ...args) => {\n     *   console.log(`got ${event}`);\n     * });\n     *\n     * @param listener\n     */ onAny(listener) {\n        this._anyListeners = this._anyListeners || [];\n        this._anyListeners.push(listener);\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback. The listener is added to the beginning of the listeners array.\n     *\n     * @example\n     * socket.prependAny((event, ...args) => {\n     *   console.log(`got event ${event}`);\n     * });\n     *\n     * @param listener\n     */ prependAny(listener) {\n        this._anyListeners = this._anyListeners || [];\n        this._anyListeners.unshift(listener);\n        return this;\n    }\n    /**\n     * Removes the listener that will be fired when any event is emitted.\n     *\n     * @example\n     * const catchAllListener = (event, ...args) => {\n     *   console.log(`got event ${event}`);\n     * }\n     *\n     * socket.onAny(catchAllListener);\n     *\n     * // remove a specific listener\n     * socket.offAny(catchAllListener);\n     *\n     * // or remove all listeners\n     * socket.offAny();\n     *\n     * @param listener\n     */ offAny(listener) {\n        if (!this._anyListeners) {\n            return this;\n        }\n        if (listener) {\n            const listeners = this._anyListeners;\n            for(let i = 0; i < listeners.length; i++){\n                if (listener === listeners[i]) {\n                    listeners.splice(i, 1);\n                    return this;\n                }\n            }\n        } else {\n            this._anyListeners = [];\n        }\n        return this;\n    }\n    /**\n     * Returns an array of listeners that are listening for any event that is specified. This array can be manipulated,\n     * e.g. to remove listeners.\n     */ listenersAny() {\n        return this._anyListeners || [];\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback.\n     *\n     * Note: acknowledgements sent to the server are not included.\n     *\n     * @example\n     * socket.onAnyOutgoing((event, ...args) => {\n     *   console.log(`sent event ${event}`);\n     * });\n     *\n     * @param listener\n     */ onAnyOutgoing(listener) {\n        this._anyOutgoingListeners = this._anyOutgoingListeners || [];\n        this._anyOutgoingListeners.push(listener);\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback. The listener is added to the beginning of the listeners array.\n     *\n     * Note: acknowledgements sent to the server are not included.\n     *\n     * @example\n     * socket.prependAnyOutgoing((event, ...args) => {\n     *   console.log(`sent event ${event}`);\n     * });\n     *\n     * @param listener\n     */ prependAnyOutgoing(listener) {\n        this._anyOutgoingListeners = this._anyOutgoingListeners || [];\n        this._anyOutgoingListeners.unshift(listener);\n        return this;\n    }\n    /**\n     * Removes the listener that will be fired when any event is emitted.\n     *\n     * @example\n     * const catchAllListener = (event, ...args) => {\n     *   console.log(`sent event ${event}`);\n     * }\n     *\n     * socket.onAnyOutgoing(catchAllListener);\n     *\n     * // remove a specific listener\n     * socket.offAnyOutgoing(catchAllListener);\n     *\n     * // or remove all listeners\n     * socket.offAnyOutgoing();\n     *\n     * @param [listener] - the catch-all listener (optional)\n     */ offAnyOutgoing(listener) {\n        if (!this._anyOutgoingListeners) {\n            return this;\n        }\n        if (listener) {\n            const listeners = this._anyOutgoingListeners;\n            for(let i = 0; i < listeners.length; i++){\n                if (listener === listeners[i]) {\n                    listeners.splice(i, 1);\n                    return this;\n                }\n            }\n        } else {\n            this._anyOutgoingListeners = [];\n        }\n        return this;\n    }\n    /**\n     * Returns an array of listeners that are listening for any event that is specified. This array can be manipulated,\n     * e.g. to remove listeners.\n     */ listenersAnyOutgoing() {\n        return this._anyOutgoingListeners || [];\n    }\n    /**\n     * Notify the listeners for each packet sent\n     *\n     * @param packet\n     *\n     * @private\n     */ notifyOutgoingListeners(packet) {\n        if (this._anyOutgoingListeners && this._anyOutgoingListeners.length) {\n            const listeners = this._anyOutgoingListeners.slice();\n            for (const listener of listeners){\n                listener.apply(this, packet.data);\n            }\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/socket.io-client/build/esm-debug/socket.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/socket.io-client/build/esm-debug/url.js":
/*!**************************************************************!*\
  !*** ./node_modules/socket.io-client/build/esm-debug/url.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   url: () => (/* binding */ url)\n/* harmony export */ });\n/* harmony import */ var engine_io_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! engine.io-client */ \"(ssr)/./node_modules/engine.io-client/build/esm-debug/index.js\");\n/* harmony import */ var debug__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! debug */ \"(ssr)/./node_modules/socket.io-client/node_modules/debug/src/index.js\");\n\n // debug()\nconst debug = debug__WEBPACK_IMPORTED_MODULE_1__(\"socket.io-client:url\"); // debug()\n/**\n * URL parser.\n *\n * @param uri - url\n * @param path - the request path of the connection\n * @param loc - An object meant to mimic window.location.\n *        Defaults to window.location.\n * @public\n */ function url(uri, path = \"\", loc) {\n    let obj = uri;\n    // default to window.location\n    loc = loc || typeof location !== \"undefined\" && location;\n    if (null == uri) uri = loc.protocol + \"//\" + loc.host;\n    // relative path support\n    if (typeof uri === \"string\") {\n        if (\"/\" === uri.charAt(0)) {\n            if (\"/\" === uri.charAt(1)) {\n                uri = loc.protocol + uri;\n            } else {\n                uri = loc.host + uri;\n            }\n        }\n        if (!/^(https?|wss?):\\/\\//.test(uri)) {\n            debug(\"protocol-less url %s\", uri);\n            if (\"undefined\" !== typeof loc) {\n                uri = loc.protocol + \"//\" + uri;\n            } else {\n                uri = \"https://\" + uri;\n            }\n        }\n        // parse\n        debug(\"parse %s\", uri);\n        obj = (0,engine_io_client__WEBPACK_IMPORTED_MODULE_0__.parse)(uri);\n    }\n    // make sure we treat `localhost:80` and `localhost` equally\n    if (!obj.port) {\n        if (/^(http|ws)$/.test(obj.protocol)) {\n            obj.port = \"80\";\n        } else if (/^(http|ws)s$/.test(obj.protocol)) {\n            obj.port = \"443\";\n        }\n    }\n    obj.path = obj.path || \"/\";\n    const ipv6 = obj.host.indexOf(\":\") !== -1;\n    const host = ipv6 ? \"[\" + obj.host + \"]\" : obj.host;\n    // define unique id\n    obj.id = obj.protocol + \"://\" + host + \":\" + obj.port + path;\n    // define href\n    obj.href = obj.protocol + \"://\" + host + (loc && loc.port === obj.port ? \"\" : \":\" + obj.port);\n    return obj;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/socket.io-client/build/esm-debug/url.js\n");

/***/ })

};
;