"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui";
exports.ids = ["vendor-chunks/@radix-ui"];
exports.modules = {

/***/ "(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs":
/*!*********************************************************!*\
  !*** ./node_modules/@radix-ui/primitive/dist/index.mjs ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   canUseDOM: () => (/* binding */ canUseDOM),\n/* harmony export */   composeEventHandlers: () => (/* binding */ composeEventHandlers),\n/* harmony export */   getActiveElement: () => (/* binding */ getActiveElement),\n/* harmony export */   getOwnerDocument: () => (/* binding */ getOwnerDocument),\n/* harmony export */   getOwnerWindow: () => (/* binding */ getOwnerWindow),\n/* harmony export */   isFrame: () => (/* binding */ isFrame)\n/* harmony export */ });\n// src/primitive.tsx\nvar canUseDOM = !!( false && 0);\nfunction composeEventHandlers(originalEventHandler, ourEventHandler, { checkForDefaultPrevented = true } = {}) {\n    return function handleEvent(event) {\n        originalEventHandler?.(event);\n        if (checkForDefaultPrevented === false || !event.defaultPrevented) {\n            return ourEventHandler?.(event);\n        }\n    };\n}\nfunction getOwnerWindow(element) {\n    if (!canUseDOM) {\n        throw new Error(\"Cannot access window outside of the DOM\");\n    }\n    return element?.ownerDocument?.defaultView ?? window;\n}\nfunction getOwnerDocument(element) {\n    if (!canUseDOM) {\n        throw new Error(\"Cannot access document outside of the DOM\");\n    }\n    return element?.ownerDocument ?? document;\n}\nfunction getActiveElement(node, activeDescendant = false) {\n    const { activeElement } = getOwnerDocument(node);\n    if (!activeElement?.nodeName) {\n        return null;\n    }\n    if (isFrame(activeElement) && activeElement.contentDocument) {\n        return getActiveElement(activeElement.contentDocument.body, activeDescendant);\n    }\n    if (activeDescendant) {\n        const id = activeElement.getAttribute(\"aria-activedescendant\");\n        if (id) {\n            const element = getOwnerDocument(activeElement).getElementById(id);\n            if (element) {\n                return element;\n            }\n        }\n    }\n    return activeElement;\n}\nfunction isFrame(element) {\n    return element.tagName === \"IFRAME\";\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-collection/dist/index.mjs":
/*!****************************************************************!*\
  !*** ./node_modules/@radix-ui/react-collection/dist/index.mjs ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createCollection: () => (/* binding */ createCollection),\n/* harmony export */   unstable_createCollection: () => (/* binding */ createCollection2)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ createCollection,unstable_createCollection auto */ // src/collection-legacy.tsx\n\n\n\n\n\nfunction createCollection(name) {\n    const PROVIDER_NAME = name + \"CollectionProvider\";\n    const [createCollectionContext, createCollectionScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(PROVIDER_NAME);\n    const [CollectionProviderImpl, useCollectionContext] = createCollectionContext(PROVIDER_NAME, {\n        collectionRef: {\n            current: null\n        },\n        itemMap: /* @__PURE__ */ new Map()\n    });\n    const CollectionProvider = (props)=>{\n        const { scope, children } = props;\n        const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n        const itemMap = react__WEBPACK_IMPORTED_MODULE_0__.useRef(/* @__PURE__ */ new Map()).current;\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionProviderImpl, {\n            scope,\n            itemMap,\n            collectionRef: ref,\n            children\n        });\n    };\n    CollectionProvider.displayName = PROVIDER_NAME;\n    const COLLECTION_SLOT_NAME = name + \"CollectionSlot\";\n    const CollectionSlotImpl = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__.createSlot)(COLLECTION_SLOT_NAME);\n    const CollectionSlot = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n        const { scope, children } = props;\n        const context = useCollectionContext(COLLECTION_SLOT_NAME, scope);\n        const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__.useComposedRefs)(forwardedRef, context.collectionRef);\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionSlotImpl, {\n            ref: composedRefs,\n            children\n        });\n    });\n    CollectionSlot.displayName = COLLECTION_SLOT_NAME;\n    const ITEM_SLOT_NAME = name + \"CollectionItemSlot\";\n    const ITEM_DATA_ATTR = \"data-radix-collection-item\";\n    const CollectionItemSlotImpl = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__.createSlot)(ITEM_SLOT_NAME);\n    const CollectionItemSlot = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n        const { scope, children, ...itemData } = props;\n        const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n        const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__.useComposedRefs)(forwardedRef, ref);\n        const context = useCollectionContext(ITEM_SLOT_NAME, scope);\n        react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n            context.itemMap.set(ref, {\n                ref,\n                ...itemData\n            });\n            return ()=>void context.itemMap.delete(ref);\n        });\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionItemSlotImpl, {\n            ...{\n                [ITEM_DATA_ATTR]: \"\"\n            },\n            ref: composedRefs,\n            children\n        });\n    });\n    CollectionItemSlot.displayName = ITEM_SLOT_NAME;\n    function useCollection(scope) {\n        const context = useCollectionContext(name + \"CollectionConsumer\", scope);\n        const getItems = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n            const collectionNode = context.collectionRef.current;\n            if (!collectionNode) return [];\n            const orderedNodes = Array.from(collectionNode.querySelectorAll(`[${ITEM_DATA_ATTR}]`));\n            const items = Array.from(context.itemMap.values());\n            const orderedItems = items.sort((a, b)=>orderedNodes.indexOf(a.ref.current) - orderedNodes.indexOf(b.ref.current));\n            return orderedItems;\n        }, [\n            context.collectionRef,\n            context.itemMap\n        ]);\n        return getItems;\n    }\n    return [\n        {\n            Provider: CollectionProvider,\n            Slot: CollectionSlot,\n            ItemSlot: CollectionItemSlot\n        },\n        useCollection,\n        createCollectionScope\n    ];\n}\n// src/collection.tsx\n\n\n\n\n// src/ordered-dictionary.ts\nvar __instanciated = /* @__PURE__ */ new WeakMap();\nvar OrderedDict = class _OrderedDict extends Map {\n    #keys;\n    constructor(entries){\n        super(entries);\n        this.#keys = [\n            ...super.keys()\n        ];\n        __instanciated.set(this, true);\n    }\n    set(key, value) {\n        if (__instanciated.get(this)) {\n            if (this.has(key)) {\n                this.#keys[this.#keys.indexOf(key)] = key;\n            } else {\n                this.#keys.push(key);\n            }\n        }\n        super.set(key, value);\n        return this;\n    }\n    insert(index, key, value) {\n        const has = this.has(key);\n        const length = this.#keys.length;\n        const relativeIndex = toSafeInteger(index);\n        let actualIndex = relativeIndex >= 0 ? relativeIndex : length + relativeIndex;\n        const safeIndex = actualIndex < 0 || actualIndex >= length ? -1 : actualIndex;\n        if (safeIndex === this.size || has && safeIndex === this.size - 1 || safeIndex === -1) {\n            this.set(key, value);\n            return this;\n        }\n        const size = this.size + (has ? 0 : 1);\n        if (relativeIndex < 0) {\n            actualIndex++;\n        }\n        const keys = [\n            ...this.#keys\n        ];\n        let nextValue;\n        let shouldSkip = false;\n        for(let i = actualIndex; i < size; i++){\n            if (actualIndex === i) {\n                let nextKey = keys[i];\n                if (keys[i] === key) {\n                    nextKey = keys[i + 1];\n                }\n                if (has) {\n                    this.delete(key);\n                }\n                nextValue = this.get(nextKey);\n                this.set(key, value);\n            } else {\n                if (!shouldSkip && keys[i - 1] === key) {\n                    shouldSkip = true;\n                }\n                const currentKey = keys[shouldSkip ? i : i - 1];\n                const currentValue = nextValue;\n                nextValue = this.get(currentKey);\n                this.delete(currentKey);\n                this.set(currentKey, currentValue);\n            }\n        }\n        return this;\n    }\n    with(index, key, value) {\n        const copy = new _OrderedDict(this);\n        copy.insert(index, key, value);\n        return copy;\n    }\n    before(key) {\n        const index = this.#keys.indexOf(key) - 1;\n        if (index < 0) {\n            return void 0;\n        }\n        return this.entryAt(index);\n    }\n    /**\n   * Sets a new key-value pair at the position before the given key.\n   */ setBefore(key, newKey, value) {\n        const index = this.#keys.indexOf(key);\n        if (index === -1) {\n            return this;\n        }\n        return this.insert(index, newKey, value);\n    }\n    after(key) {\n        let index = this.#keys.indexOf(key);\n        index = index === -1 || index === this.size - 1 ? -1 : index + 1;\n        if (index === -1) {\n            return void 0;\n        }\n        return this.entryAt(index);\n    }\n    /**\n   * Sets a new key-value pair at the position after the given key.\n   */ setAfter(key, newKey, value) {\n        const index = this.#keys.indexOf(key);\n        if (index === -1) {\n            return this;\n        }\n        return this.insert(index + 1, newKey, value);\n    }\n    first() {\n        return this.entryAt(0);\n    }\n    last() {\n        return this.entryAt(-1);\n    }\n    clear() {\n        this.#keys = [];\n        return super.clear();\n    }\n    delete(key) {\n        const deleted = super.delete(key);\n        if (deleted) {\n            this.#keys.splice(this.#keys.indexOf(key), 1);\n        }\n        return deleted;\n    }\n    deleteAt(index) {\n        const key = this.keyAt(index);\n        if (key !== void 0) {\n            return this.delete(key);\n        }\n        return false;\n    }\n    at(index) {\n        const key = at(this.#keys, index);\n        if (key !== void 0) {\n            return this.get(key);\n        }\n    }\n    entryAt(index) {\n        const key = at(this.#keys, index);\n        if (key !== void 0) {\n            return [\n                key,\n                this.get(key)\n            ];\n        }\n    }\n    indexOf(key) {\n        return this.#keys.indexOf(key);\n    }\n    keyAt(index) {\n        return at(this.#keys, index);\n    }\n    from(key, offset) {\n        const index = this.indexOf(key);\n        if (index === -1) {\n            return void 0;\n        }\n        let dest = index + offset;\n        if (dest < 0) dest = 0;\n        if (dest >= this.size) dest = this.size - 1;\n        return this.at(dest);\n    }\n    keyFrom(key, offset) {\n        const index = this.indexOf(key);\n        if (index === -1) {\n            return void 0;\n        }\n        let dest = index + offset;\n        if (dest < 0) dest = 0;\n        if (dest >= this.size) dest = this.size - 1;\n        return this.keyAt(dest);\n    }\n    find(predicate, thisArg) {\n        let index = 0;\n        for (const entry of this){\n            if (Reflect.apply(predicate, thisArg, [\n                entry,\n                index,\n                this\n            ])) {\n                return entry;\n            }\n            index++;\n        }\n        return void 0;\n    }\n    findIndex(predicate, thisArg) {\n        let index = 0;\n        for (const entry of this){\n            if (Reflect.apply(predicate, thisArg, [\n                entry,\n                index,\n                this\n            ])) {\n                return index;\n            }\n            index++;\n        }\n        return -1;\n    }\n    filter(predicate, thisArg) {\n        const entries = [];\n        let index = 0;\n        for (const entry of this){\n            if (Reflect.apply(predicate, thisArg, [\n                entry,\n                index,\n                this\n            ])) {\n                entries.push(entry);\n            }\n            index++;\n        }\n        return new _OrderedDict(entries);\n    }\n    map(callbackfn, thisArg) {\n        const entries = [];\n        let index = 0;\n        for (const entry of this){\n            entries.push([\n                entry[0],\n                Reflect.apply(callbackfn, thisArg, [\n                    entry,\n                    index,\n                    this\n                ])\n            ]);\n            index++;\n        }\n        return new _OrderedDict(entries);\n    }\n    reduce(...args) {\n        const [callbackfn, initialValue] = args;\n        let index = 0;\n        let accumulator = initialValue ?? this.at(0);\n        for (const entry of this){\n            if (index === 0 && args.length === 1) {\n                accumulator = entry;\n            } else {\n                accumulator = Reflect.apply(callbackfn, this, [\n                    accumulator,\n                    entry,\n                    index,\n                    this\n                ]);\n            }\n            index++;\n        }\n        return accumulator;\n    }\n    reduceRight(...args) {\n        const [callbackfn, initialValue] = args;\n        let accumulator = initialValue ?? this.at(-1);\n        for(let index = this.size - 1; index >= 0; index--){\n            const entry = this.at(index);\n            if (index === this.size - 1 && args.length === 1) {\n                accumulator = entry;\n            } else {\n                accumulator = Reflect.apply(callbackfn, this, [\n                    accumulator,\n                    entry,\n                    index,\n                    this\n                ]);\n            }\n        }\n        return accumulator;\n    }\n    toSorted(compareFn) {\n        const entries = [\n            ...this.entries()\n        ].sort(compareFn);\n        return new _OrderedDict(entries);\n    }\n    toReversed() {\n        const reversed = new _OrderedDict();\n        for(let index = this.size - 1; index >= 0; index--){\n            const key = this.keyAt(index);\n            const element = this.get(key);\n            reversed.set(key, element);\n        }\n        return reversed;\n    }\n    toSpliced(...args) {\n        const entries = [\n            ...this.entries()\n        ];\n        entries.splice(...args);\n        return new _OrderedDict(entries);\n    }\n    slice(start, end) {\n        const result = new _OrderedDict();\n        let stop = this.size - 1;\n        if (start === void 0) {\n            return result;\n        }\n        if (start < 0) {\n            start = start + this.size;\n        }\n        if (end !== void 0 && end > 0) {\n            stop = end - 1;\n        }\n        for(let index = start; index <= stop; index++){\n            const key = this.keyAt(index);\n            const element = this.get(key);\n            result.set(key, element);\n        }\n        return result;\n    }\n    every(predicate, thisArg) {\n        let index = 0;\n        for (const entry of this){\n            if (!Reflect.apply(predicate, thisArg, [\n                entry,\n                index,\n                this\n            ])) {\n                return false;\n            }\n            index++;\n        }\n        return true;\n    }\n    some(predicate, thisArg) {\n        let index = 0;\n        for (const entry of this){\n            if (Reflect.apply(predicate, thisArg, [\n                entry,\n                index,\n                this\n            ])) {\n                return true;\n            }\n            index++;\n        }\n        return false;\n    }\n};\nfunction at(array, index) {\n    if (\"at\" in Array.prototype) {\n        return Array.prototype.at.call(array, index);\n    }\n    const actualIndex = toSafeIndex(array, index);\n    return actualIndex === -1 ? void 0 : array[actualIndex];\n}\nfunction toSafeIndex(array, index) {\n    const length = array.length;\n    const relativeIndex = toSafeInteger(index);\n    const actualIndex = relativeIndex >= 0 ? relativeIndex : length + relativeIndex;\n    return actualIndex < 0 || actualIndex >= length ? -1 : actualIndex;\n}\nfunction toSafeInteger(number) {\n    return number !== number || number === 0 ? 0 : Math.trunc(number);\n}\n// src/collection.tsx\n\nfunction createCollection2(name) {\n    const PROVIDER_NAME = name + \"CollectionProvider\";\n    const [createCollectionContext, createCollectionScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(PROVIDER_NAME);\n    const [CollectionContextProvider, useCollectionContext] = createCollectionContext(PROVIDER_NAME, {\n        collectionElement: null,\n        collectionRef: {\n            current: null\n        },\n        collectionRefObject: {\n            current: null\n        },\n        itemMap: new OrderedDict(),\n        setItemMap: ()=>void 0\n    });\n    const CollectionProvider = ({ state, ...props })=>{\n        return state ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionProviderImpl, {\n            ...props,\n            state\n        }) : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionInit, {\n            ...props\n        });\n    };\n    CollectionProvider.displayName = PROVIDER_NAME;\n    const CollectionInit = (props)=>{\n        const state = useInitCollection();\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionProviderImpl, {\n            ...props,\n            state\n        });\n    };\n    CollectionInit.displayName = PROVIDER_NAME + \"Init\";\n    const CollectionProviderImpl = (props)=>{\n        const { scope, children, state } = props;\n        const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n        const [collectionElement, setCollectionElement] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n        const composeRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__.useComposedRefs)(ref, setCollectionElement);\n        const [itemMap, setItemMap] = state;\n        react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n            if (!collectionElement) return;\n            const observer = getChildListObserver(()=>{});\n            observer.observe(collectionElement, {\n                childList: true,\n                subtree: true\n            });\n            return ()=>{\n                observer.disconnect();\n            };\n        }, [\n            collectionElement\n        ]);\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionContextProvider, {\n            scope,\n            itemMap,\n            setItemMap,\n            collectionRef: composeRefs,\n            collectionRefObject: ref,\n            collectionElement,\n            children\n        });\n    };\n    CollectionProviderImpl.displayName = PROVIDER_NAME + \"Impl\";\n    const COLLECTION_SLOT_NAME = name + \"CollectionSlot\";\n    const CollectionSlotImpl = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__.createSlot)(COLLECTION_SLOT_NAME);\n    const CollectionSlot = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n        const { scope, children } = props;\n        const context = useCollectionContext(COLLECTION_SLOT_NAME, scope);\n        const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__.useComposedRefs)(forwardedRef, context.collectionRef);\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionSlotImpl, {\n            ref: composedRefs,\n            children\n        });\n    });\n    CollectionSlot.displayName = COLLECTION_SLOT_NAME;\n    const ITEM_SLOT_NAME = name + \"CollectionItemSlot\";\n    const ITEM_DATA_ATTR = \"data-radix-collection-item\";\n    const CollectionItemSlotImpl = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__.createSlot)(ITEM_SLOT_NAME);\n    const CollectionItemSlot = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n        const { scope, children, ...itemData } = props;\n        const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n        const [element, setElement] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n        const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__.useComposedRefs)(forwardedRef, ref, setElement);\n        const context = useCollectionContext(ITEM_SLOT_NAME, scope);\n        const { setItemMap } = context;\n        const itemDataRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(itemData);\n        if (!shallowEqual(itemDataRef.current, itemData)) {\n            itemDataRef.current = itemData;\n        }\n        const memoizedItemData = itemDataRef.current;\n        react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n            const itemData2 = memoizedItemData;\n            setItemMap((map)=>{\n                if (!element) {\n                    return map;\n                }\n                if (!map.has(element)) {\n                    map.set(element, {\n                        ...itemData2,\n                        element\n                    });\n                    return map.toSorted(sortByDocumentPosition);\n                }\n                return map.set(element, {\n                    ...itemData2,\n                    element\n                }).toSorted(sortByDocumentPosition);\n            });\n            return ()=>{\n                setItemMap((map)=>{\n                    if (!element || !map.has(element)) {\n                        return map;\n                    }\n                    map.delete(element);\n                    return new OrderedDict(map);\n                });\n            };\n        }, [\n            element,\n            memoizedItemData,\n            setItemMap\n        ]);\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionItemSlotImpl, {\n            ...{\n                [ITEM_DATA_ATTR]: \"\"\n            },\n            ref: composedRefs,\n            children\n        });\n    });\n    CollectionItemSlot.displayName = ITEM_SLOT_NAME;\n    function useInitCollection() {\n        return react__WEBPACK_IMPORTED_MODULE_0__.useState(new OrderedDict());\n    }\n    function useCollection(scope) {\n        const { itemMap } = useCollectionContext(name + \"CollectionConsumer\", scope);\n        return itemMap;\n    }\n    const functions = {\n        createCollectionScope,\n        useCollection,\n        useInitCollection\n    };\n    return [\n        {\n            Provider: CollectionProvider,\n            Slot: CollectionSlot,\n            ItemSlot: CollectionItemSlot\n        },\n        functions\n    ];\n}\nfunction shallowEqual(a, b) {\n    if (a === b) return true;\n    if (typeof a !== \"object\" || typeof b !== \"object\") return false;\n    if (a == null || b == null) return false;\n    const keysA = Object.keys(a);\n    const keysB = Object.keys(b);\n    if (keysA.length !== keysB.length) return false;\n    for (const key of keysA){\n        if (!Object.prototype.hasOwnProperty.call(b, key)) return false;\n        if (a[key] !== b[key]) return false;\n    }\n    return true;\n}\nfunction isElementPreceding(a, b) {\n    return !!(b.compareDocumentPosition(a) & Node.DOCUMENT_POSITION_PRECEDING);\n}\nfunction sortByDocumentPosition(a, b) {\n    return !a[1].element || !b[1].element ? 0 : isElementPreceding(a[1].element, b[1].element) ? -1 : 1;\n}\nfunction getChildListObserver(callback) {\n    const observer = new MutationObserver((mutationsList)=>{\n        for (const mutation of mutationsList){\n            if (mutation.type === \"childList\") {\n                callback();\n                return;\n            }\n        }\n    });\n    return observer;\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-collection/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/@radix-ui/react-compose-refs/dist/index.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   composeRefs: () => (/* binding */ composeRefs),\n/* harmony export */   useComposedRefs: () => (/* binding */ useComposedRefs)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n// packages/react/compose-refs/src/compose-refs.tsx\n\nfunction setRef(ref, value) {\n    if (typeof ref === \"function\") {\n        return ref(value);\n    } else if (ref !== null && ref !== void 0) {\n        ref.current = value;\n    }\n}\nfunction composeRefs(...refs) {\n    return (node)=>{\n        let hasCleanup = false;\n        const cleanups = refs.map((ref)=>{\n            const cleanup = setRef(ref, node);\n            if (!hasCleanup && typeof cleanup == \"function\") {\n                hasCleanup = true;\n            }\n            return cleanup;\n        });\n        if (hasCleanup) {\n            return ()=>{\n                for(let i = 0; i < cleanups.length; i++){\n                    const cleanup = cleanups[i];\n                    if (typeof cleanup == \"function\") {\n                        cleanup();\n                    } else {\n                        setRef(refs[i], null);\n                    }\n                }\n            };\n        }\n    };\n}\nfunction useComposedRefs(...refs) {\n    return react__WEBPACK_IMPORTED_MODULE_0__.useCallback(composeRefs(...refs), refs);\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs":
/*!*************************************************************!*\
  !*** ./node_modules/@radix-ui/react-context/dist/index.mjs ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createContext: () => (/* binding */ createContext2),\n/* harmony export */   createContextScope: () => (/* binding */ createContextScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// packages/react/context/src/create-context.tsx\n\n\nfunction createContext2(rootComponentName, defaultContext) {\n    const Context = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(defaultContext);\n    const Provider = (props)=>{\n        const { children, ...context } = props;\n        const value = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>context, Object.values(context));\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Context.Provider, {\n            value,\n            children\n        });\n    };\n    Provider.displayName = rootComponentName + \"Provider\";\n    function useContext2(consumerName) {\n        const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(Context);\n        if (context) return context;\n        if (defaultContext !== void 0) return defaultContext;\n        throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n    }\n    return [\n        Provider,\n        useContext2\n    ];\n}\nfunction createContextScope(scopeName, createContextScopeDeps = []) {\n    let defaultContexts = [];\n    function createContext3(rootComponentName, defaultContext) {\n        const BaseContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(defaultContext);\n        const index = defaultContexts.length;\n        defaultContexts = [\n            ...defaultContexts,\n            defaultContext\n        ];\n        const Provider = (props)=>{\n            const { scope, children, ...context } = props;\n            const Context = scope?.[scopeName]?.[index] || BaseContext;\n            const value = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>context, Object.values(context));\n            return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Context.Provider, {\n                value,\n                children\n            });\n        };\n        Provider.displayName = rootComponentName + \"Provider\";\n        function useContext2(consumerName, scope) {\n            const Context = scope?.[scopeName]?.[index] || BaseContext;\n            const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(Context);\n            if (context) return context;\n            if (defaultContext !== void 0) return defaultContext;\n            throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n        }\n        return [\n            Provider,\n            useContext2\n        ];\n    }\n    const createScope = ()=>{\n        const scopeContexts = defaultContexts.map((defaultContext)=>{\n            return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(defaultContext);\n        });\n        return function useScope(scope) {\n            const contexts = scope?.[scopeName] || scopeContexts;\n            return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>({\n                    [`__scope${scopeName}`]: {\n                        ...scope,\n                        [scopeName]: contexts\n                    }\n                }), [\n                scope,\n                contexts\n            ]);\n        };\n    };\n    createScope.scopeName = scopeName;\n    return [\n        createContext3,\n        composeContextScopes(createScope, ...createContextScopeDeps)\n    ];\n}\nfunction composeContextScopes(...scopes) {\n    const baseScope = scopes[0];\n    if (scopes.length === 1) return baseScope;\n    const createScope = ()=>{\n        const scopeHooks = scopes.map((createScope2)=>({\n                useScope: createScope2(),\n                scopeName: createScope2.scopeName\n            }));\n        return function useComposedScopes(overrideScopes) {\n            const nextScopes = scopeHooks.reduce((nextScopes2, { useScope, scopeName })=>{\n                const scopeProps = useScope(overrideScopes);\n                const currentScope = scopeProps[`__scope${scopeName}`];\n                return {\n                    ...nextScopes2,\n                    ...currentScope\n                };\n            }, {});\n            return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>({\n                    [`__scope${baseScope.scopeName}`]: nextScopes\n                }), [\n                nextScopes\n            ]);\n        };\n    };\n    createScope.scopeName = baseScope.scopeName;\n    return createScope;\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Branch: () => (/* binding */ Branch),\n/* harmony export */   DismissableLayer: () => (/* binding */ DismissableLayer),\n/* harmony export */   DismissableLayerBranch: () => (/* binding */ DismissableLayerBranch),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_escape_keydown__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-use-escape-keydown */ \"(ssr)/./node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Branch,DismissableLayer,DismissableLayerBranch,Root auto */ // src/dismissable-layer.tsx\n\n\n\n\n\n\n\nvar DISMISSABLE_LAYER_NAME = \"DismissableLayer\";\nvar CONTEXT_UPDATE = \"dismissableLayer.update\";\nvar POINTER_DOWN_OUTSIDE = \"dismissableLayer.pointerDownOutside\";\nvar FOCUS_OUTSIDE = \"dismissableLayer.focusOutside\";\nvar originalBodyPointerEvents;\nvar DismissableLayerContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext({\n    layers: /* @__PURE__ */ new Set(),\n    layersWithOutsidePointerEventsDisabled: /* @__PURE__ */ new Set(),\n    branches: /* @__PURE__ */ new Set()\n});\nvar DismissableLayer = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { disableOutsidePointerEvents = false, onEscapeKeyDown, onPointerDownOutside, onFocusOutside, onInteractOutside, onDismiss, ...layerProps } = props;\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(DismissableLayerContext);\n    const [node, setNode] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const ownerDocument = node?.ownerDocument ?? globalThis?.document;\n    const [, force] = react__WEBPACK_IMPORTED_MODULE_0__.useState({});\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__.useComposedRefs)(forwardedRef, (node2)=>setNode(node2));\n    const layers = Array.from(context.layers);\n    const [highestLayerWithOutsidePointerEventsDisabled] = [\n        ...context.layersWithOutsidePointerEventsDisabled\n    ].slice(-1);\n    const highestLayerWithOutsidePointerEventsDisabledIndex = layers.indexOf(highestLayerWithOutsidePointerEventsDisabled);\n    const index = node ? layers.indexOf(node) : -1;\n    const isBodyPointerEventsDisabled = context.layersWithOutsidePointerEventsDisabled.size > 0;\n    const isPointerEventsEnabled = index >= highestLayerWithOutsidePointerEventsDisabledIndex;\n    const pointerDownOutside = usePointerDownOutside((event)=>{\n        const target = event.target;\n        const isPointerDownOnBranch = [\n            ...context.branches\n        ].some((branch)=>branch.contains(target));\n        if (!isPointerEventsEnabled || isPointerDownOnBranch) return;\n        onPointerDownOutside?.(event);\n        onInteractOutside?.(event);\n        if (!event.defaultPrevented) onDismiss?.();\n    }, ownerDocument);\n    const focusOutside = useFocusOutside((event)=>{\n        const target = event.target;\n        const isFocusInBranch = [\n            ...context.branches\n        ].some((branch)=>branch.contains(target));\n        if (isFocusInBranch) return;\n        onFocusOutside?.(event);\n        onInteractOutside?.(event);\n        if (!event.defaultPrevented) onDismiss?.();\n    }, ownerDocument);\n    (0,_radix_ui_react_use_escape_keydown__WEBPACK_IMPORTED_MODULE_3__.useEscapeKeydown)((event)=>{\n        const isHighestLayer = index === context.layers.size - 1;\n        if (!isHighestLayer) return;\n        onEscapeKeyDown?.(event);\n        if (!event.defaultPrevented && onDismiss) {\n            event.preventDefault();\n            onDismiss();\n        }\n    }, ownerDocument);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (!node) return;\n        if (disableOutsidePointerEvents) {\n            if (context.layersWithOutsidePointerEventsDisabled.size === 0) {\n                originalBodyPointerEvents = ownerDocument.body.style.pointerEvents;\n                ownerDocument.body.style.pointerEvents = \"none\";\n            }\n            context.layersWithOutsidePointerEventsDisabled.add(node);\n        }\n        context.layers.add(node);\n        dispatchUpdate();\n        return ()=>{\n            if (disableOutsidePointerEvents && context.layersWithOutsidePointerEventsDisabled.size === 1) {\n                ownerDocument.body.style.pointerEvents = originalBodyPointerEvents;\n            }\n        };\n    }, [\n        node,\n        ownerDocument,\n        disableOutsidePointerEvents,\n        context\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        return ()=>{\n            if (!node) return;\n            context.layers.delete(node);\n            context.layersWithOutsidePointerEventsDisabled.delete(node);\n            dispatchUpdate();\n        };\n    }, [\n        node,\n        context\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const handleUpdate = ()=>force({});\n        document.addEventListener(CONTEXT_UPDATE, handleUpdate);\n        return ()=>document.removeEventListener(CONTEXT_UPDATE, handleUpdate);\n    }, []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n        ...layerProps,\n        ref: composedRefs,\n        style: {\n            pointerEvents: isBodyPointerEventsDisabled ? isPointerEventsEnabled ? \"auto\" : \"none\" : void 0,\n            ...props.style\n        },\n        onFocusCapture: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__.composeEventHandlers)(props.onFocusCapture, focusOutside.onFocusCapture),\n        onBlurCapture: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__.composeEventHandlers)(props.onBlurCapture, focusOutside.onBlurCapture),\n        onPointerDownCapture: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__.composeEventHandlers)(props.onPointerDownCapture, pointerDownOutside.onPointerDownCapture)\n    });\n});\nDismissableLayer.displayName = DISMISSABLE_LAYER_NAME;\nvar BRANCH_NAME = \"DismissableLayerBranch\";\nvar DismissableLayerBranch = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(DismissableLayerContext);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__.useComposedRefs)(forwardedRef, ref);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const node = ref.current;\n        if (node) {\n            context.branches.add(node);\n            return ()=>{\n                context.branches.delete(node);\n            };\n        }\n    }, [\n        context.branches\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n        ...props,\n        ref: composedRefs\n    });\n});\nDismissableLayerBranch.displayName = BRANCH_NAME;\nfunction usePointerDownOutside(onPointerDownOutside, ownerDocument = globalThis?.document) {\n    const handlePointerDownOutside = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__.useCallbackRef)(onPointerDownOutside);\n    const isPointerInsideReactTreeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const handleClickRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(()=>{});\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const handlePointerDown = (event)=>{\n            if (event.target && !isPointerInsideReactTreeRef.current) {\n                let handleAndDispatchPointerDownOutsideEvent2 = function() {\n                    handleAndDispatchCustomEvent(POINTER_DOWN_OUTSIDE, handlePointerDownOutside, eventDetail, {\n                        discrete: true\n                    });\n                };\n                var handleAndDispatchPointerDownOutsideEvent = handleAndDispatchPointerDownOutsideEvent2;\n                const eventDetail = {\n                    originalEvent: event\n                };\n                if (event.pointerType === \"touch\") {\n                    ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n                    handleClickRef.current = handleAndDispatchPointerDownOutsideEvent2;\n                    ownerDocument.addEventListener(\"click\", handleClickRef.current, {\n                        once: true\n                    });\n                } else {\n                    handleAndDispatchPointerDownOutsideEvent2();\n                }\n            } else {\n                ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n            }\n            isPointerInsideReactTreeRef.current = false;\n        };\n        const timerId = window.setTimeout(()=>{\n            ownerDocument.addEventListener(\"pointerdown\", handlePointerDown);\n        }, 0);\n        return ()=>{\n            window.clearTimeout(timerId);\n            ownerDocument.removeEventListener(\"pointerdown\", handlePointerDown);\n            ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n        };\n    }, [\n        ownerDocument,\n        handlePointerDownOutside\n    ]);\n    return {\n        // ensures we check React component tree (not just DOM tree)\n        onPointerDownCapture: ()=>isPointerInsideReactTreeRef.current = true\n    };\n}\nfunction useFocusOutside(onFocusOutside, ownerDocument = globalThis?.document) {\n    const handleFocusOutside = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__.useCallbackRef)(onFocusOutside);\n    const isFocusInsideReactTreeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const handleFocus = (event)=>{\n            if (event.target && !isFocusInsideReactTreeRef.current) {\n                const eventDetail = {\n                    originalEvent: event\n                };\n                handleAndDispatchCustomEvent(FOCUS_OUTSIDE, handleFocusOutside, eventDetail, {\n                    discrete: false\n                });\n            }\n        };\n        ownerDocument.addEventListener(\"focusin\", handleFocus);\n        return ()=>ownerDocument.removeEventListener(\"focusin\", handleFocus);\n    }, [\n        ownerDocument,\n        handleFocusOutside\n    ]);\n    return {\n        onFocusCapture: ()=>isFocusInsideReactTreeRef.current = true,\n        onBlurCapture: ()=>isFocusInsideReactTreeRef.current = false\n    };\n}\nfunction dispatchUpdate() {\n    const event = new CustomEvent(CONTEXT_UPDATE);\n    document.dispatchEvent(event);\n}\nfunction handleAndDispatchCustomEvent(name, handler, detail, { discrete }) {\n    const target = detail.originalEvent.target;\n    const event = new CustomEvent(name, {\n        bubbles: false,\n        cancelable: true,\n        detail\n    });\n    if (handler) target.addEventListener(name, handler, {\n        once: true\n    });\n    if (discrete) {\n        (0,_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.dispatchDiscreteCustomEvent)(target, event);\n    } else {\n        target.dispatchEvent(event);\n    }\n}\nvar Root = DismissableLayer;\nvar Branch = DismissableLayerBranch;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-portal/dist/index.mjs":
/*!************************************************************!*\
  !*** ./node_modules/@radix-ui/react-portal/dist/index.mjs ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Portal: () => (/* binding */ Portal),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Portal,Root auto */ // src/portal.tsx\n\n\n\n\n\nvar PORTAL_NAME = \"Portal\";\nvar Portal = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { container: containerProp, ...portalProps } = props;\n    const [mounted, setMounted] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_3__.useLayoutEffect)(()=>setMounted(true), []);\n    const container = containerProp || mounted && globalThis?.document?.body;\n    return container ? /*#__PURE__*/ react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal(/* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n        ...portalProps,\n        ref: forwardedRef\n    }), container) : null;\n});\nPortal.displayName = PORTAL_NAME;\nvar Root = Portal;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-portal/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-presence/dist/index.mjs":
/*!**************************************************************!*\
  !*** ./node_modules/@radix-ui/react-presence/dist/index.mjs ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Presence: () => (/* binding */ Presence),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ Presence,Root auto */ // src/presence.tsx\n\n\n\n// src/use-state-machine.tsx\n\nfunction useStateMachine(initialState, machine) {\n    return react__WEBPACK_IMPORTED_MODULE_0__.useReducer((state, event)=>{\n        const nextState = machine[state][event];\n        return nextState ?? state;\n    }, initialState);\n}\n// src/presence.tsx\nvar Presence = (props)=>{\n    const { present, children } = props;\n    const presence = usePresence(present);\n    const child = typeof children === \"function\" ? children({\n        present: presence.isPresent\n    }) : react__WEBPACK_IMPORTED_MODULE_0__.Children.only(children);\n    const ref = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_1__.useComposedRefs)(presence.ref, getElementRef(child));\n    const forceMount = typeof children === \"function\";\n    return forceMount || presence.isPresent ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(child, {\n        ref\n    }) : null;\n};\nPresence.displayName = \"Presence\";\nfunction usePresence(present) {\n    const [node, setNode] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    const stylesRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const prevPresentRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(present);\n    const prevAnimationNameRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(\"none\");\n    const initialState = present ? \"mounted\" : \"unmounted\";\n    const [state, send] = useStateMachine(initialState, {\n        mounted: {\n            UNMOUNT: \"unmounted\",\n            ANIMATION_OUT: \"unmountSuspended\"\n        },\n        unmountSuspended: {\n            MOUNT: \"mounted\",\n            ANIMATION_END: \"unmounted\"\n        },\n        unmounted: {\n            MOUNT: \"mounted\"\n        }\n    });\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const currentAnimationName = getAnimationName(stylesRef.current);\n        prevAnimationNameRef.current = state === \"mounted\" ? currentAnimationName : \"none\";\n    }, [\n        state\n    ]);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_2__.useLayoutEffect)(()=>{\n        const styles = stylesRef.current;\n        const wasPresent = prevPresentRef.current;\n        const hasPresentChanged = wasPresent !== present;\n        if (hasPresentChanged) {\n            const prevAnimationName = prevAnimationNameRef.current;\n            const currentAnimationName = getAnimationName(styles);\n            if (present) {\n                send(\"MOUNT\");\n            } else if (currentAnimationName === \"none\" || styles?.display === \"none\") {\n                send(\"UNMOUNT\");\n            } else {\n                const isAnimating = prevAnimationName !== currentAnimationName;\n                if (wasPresent && isAnimating) {\n                    send(\"ANIMATION_OUT\");\n                } else {\n                    send(\"UNMOUNT\");\n                }\n            }\n            prevPresentRef.current = present;\n        }\n    }, [\n        present,\n        send\n    ]);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_2__.useLayoutEffect)(()=>{\n        if (node) {\n            let timeoutId;\n            const ownerWindow = node.ownerDocument.defaultView ?? window;\n            const handleAnimationEnd = (event)=>{\n                const currentAnimationName = getAnimationName(stylesRef.current);\n                const isCurrentAnimation = currentAnimationName.includes(CSS.escape(event.animationName));\n                if (event.target === node && isCurrentAnimation) {\n                    send(\"ANIMATION_END\");\n                    if (!prevPresentRef.current) {\n                        const currentFillMode = node.style.animationFillMode;\n                        node.style.animationFillMode = \"forwards\";\n                        timeoutId = ownerWindow.setTimeout(()=>{\n                            if (node.style.animationFillMode === \"forwards\") {\n                                node.style.animationFillMode = currentFillMode;\n                            }\n                        });\n                    }\n                }\n            };\n            const handleAnimationStart = (event)=>{\n                if (event.target === node) {\n                    prevAnimationNameRef.current = getAnimationName(stylesRef.current);\n                }\n            };\n            node.addEventListener(\"animationstart\", handleAnimationStart);\n            node.addEventListener(\"animationcancel\", handleAnimationEnd);\n            node.addEventListener(\"animationend\", handleAnimationEnd);\n            return ()=>{\n                ownerWindow.clearTimeout(timeoutId);\n                node.removeEventListener(\"animationstart\", handleAnimationStart);\n                node.removeEventListener(\"animationcancel\", handleAnimationEnd);\n                node.removeEventListener(\"animationend\", handleAnimationEnd);\n            };\n        } else {\n            send(\"ANIMATION_END\");\n        }\n    }, [\n        node,\n        send\n    ]);\n    return {\n        isPresent: [\n            \"mounted\",\n            \"unmountSuspended\"\n        ].includes(state),\n        ref: react__WEBPACK_IMPORTED_MODULE_0__.useCallback((node2)=>{\n            stylesRef.current = node2 ? getComputedStyle(node2) : null;\n            setNode(node2);\n        }, [])\n    };\n}\nfunction getAnimationName(styles) {\n    return styles?.animationName || \"none\";\n}\nfunction getElementRef(element) {\n    let getter = Object.getOwnPropertyDescriptor(element.props, \"ref\")?.get;\n    let mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n    if (mayWarn) {\n        return element.ref;\n    }\n    getter = Object.getOwnPropertyDescriptor(element, \"ref\")?.get;\n    mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n    if (mayWarn) {\n        return element.props.ref;\n    }\n    return element.props.ref || element.ref;\n}\nvar Root = Presence;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-presence/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/@radix-ui/react-primitive/dist/index.mjs ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Primitive: () => (/* binding */ Primitive),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   dispatchDiscreteCustomEvent: () => (/* binding */ dispatchDiscreteCustomEvent)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// src/primitive.tsx\n\n\n\n\nvar NODES = [\n    \"a\",\n    \"button\",\n    \"div\",\n    \"form\",\n    \"h2\",\n    \"h3\",\n    \"img\",\n    \"input\",\n    \"label\",\n    \"li\",\n    \"nav\",\n    \"ol\",\n    \"p\",\n    \"select\",\n    \"span\",\n    \"svg\",\n    \"ul\"\n];\nvar Primitive = NODES.reduce((primitive, node)=>{\n    const Slot = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__.createSlot)(`Primitive.${node}`);\n    const Node = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n        const { asChild, ...primitiveProps } = props;\n        const Comp = asChild ? Slot : node;\n        if (false) {}\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Comp, {\n            ...primitiveProps,\n            ref: forwardedRef\n        });\n    });\n    Node.displayName = `Primitive.${node}`;\n    return {\n        ...primitive,\n        [node]: Node\n    };\n}, {});\nfunction dispatchDiscreteCustomEvent(target, event) {\n    if (target) react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync(()=>target.dispatchEvent(event));\n}\nvar Root = Primitive;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs":
/*!**********************************************************!*\
  !*** ./node_modules/@radix-ui/react-slot/dist/index.mjs ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Root: () => (/* binding */ Slot),\n/* harmony export */   Slot: () => (/* binding */ Slot),\n/* harmony export */   Slottable: () => (/* binding */ Slottable),\n/* harmony export */   createSlot: () => (/* binding */ createSlot),\n/* harmony export */   createSlottable: () => (/* binding */ createSlottable)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// src/slot.tsx\n\n\n\n// @__NO_SIDE_EFFECTS__\nfunction createSlot(ownerName) {\n    const SlotClone = /* @__PURE__ */ createSlotClone(ownerName);\n    const Slot2 = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n        const { children, ...slotProps } = props;\n        const childrenArray = react__WEBPACK_IMPORTED_MODULE_0__.Children.toArray(children);\n        const slottable = childrenArray.find(isSlottable);\n        if (slottable) {\n            const newElement = slottable.props.children;\n            const newChildren = childrenArray.map((child)=>{\n                if (child === slottable) {\n                    if (react__WEBPACK_IMPORTED_MODULE_0__.Children.count(newElement) > 1) return react__WEBPACK_IMPORTED_MODULE_0__.Children.only(null);\n                    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(newElement) ? newElement.props.children : null;\n                } else {\n                    return child;\n                }\n            });\n            return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SlotClone, {\n                ...slotProps,\n                ref: forwardedRef,\n                children: /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(newElement) ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(newElement, void 0, newChildren) : null\n            });\n        }\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SlotClone, {\n            ...slotProps,\n            ref: forwardedRef,\n            children\n        });\n    });\n    Slot2.displayName = `${ownerName}.Slot`;\n    return Slot2;\n}\nvar Slot = /* @__PURE__ */ createSlot(\"Slot\");\n// @__NO_SIDE_EFFECTS__\nfunction createSlotClone(ownerName) {\n    const SlotClone = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n        const { children, ...slotProps } = props;\n        if (/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(children)) {\n            const childrenRef = getElementRef(children);\n            const props2 = mergeProps(slotProps, children.props);\n            if (children.type !== react__WEBPACK_IMPORTED_MODULE_0__.Fragment) {\n                props2.ref = forwardedRef ? (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__.composeRefs)(forwardedRef, childrenRef) : childrenRef;\n            }\n            return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(children, props2);\n        }\n        return react__WEBPACK_IMPORTED_MODULE_0__.Children.count(children) > 1 ? react__WEBPACK_IMPORTED_MODULE_0__.Children.only(null) : null;\n    });\n    SlotClone.displayName = `${ownerName}.SlotClone`;\n    return SlotClone;\n}\nvar SLOTTABLE_IDENTIFIER = Symbol(\"radix.slottable\");\n// @__NO_SIDE_EFFECTS__\nfunction createSlottable(ownerName) {\n    const Slottable2 = ({ children })=>{\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n            children\n        });\n    };\n    Slottable2.displayName = `${ownerName}.Slottable`;\n    Slottable2.__radixId = SLOTTABLE_IDENTIFIER;\n    return Slottable2;\n}\nvar Slottable = /* @__PURE__ */ createSlottable(\"Slottable\");\nfunction isSlottable(child) {\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(child) && typeof child.type === \"function\" && \"__radixId\" in child.type && child.type.__radixId === SLOTTABLE_IDENTIFIER;\n}\nfunction mergeProps(slotProps, childProps) {\n    const overrideProps = {\n        ...childProps\n    };\n    for(const propName in childProps){\n        const slotPropValue = slotProps[propName];\n        const childPropValue = childProps[propName];\n        const isHandler = /^on[A-Z]/.test(propName);\n        if (isHandler) {\n            if (slotPropValue && childPropValue) {\n                overrideProps[propName] = (...args)=>{\n                    const result = childPropValue(...args);\n                    slotPropValue(...args);\n                    return result;\n                };\n            } else if (slotPropValue) {\n                overrideProps[propName] = slotPropValue;\n            }\n        } else if (propName === \"style\") {\n            overrideProps[propName] = {\n                ...slotPropValue,\n                ...childPropValue\n            };\n        } else if (propName === \"className\") {\n            overrideProps[propName] = [\n                slotPropValue,\n                childPropValue\n            ].filter(Boolean).join(\" \");\n        }\n    }\n    return {\n        ...slotProps,\n        ...overrideProps\n    };\n}\nfunction getElementRef(element) {\n    let getter = Object.getOwnPropertyDescriptor(element.props, \"ref\")?.get;\n    let mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n    if (mayWarn) {\n        return element.ref;\n    }\n    getter = Object.getOwnPropertyDescriptor(element, \"ref\")?.get;\n    mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n    if (mayWarn) {\n        return element.props.ref;\n    }\n    return element.props.ref || element.ref;\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-toast/dist/index.mjs":
/*!***********************************************************!*\
  !*** ./node_modules/@radix-ui/react-toast/dist/index.mjs ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Action: () => (/* binding */ Action),\n/* harmony export */   Close: () => (/* binding */ Close),\n/* harmony export */   Description: () => (/* binding */ Description),\n/* harmony export */   Provider: () => (/* binding */ Provider),\n/* harmony export */   Root: () => (/* binding */ Root2),\n/* harmony export */   Title: () => (/* binding */ Title),\n/* harmony export */   Toast: () => (/* binding */ Toast),\n/* harmony export */   ToastAction: () => (/* binding */ ToastAction),\n/* harmony export */   ToastClose: () => (/* binding */ ToastClose),\n/* harmony export */   ToastDescription: () => (/* binding */ ToastDescription),\n/* harmony export */   ToastProvider: () => (/* binding */ ToastProvider),\n/* harmony export */   ToastTitle: () => (/* binding */ ToastTitle),\n/* harmony export */   ToastViewport: () => (/* binding */ ToastViewport),\n/* harmony export */   Viewport: () => (/* binding */ Viewport),\n/* harmony export */   createToastScope: () => (/* binding */ createToastScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-collection */ \"(ssr)/./node_modules/@radix-ui/react-collection/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-dismissable-layer */ \"(ssr)/./node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @radix-ui/react-portal */ \"(ssr)/./node_modules/@radix-ui/react-portal/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-presence */ \"(ssr)/./node_modules/@radix-ui/react-presence/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_visually_hidden__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-visually-hidden */ \"(ssr)/./node_modules/@radix-ui/react-visually-hidden/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Action,Close,Description,Provider,Root,Title,Toast,ToastAction,ToastClose,ToastDescription,ToastProvider,ToastTitle,ToastViewport,Viewport,createToastScope auto */ // src/toast.tsx\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar PROVIDER_NAME = \"ToastProvider\";\nvar [Collection, useCollection, createCollectionScope] = (0,_radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_3__.createCollection)(\"Toast\");\nvar [createToastContext, createToastScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_4__.createContextScope)(\"Toast\", [\n    createCollectionScope\n]);\nvar [ToastProviderProvider, useToastProviderContext] = createToastContext(PROVIDER_NAME);\nvar ToastProvider = (props)=>{\n    const { __scopeToast, label = \"Notification\", duration = 5e3, swipeDirection = \"right\", swipeThreshold = 50, children } = props;\n    const [viewport, setViewport] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [toastCount, setToastCount] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    const isFocusedToastEscapeKeyDownRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const isClosePausedRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    if (!label.trim()) {\n        console.error(`Invalid prop \\`label\\` supplied to \\`${PROVIDER_NAME}\\`. Expected non-empty \\`string\\`.`);\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.Provider, {\n        scope: __scopeToast,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(ToastProviderProvider, {\n            scope: __scopeToast,\n            label,\n            duration,\n            swipeDirection,\n            swipeThreshold,\n            toastCount,\n            viewport,\n            onViewportChange: setViewport,\n            onToastAdd: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>setToastCount((prevCount)=>prevCount + 1), []),\n            onToastRemove: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>setToastCount((prevCount)=>prevCount - 1), []),\n            isFocusedToastEscapeKeyDownRef,\n            isClosePausedRef,\n            children\n        })\n    });\n};\nToastProvider.displayName = PROVIDER_NAME;\nvar VIEWPORT_NAME = \"ToastViewport\";\nvar VIEWPORT_DEFAULT_HOTKEY = [\n    \"F8\"\n];\nvar VIEWPORT_PAUSE = \"toast.viewportPause\";\nvar VIEWPORT_RESUME = \"toast.viewportResume\";\nvar ToastViewport = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeToast, hotkey = VIEWPORT_DEFAULT_HOTKEY, label = \"Notifications ({hotkey})\", ...viewportProps } = props;\n    const context = useToastProviderContext(VIEWPORT_NAME, __scopeToast);\n    const getItems = useCollection(__scopeToast);\n    const wrapperRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const headFocusProxyRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const tailFocusProxyRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs)(forwardedRef, ref, context.onViewportChange);\n    const hotkeyLabel = hotkey.join(\"+\").replace(/Key/g, \"\").replace(/Digit/g, \"\");\n    const hasToasts = context.toastCount > 0;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const handleKeyDown = (event)=>{\n            const isHotkeyPressed = hotkey.length !== 0 && hotkey.every((key)=>event[key] || event.code === key);\n            if (isHotkeyPressed) ref.current?.focus();\n        };\n        document.addEventListener(\"keydown\", handleKeyDown);\n        return ()=>document.removeEventListener(\"keydown\", handleKeyDown);\n    }, [\n        hotkey\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const wrapper = wrapperRef.current;\n        const viewport = ref.current;\n        if (hasToasts && wrapper && viewport) {\n            const handlePause = ()=>{\n                if (!context.isClosePausedRef.current) {\n                    const pauseEvent = new CustomEvent(VIEWPORT_PAUSE);\n                    viewport.dispatchEvent(pauseEvent);\n                    context.isClosePausedRef.current = true;\n                }\n            };\n            const handleResume = ()=>{\n                if (context.isClosePausedRef.current) {\n                    const resumeEvent = new CustomEvent(VIEWPORT_RESUME);\n                    viewport.dispatchEvent(resumeEvent);\n                    context.isClosePausedRef.current = false;\n                }\n            };\n            const handleFocusOutResume = (event)=>{\n                const isFocusMovingOutside = !wrapper.contains(event.relatedTarget);\n                if (isFocusMovingOutside) handleResume();\n            };\n            const handlePointerLeaveResume = ()=>{\n                const isFocusInside = wrapper.contains(document.activeElement);\n                if (!isFocusInside) handleResume();\n            };\n            wrapper.addEventListener(\"focusin\", handlePause);\n            wrapper.addEventListener(\"focusout\", handleFocusOutResume);\n            wrapper.addEventListener(\"pointermove\", handlePause);\n            wrapper.addEventListener(\"pointerleave\", handlePointerLeaveResume);\n            window.addEventListener(\"blur\", handlePause);\n            window.addEventListener(\"focus\", handleResume);\n            return ()=>{\n                wrapper.removeEventListener(\"focusin\", handlePause);\n                wrapper.removeEventListener(\"focusout\", handleFocusOutResume);\n                wrapper.removeEventListener(\"pointermove\", handlePause);\n                wrapper.removeEventListener(\"pointerleave\", handlePointerLeaveResume);\n                window.removeEventListener(\"blur\", handlePause);\n                window.removeEventListener(\"focus\", handleResume);\n            };\n        }\n    }, [\n        hasToasts,\n        context.isClosePausedRef\n    ]);\n    const getSortedTabbableCandidates = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(({ tabbingDirection })=>{\n        const toastItems = getItems();\n        const tabbableCandidates = toastItems.map((toastItem)=>{\n            const toastNode = toastItem.ref.current;\n            const toastTabbableCandidates = [\n                toastNode,\n                ...getTabbableCandidates(toastNode)\n            ];\n            return tabbingDirection === \"forwards\" ? toastTabbableCandidates : toastTabbableCandidates.reverse();\n        });\n        return (tabbingDirection === \"forwards\" ? tabbableCandidates.reverse() : tabbableCandidates).flat();\n    }, [\n        getItems\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const viewport = ref.current;\n        if (viewport) {\n            const handleKeyDown = (event)=>{\n                const isMetaKey = event.altKey || event.ctrlKey || event.metaKey;\n                const isTabKey = event.key === \"Tab\" && !isMetaKey;\n                if (isTabKey) {\n                    const focusedElement = document.activeElement;\n                    const isTabbingBackwards = event.shiftKey;\n                    const targetIsViewport = event.target === viewport;\n                    if (targetIsViewport && isTabbingBackwards) {\n                        headFocusProxyRef.current?.focus();\n                        return;\n                    }\n                    const tabbingDirection = isTabbingBackwards ? \"backwards\" : \"forwards\";\n                    const sortedCandidates = getSortedTabbableCandidates({\n                        tabbingDirection\n                    });\n                    const index = sortedCandidates.findIndex((candidate)=>candidate === focusedElement);\n                    if (focusFirst(sortedCandidates.slice(index + 1))) {\n                        event.preventDefault();\n                    } else {\n                        isTabbingBackwards ? headFocusProxyRef.current?.focus() : tailFocusProxyRef.current?.focus();\n                    }\n                }\n            };\n            viewport.addEventListener(\"keydown\", handleKeyDown);\n            return ()=>viewport.removeEventListener(\"keydown\", handleKeyDown);\n        }\n    }, [\n        getItems,\n        getSortedTabbableCandidates\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(_radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_6__.Branch, {\n        ref: wrapperRef,\n        role: \"region\",\n        \"aria-label\": label.replace(\"{hotkey}\", hotkeyLabel),\n        tabIndex: -1,\n        style: {\n            pointerEvents: hasToasts ? void 0 : \"none\"\n        },\n        children: [\n            hasToasts && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(FocusProxy, {\n                ref: headFocusProxyRef,\n                onFocusFromOutsideViewport: ()=>{\n                    const tabbableCandidates = getSortedTabbableCandidates({\n                        tabbingDirection: \"forwards\"\n                    });\n                    focusFirst(tabbableCandidates);\n                }\n            }),\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.Slot, {\n                scope: __scopeToast,\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.ol, {\n                    tabIndex: -1,\n                    ...viewportProps,\n                    ref: composedRefs\n                })\n            }),\n            hasToasts && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(FocusProxy, {\n                ref: tailFocusProxyRef,\n                onFocusFromOutsideViewport: ()=>{\n                    const tabbableCandidates = getSortedTabbableCandidates({\n                        tabbingDirection: \"backwards\"\n                    });\n                    focusFirst(tabbableCandidates);\n                }\n            })\n        ]\n    });\n});\nToastViewport.displayName = VIEWPORT_NAME;\nvar FOCUS_PROXY_NAME = \"ToastFocusProxy\";\nvar FocusProxy = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeToast, onFocusFromOutsideViewport, ...proxyProps } = props;\n    const context = useToastProviderContext(FOCUS_PROXY_NAME, __scopeToast);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_visually_hidden__WEBPACK_IMPORTED_MODULE_8__.VisuallyHidden, {\n        tabIndex: 0,\n        ...proxyProps,\n        ref: forwardedRef,\n        style: {\n            position: \"fixed\"\n        },\n        onFocus: (event)=>{\n            const prevFocusedElement = event.relatedTarget;\n            const isFocusFromOutsideViewport = !context.viewport?.contains(prevFocusedElement);\n            if (isFocusFromOutsideViewport) onFocusFromOutsideViewport();\n        }\n    });\n});\nFocusProxy.displayName = FOCUS_PROXY_NAME;\nvar TOAST_NAME = \"Toast\";\nvar TOAST_SWIPE_START = \"toast.swipeStart\";\nvar TOAST_SWIPE_MOVE = \"toast.swipeMove\";\nvar TOAST_SWIPE_CANCEL = \"toast.swipeCancel\";\nvar TOAST_SWIPE_END = \"toast.swipeEnd\";\nvar Toast = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { forceMount, open: openProp, defaultOpen, onOpenChange, ...toastProps } = props;\n    const [open, setOpen] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_9__.useControllableState)({\n        prop: openProp,\n        defaultProp: defaultOpen ?? true,\n        onChange: onOpenChange,\n        caller: TOAST_NAME\n    });\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_10__.Presence, {\n        present: forceMount || open,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(ToastImpl, {\n            open,\n            ...toastProps,\n            ref: forwardedRef,\n            onClose: ()=>setOpen(false),\n            onPause: (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__.useCallbackRef)(props.onPause),\n            onResume: (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__.useCallbackRef)(props.onResume),\n            onSwipeStart: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onSwipeStart, (event)=>{\n                event.currentTarget.setAttribute(\"data-swipe\", \"start\");\n            }),\n            onSwipeMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onSwipeMove, (event)=>{\n                const { x, y } = event.detail.delta;\n                event.currentTarget.setAttribute(\"data-swipe\", \"move\");\n                event.currentTarget.style.setProperty(\"--radix-toast-swipe-move-x\", `${x}px`);\n                event.currentTarget.style.setProperty(\"--radix-toast-swipe-move-y\", `${y}px`);\n            }),\n            onSwipeCancel: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onSwipeCancel, (event)=>{\n                event.currentTarget.setAttribute(\"data-swipe\", \"cancel\");\n                event.currentTarget.style.removeProperty(\"--radix-toast-swipe-move-x\");\n                event.currentTarget.style.removeProperty(\"--radix-toast-swipe-move-y\");\n                event.currentTarget.style.removeProperty(\"--radix-toast-swipe-end-x\");\n                event.currentTarget.style.removeProperty(\"--radix-toast-swipe-end-y\");\n            }),\n            onSwipeEnd: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onSwipeEnd, (event)=>{\n                const { x, y } = event.detail.delta;\n                event.currentTarget.setAttribute(\"data-swipe\", \"end\");\n                event.currentTarget.style.removeProperty(\"--radix-toast-swipe-move-x\");\n                event.currentTarget.style.removeProperty(\"--radix-toast-swipe-move-y\");\n                event.currentTarget.style.setProperty(\"--radix-toast-swipe-end-x\", `${x}px`);\n                event.currentTarget.style.setProperty(\"--radix-toast-swipe-end-y\", `${y}px`);\n                setOpen(false);\n            })\n        })\n    });\n});\nToast.displayName = TOAST_NAME;\nvar [ToastInteractiveProvider, useToastInteractiveContext] = createToastContext(TOAST_NAME, {\n    onClose () {}\n});\nvar ToastImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeToast, type = \"foreground\", duration: durationProp, open, onClose, onEscapeKeyDown, onPause, onResume, onSwipeStart, onSwipeMove, onSwipeCancel, onSwipeEnd, ...toastProps } = props;\n    const context = useToastProviderContext(TOAST_NAME, __scopeToast);\n    const [node, setNode] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs)(forwardedRef, (node2)=>setNode(node2));\n    const pointerStartRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const swipeDeltaRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const duration = durationProp || context.duration;\n    const closeTimerStartTimeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const closeTimerRemainingTimeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(duration);\n    const closeTimerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const { onToastAdd, onToastRemove } = context;\n    const handleClose = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__.useCallbackRef)(()=>{\n        const isFocusInToast = node?.contains(document.activeElement);\n        if (isFocusInToast) context.viewport?.focus();\n        onClose();\n    });\n    const startTimer = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((duration2)=>{\n        if (!duration2 || duration2 === Infinity) return;\n        window.clearTimeout(closeTimerRef.current);\n        closeTimerStartTimeRef.current = /* @__PURE__ */ new Date().getTime();\n        closeTimerRef.current = window.setTimeout(handleClose, duration2);\n    }, [\n        handleClose\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const viewport = context.viewport;\n        if (viewport) {\n            const handleResume = ()=>{\n                startTimer(closeTimerRemainingTimeRef.current);\n                onResume?.();\n            };\n            const handlePause = ()=>{\n                const elapsedTime = /* @__PURE__ */ new Date().getTime() - closeTimerStartTimeRef.current;\n                closeTimerRemainingTimeRef.current = closeTimerRemainingTimeRef.current - elapsedTime;\n                window.clearTimeout(closeTimerRef.current);\n                onPause?.();\n            };\n            viewport.addEventListener(VIEWPORT_PAUSE, handlePause);\n            viewport.addEventListener(VIEWPORT_RESUME, handleResume);\n            return ()=>{\n                viewport.removeEventListener(VIEWPORT_PAUSE, handlePause);\n                viewport.removeEventListener(VIEWPORT_RESUME, handleResume);\n            };\n        }\n    }, [\n        context.viewport,\n        duration,\n        onPause,\n        onResume,\n        startTimer\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (open && !context.isClosePausedRef.current) startTimer(duration);\n    }, [\n        open,\n        duration,\n        context.isClosePausedRef,\n        startTimer\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        onToastAdd();\n        return ()=>onToastRemove();\n    }, [\n        onToastAdd,\n        onToastRemove\n    ]);\n    const announceTextContent = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>{\n        return node ? getAnnounceTextContent(node) : null;\n    }, [\n        node\n    ]);\n    if (!context.viewport) return null;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.Fragment, {\n        children: [\n            announceTextContent && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(ToastAnnounce, {\n                __scopeToast,\n                role: \"status\",\n                \"aria-live\": type === \"foreground\" ? \"assertive\" : \"polite\",\n                children: announceTextContent\n            }),\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(ToastInteractiveProvider, {\n                scope: __scopeToast,\n                onClose: handleClose,\n                children: /*#__PURE__*/ react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal(/* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.ItemSlot, {\n                    scope: __scopeToast,\n                    children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_6__.Root, {\n                        asChild: true,\n                        onEscapeKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(onEscapeKeyDown, ()=>{\n                            if (!context.isFocusedToastEscapeKeyDownRef.current) handleClose();\n                            context.isFocusedToastEscapeKeyDownRef.current = false;\n                        }),\n                        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.li, {\n                            tabIndex: 0,\n                            \"data-state\": open ? \"open\" : \"closed\",\n                            \"data-swipe-direction\": context.swipeDirection,\n                            ...toastProps,\n                            ref: composedRefs,\n                            style: {\n                                userSelect: \"none\",\n                                touchAction: \"none\",\n                                ...props.style\n                            },\n                            onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onKeyDown, (event)=>{\n                                if (event.key !== \"Escape\") return;\n                                onEscapeKeyDown?.(event.nativeEvent);\n                                if (!event.nativeEvent.defaultPrevented) {\n                                    context.isFocusedToastEscapeKeyDownRef.current = true;\n                                    handleClose();\n                                }\n                            }),\n                            onPointerDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerDown, (event)=>{\n                                if (event.button !== 0) return;\n                                pointerStartRef.current = {\n                                    x: event.clientX,\n                                    y: event.clientY\n                                };\n                            }),\n                            onPointerMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerMove, (event)=>{\n                                if (!pointerStartRef.current) return;\n                                const x = event.clientX - pointerStartRef.current.x;\n                                const y = event.clientY - pointerStartRef.current.y;\n                                const hasSwipeMoveStarted = Boolean(swipeDeltaRef.current);\n                                const isHorizontalSwipe = [\n                                    \"left\",\n                                    \"right\"\n                                ].includes(context.swipeDirection);\n                                const clamp = [\n                                    \"left\",\n                                    \"up\"\n                                ].includes(context.swipeDirection) ? Math.min : Math.max;\n                                const clampedX = isHorizontalSwipe ? clamp(0, x) : 0;\n                                const clampedY = !isHorizontalSwipe ? clamp(0, y) : 0;\n                                const moveStartBuffer = event.pointerType === \"touch\" ? 10 : 2;\n                                const delta = {\n                                    x: clampedX,\n                                    y: clampedY\n                                };\n                                const eventDetail = {\n                                    originalEvent: event,\n                                    delta\n                                };\n                                if (hasSwipeMoveStarted) {\n                                    swipeDeltaRef.current = delta;\n                                    handleAndDispatchCustomEvent(TOAST_SWIPE_MOVE, onSwipeMove, eventDetail, {\n                                        discrete: false\n                                    });\n                                } else if (isDeltaInDirection(delta, context.swipeDirection, moveStartBuffer)) {\n                                    swipeDeltaRef.current = delta;\n                                    handleAndDispatchCustomEvent(TOAST_SWIPE_START, onSwipeStart, eventDetail, {\n                                        discrete: false\n                                    });\n                                    event.target.setPointerCapture(event.pointerId);\n                                } else if (Math.abs(x) > moveStartBuffer || Math.abs(y) > moveStartBuffer) {\n                                    pointerStartRef.current = null;\n                                }\n                            }),\n                            onPointerUp: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerUp, (event)=>{\n                                const delta = swipeDeltaRef.current;\n                                const target = event.target;\n                                if (target.hasPointerCapture(event.pointerId)) {\n                                    target.releasePointerCapture(event.pointerId);\n                                }\n                                swipeDeltaRef.current = null;\n                                pointerStartRef.current = null;\n                                if (delta) {\n                                    const toast = event.currentTarget;\n                                    const eventDetail = {\n                                        originalEvent: event,\n                                        delta\n                                    };\n                                    if (isDeltaInDirection(delta, context.swipeDirection, context.swipeThreshold)) {\n                                        handleAndDispatchCustomEvent(TOAST_SWIPE_END, onSwipeEnd, eventDetail, {\n                                            discrete: true\n                                        });\n                                    } else {\n                                        handleAndDispatchCustomEvent(TOAST_SWIPE_CANCEL, onSwipeCancel, eventDetail, {\n                                            discrete: true\n                                        });\n                                    }\n                                    toast.addEventListener(\"click\", (event2)=>event2.preventDefault(), {\n                                        once: true\n                                    });\n                                }\n                            })\n                        })\n                    })\n                }), context.viewport)\n            })\n        ]\n    });\n});\nvar ToastAnnounce = (props)=>{\n    const { __scopeToast, children, ...announceProps } = props;\n    const context = useToastProviderContext(TOAST_NAME, __scopeToast);\n    const [renderAnnounceText, setRenderAnnounceText] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [isAnnounced, setIsAnnounced] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    useNextFrame(()=>setRenderAnnounceText(true));\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const timer = window.setTimeout(()=>setIsAnnounced(true), 1e3);\n        return ()=>window.clearTimeout(timer);\n    }, []);\n    return isAnnounced ? null : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_13__.Portal, {\n        asChild: true,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_visually_hidden__WEBPACK_IMPORTED_MODULE_8__.VisuallyHidden, {\n            ...announceProps,\n            children: renderAnnounceText && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.Fragment, {\n                children: [\n                    context.label,\n                    \" \",\n                    children\n                ]\n            })\n        })\n    });\n};\nvar TITLE_NAME = \"ToastTitle\";\nvar ToastTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeToast, ...titleProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.div, {\n        ...titleProps,\n        ref: forwardedRef\n    });\n});\nToastTitle.displayName = TITLE_NAME;\nvar DESCRIPTION_NAME = \"ToastDescription\";\nvar ToastDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeToast, ...descriptionProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.div, {\n        ...descriptionProps,\n        ref: forwardedRef\n    });\n});\nToastDescription.displayName = DESCRIPTION_NAME;\nvar ACTION_NAME = \"ToastAction\";\nvar ToastAction = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { altText, ...actionProps } = props;\n    if (!altText.trim()) {\n        console.error(`Invalid prop \\`altText\\` supplied to \\`${ACTION_NAME}\\`. Expected non-empty \\`string\\`.`);\n        return null;\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(ToastAnnounceExclude, {\n        altText,\n        asChild: true,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(ToastClose, {\n            ...actionProps,\n            ref: forwardedRef\n        })\n    });\n});\nToastAction.displayName = ACTION_NAME;\nvar CLOSE_NAME = \"ToastClose\";\nvar ToastClose = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeToast, ...closeProps } = props;\n    const interactiveContext = useToastInteractiveContext(CLOSE_NAME, __scopeToast);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(ToastAnnounceExclude, {\n        asChild: true,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.button, {\n            type: \"button\",\n            ...closeProps,\n            ref: forwardedRef,\n            onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onClick, interactiveContext.onClose)\n        })\n    });\n});\nToastClose.displayName = CLOSE_NAME;\nvar ToastAnnounceExclude = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeToast, altText, ...announceExcludeProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.div, {\n        \"data-radix-toast-announce-exclude\": \"\",\n        \"data-radix-toast-announce-alt\": altText || void 0,\n        ...announceExcludeProps,\n        ref: forwardedRef\n    });\n});\nfunction getAnnounceTextContent(container) {\n    const textContent = [];\n    const childNodes = Array.from(container.childNodes);\n    childNodes.forEach((node)=>{\n        if (node.nodeType === node.TEXT_NODE && node.textContent) textContent.push(node.textContent);\n        if (isHTMLElement(node)) {\n            const isHidden = node.ariaHidden || node.hidden || node.style.display === \"none\";\n            const isExcluded = node.dataset.radixToastAnnounceExclude === \"\";\n            if (!isHidden) {\n                if (isExcluded) {\n                    const altText = node.dataset.radixToastAnnounceAlt;\n                    if (altText) textContent.push(altText);\n                } else {\n                    textContent.push(...getAnnounceTextContent(node));\n                }\n            }\n        }\n    });\n    return textContent;\n}\nfunction handleAndDispatchCustomEvent(name, handler, detail, { discrete }) {\n    const currentTarget = detail.originalEvent.currentTarget;\n    const event = new CustomEvent(name, {\n        bubbles: true,\n        cancelable: true,\n        detail\n    });\n    if (handler) currentTarget.addEventListener(name, handler, {\n        once: true\n    });\n    if (discrete) {\n        (0,_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.dispatchDiscreteCustomEvent)(currentTarget, event);\n    } else {\n        currentTarget.dispatchEvent(event);\n    }\n}\nvar isDeltaInDirection = (delta, direction, threshold = 0)=>{\n    const deltaX = Math.abs(delta.x);\n    const deltaY = Math.abs(delta.y);\n    const isDeltaX = deltaX > deltaY;\n    if (direction === \"left\" || direction === \"right\") {\n        return isDeltaX && deltaX > threshold;\n    } else {\n        return !isDeltaX && deltaY > threshold;\n    }\n};\nfunction useNextFrame(callback = ()=>{}) {\n    const fn = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__.useCallbackRef)(callback);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_14__.useLayoutEffect)(()=>{\n        let raf1 = 0;\n        let raf2 = 0;\n        raf1 = window.requestAnimationFrame(()=>raf2 = window.requestAnimationFrame(fn));\n        return ()=>{\n            window.cancelAnimationFrame(raf1);\n            window.cancelAnimationFrame(raf2);\n        };\n    }, [\n        fn\n    ]);\n}\nfunction isHTMLElement(node) {\n    return node.nodeType === node.ELEMENT_NODE;\n}\nfunction getTabbableCandidates(container) {\n    const nodes = [];\n    const walker = document.createTreeWalker(container, NodeFilter.SHOW_ELEMENT, {\n        acceptNode: (node)=>{\n            const isHiddenInput = node.tagName === \"INPUT\" && node.type === \"hidden\";\n            if (node.disabled || node.hidden || isHiddenInput) return NodeFilter.FILTER_SKIP;\n            return node.tabIndex >= 0 ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_SKIP;\n        }\n    });\n    while(walker.nextNode())nodes.push(walker.currentNode);\n    return nodes;\n}\nfunction focusFirst(candidates) {\n    const previouslyFocusedElement = document.activeElement;\n    return candidates.some((candidate)=>{\n        if (candidate === previouslyFocusedElement) return true;\n        candidate.focus();\n        return document.activeElement !== previouslyFocusedElement;\n    });\n}\nvar Provider = ToastProvider;\nvar Viewport = ToastViewport;\nvar Root2 = Toast;\nvar Title = ToastTitle;\nvar Description = ToastDescription;\nvar Action = ToastAction;\nvar Close = ToastClose;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-toast/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs":
/*!**********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCallbackRef: () => (/* binding */ useCallbackRef)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n// packages/react/use-callback-ref/src/use-callback-ref.tsx\n\nfunction useCallbackRef(callback) {\n    const callbackRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(callback);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        callbackRef.current = callback;\n    });\n    return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>(...args)=>callbackRef.current?.(...args), []);\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1jYWxsYmFjay1yZWYvZGlzdC9pbmRleC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQSwyREFBMkQ7QUFDNUI7QUFDL0IsU0FBU0MsZUFBZUMsUUFBUTtJQUM5QixNQUFNQyxjQUFjSCx5Q0FBWSxDQUFDRTtJQUNqQ0YsNENBQWUsQ0FBQztRQUNkRyxZQUFZRyxPQUFPLEdBQUdKO0lBQ3hCO0lBQ0EsT0FBT0YsMENBQWEsQ0FBQyxJQUFNLENBQUMsR0FBR1EsT0FBU0wsWUFBWUcsT0FBTyxNQUFNRSxPQUFPLEVBQUU7QUFDNUU7QUFHRSxDQUNGLGtDQUFrQyIsInNvdXJjZXMiOlsid2VicGFjazovL3N1cGVybWFya2V0LWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL0ByYWRpeC11aS9yZWFjdC11c2UtY2FsbGJhY2stcmVmL2Rpc3QvaW5kZXgubWpzPzExZjAiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gcGFja2FnZXMvcmVhY3QvdXNlLWNhbGxiYWNrLXJlZi9zcmMvdXNlLWNhbGxiYWNrLXJlZi50c3hcbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiO1xuZnVuY3Rpb24gdXNlQ2FsbGJhY2tSZWYoY2FsbGJhY2spIHtcbiAgY29uc3QgY2FsbGJhY2tSZWYgPSBSZWFjdC51c2VSZWYoY2FsbGJhY2spO1xuICBSZWFjdC51c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNhbGxiYWNrUmVmLmN1cnJlbnQgPSBjYWxsYmFjaztcbiAgfSk7XG4gIHJldHVybiBSZWFjdC51c2VNZW1vKCgpID0+ICguLi5hcmdzKSA9PiBjYWxsYmFja1JlZi5jdXJyZW50Py4oLi4uYXJncyksIFtdKTtcbn1cbmV4cG9ydCB7XG4gIHVzZUNhbGxiYWNrUmVmXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXgubWpzLm1hcFxuIl0sIm5hbWVzIjpbIlJlYWN0IiwidXNlQ2FsbGJhY2tSZWYiLCJjYWxsYmFjayIsImNhbGxiYWNrUmVmIiwidXNlUmVmIiwidXNlRWZmZWN0IiwiY3VycmVudCIsInVzZU1lbW8iLCJhcmdzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs":
/*!****************************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("var react__WEBPACK_IMPORTED_MODULE_0___namespace_cache;\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useControllableState: () => (/* binding */ useControllableState),\n/* harmony export */   useControllableStateReducer: () => (/* binding */ useControllableStateReducer)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_effect_event__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-use-effect-event */ \"(ssr)/./node_modules/@radix-ui/react-use-effect-event/dist/index.mjs\");\n// src/use-controllable-state.tsx\n\n\nvar useInsertionEffect = /*#__PURE__*/ (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(react__WEBPACK_IMPORTED_MODULE_0__, 2)))[\" useInsertionEffect \".trim().toString()] || _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect;\nfunction useControllableState({ prop, defaultProp, onChange = ()=>{}, caller }) {\n    const [uncontrolledProp, setUncontrolledProp, onChangeRef] = useUncontrolledState({\n        defaultProp,\n        onChange\n    });\n    const isControlled = prop !== void 0;\n    const value = isControlled ? prop : uncontrolledProp;\n    if (true) {\n        const isControlledRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(prop !== void 0);\n        react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n            const wasControlled = isControlledRef.current;\n            if (wasControlled !== isControlled) {\n                const from = wasControlled ? \"controlled\" : \"uncontrolled\";\n                const to = isControlled ? \"controlled\" : \"uncontrolled\";\n                console.warn(`${caller} is changing from ${from} to ${to}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`);\n            }\n            isControlledRef.current = isControlled;\n        }, [\n            isControlled,\n            caller\n        ]);\n    }\n    const setValue = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((nextValue)=>{\n        if (isControlled) {\n            const value2 = isFunction(nextValue) ? nextValue(prop) : nextValue;\n            if (value2 !== prop) {\n                onChangeRef.current?.(value2);\n            }\n        } else {\n            setUncontrolledProp(nextValue);\n        }\n    }, [\n        isControlled,\n        prop,\n        setUncontrolledProp,\n        onChangeRef\n    ]);\n    return [\n        value,\n        setValue\n    ];\n}\nfunction useUncontrolledState({ defaultProp, onChange }) {\n    const [value, setValue] = react__WEBPACK_IMPORTED_MODULE_0__.useState(defaultProp);\n    const prevValueRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(value);\n    const onChangeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(onChange);\n    useInsertionEffect(()=>{\n        onChangeRef.current = onChange;\n    }, [\n        onChange\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (prevValueRef.current !== value) {\n            onChangeRef.current?.(value);\n            prevValueRef.current = value;\n        }\n    }, [\n        value,\n        prevValueRef\n    ]);\n    return [\n        value,\n        setValue,\n        onChangeRef\n    ];\n}\nfunction isFunction(value) {\n    return typeof value === \"function\";\n}\n// src/use-controllable-state-reducer.tsx\n\n\nvar SYNC_STATE = Symbol(\"RADIX:SYNC_STATE\");\nfunction useControllableStateReducer(reducer, userArgs, initialArg, init) {\n    const { prop: controlledState, defaultProp, onChange: onChangeProp, caller } = userArgs;\n    const isControlled = controlledState !== void 0;\n    const onChange = (0,_radix_ui_react_use_effect_event__WEBPACK_IMPORTED_MODULE_2__.useEffectEvent)(onChangeProp);\n    if (true) {\n        const isControlledRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(controlledState !== void 0);\n        react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n            const wasControlled = isControlledRef.current;\n            if (wasControlled !== isControlled) {\n                const from = wasControlled ? \"controlled\" : \"uncontrolled\";\n                const to = isControlled ? \"controlled\" : \"uncontrolled\";\n                console.warn(`${caller} is changing from ${from} to ${to}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`);\n            }\n            isControlledRef.current = isControlled;\n        }, [\n            isControlled,\n            caller\n        ]);\n    }\n    const args = [\n        {\n            ...initialArg,\n            state: defaultProp\n        }\n    ];\n    if (init) {\n        args.push(init);\n    }\n    const [internalState, dispatch] = react__WEBPACK_IMPORTED_MODULE_0__.useReducer((state2, action)=>{\n        if (action.type === SYNC_STATE) {\n            return {\n                ...state2,\n                state: action.state\n            };\n        }\n        const next = reducer(state2, action);\n        if (isControlled && !Object.is(next.state, state2.state)) {\n            onChange(next.state);\n        }\n        return next;\n    }, ...args);\n    const uncontrolledState = internalState.state;\n    const prevValueRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(uncontrolledState);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (prevValueRef.current !== uncontrolledState) {\n            prevValueRef.current = uncontrolledState;\n            if (!isControlled) {\n                onChange(uncontrolledState);\n            }\n        }\n    }, [\n        onChange,\n        uncontrolledState,\n        prevValueRef,\n        isControlled\n    ]);\n    const state = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>{\n        const isControlled2 = controlledState !== void 0;\n        if (isControlled2) {\n            return {\n                ...internalState,\n                state: controlledState\n            };\n        }\n        return internalState;\n    }, [\n        internalState,\n        controlledState\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (isControlled && !Object.is(controlledState, internalState.state)) {\n            dispatch({\n                type: SYNC_STATE,\n                state: controlledState\n            });\n        }\n    }, [\n        controlledState,\n        internalState.state,\n        isControlled\n    ]);\n    return [\n        state,\n        dispatch\n    ];\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-effect-event/dist/index.mjs":
/*!**********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-effect-event/dist/index.mjs ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("var react__WEBPACK_IMPORTED_MODULE_0___namespace_cache;\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useEffectEvent: () => (/* binding */ useEffectEvent)\n/* harmony export */ });\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n// src/use-effect-event.tsx\n\n\nvar useReactEffectEvent = /*#__PURE__*/ (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(react__WEBPACK_IMPORTED_MODULE_0__, 2)))[\" useEffectEvent \".trim().toString()];\nvar useReactInsertionEffect = /*#__PURE__*/ (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(react__WEBPACK_IMPORTED_MODULE_0__, 2)))[\" useInsertionEffect \".trim().toString()];\nfunction useEffectEvent(callback) {\n    if (typeof useReactEffectEvent === \"function\") {\n        return useReactEffectEvent(callback);\n    }\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(()=>{\n        throw new Error(\"Cannot call an event handler while rendering.\");\n    });\n    if (typeof useReactInsertionEffect === \"function\") {\n        useReactInsertionEffect(()=>{\n            ref.current = callback;\n        });\n    } else {\n        (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(()=>{\n            ref.current = callback;\n        });\n    }\n    return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>(...args)=>ref.current?.(...args), []);\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-effect-event/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs":
/*!************************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useEscapeKeydown: () => (/* binding */ useEscapeKeydown)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n// packages/react/use-escape-keydown/src/use-escape-keydown.tsx\n\n\nfunction useEscapeKeydown(onEscapeKeyDownProp, ownerDocument = globalThis?.document) {\n    const onEscapeKeyDown = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_1__.useCallbackRef)(onEscapeKeyDownProp);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const handleKeyDown = (event)=>{\n            if (event.key === \"Escape\") {\n                onEscapeKeyDown(event);\n            }\n        };\n        ownerDocument.addEventListener(\"keydown\", handleKeyDown, {\n            capture: true\n        });\n        return ()=>ownerDocument.removeEventListener(\"keydown\", handleKeyDown, {\n                capture: true\n            });\n    }, [\n        onEscapeKeyDown,\n        ownerDocument\n    ]);\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useLayoutEffect: () => (/* binding */ useLayoutEffect2)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n// packages/react/use-layout-effect/src/use-layout-effect.tsx\n\nvar useLayoutEffect2 = globalThis?.document ? react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect : ()=>{};\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1sYXlvdXQtZWZmZWN0L2Rpc3QvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUEsNkRBQTZEO0FBQzlCO0FBQy9CLElBQUlDLG1CQUFtQkMsWUFBWUMsV0FBV0gsa0RBQXFCLEdBQUcsS0FDdEU7QUFHRSxDQUNGLGtDQUFrQyIsInNvdXJjZXMiOlsid2VicGFjazovL3N1cGVybWFya2V0LWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL0ByYWRpeC11aS9yZWFjdC11c2UtbGF5b3V0LWVmZmVjdC9kaXN0L2luZGV4Lm1qcz8yZDZmIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHBhY2thZ2VzL3JlYWN0L3VzZS1sYXlvdXQtZWZmZWN0L3NyYy91c2UtbGF5b3V0LWVmZmVjdC50c3hcbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiO1xudmFyIHVzZUxheW91dEVmZmVjdDIgPSBnbG9iYWxUaGlzPy5kb2N1bWVudCA/IFJlYWN0LnVzZUxheW91dEVmZmVjdCA6ICgpID0+IHtcbn07XG5leHBvcnQge1xuICB1c2VMYXlvdXRFZmZlY3QyIGFzIHVzZUxheW91dEVmZmVjdFxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4Lm1qcy5tYXBcbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZUxheW91dEVmZmVjdDIiLCJnbG9iYWxUaGlzIiwiZG9jdW1lbnQiLCJ1c2VMYXlvdXRFZmZlY3QiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-visually-hidden/dist/index.mjs":
/*!*********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-visually-hidden/dist/index.mjs ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   VISUALLY_HIDDEN_STYLES: () => (/* binding */ VISUALLY_HIDDEN_STYLES),\n/* harmony export */   VisuallyHidden: () => (/* binding */ VisuallyHidden)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// src/visually-hidden.tsx\n\n\n\nvar VISUALLY_HIDDEN_STYLES = Object.freeze({\n    // See: https://github.com/twbs/bootstrap/blob/main/scss/mixins/_visually-hidden.scss\n    position: \"absolute\",\n    border: 0,\n    width: 1,\n    height: 1,\n    padding: 0,\n    margin: -1,\n    overflow: \"hidden\",\n    clip: \"rect(0, 0, 0, 0)\",\n    whiteSpace: \"nowrap\",\n    wordWrap: \"normal\"\n});\nvar NAME = \"VisuallyHidden\";\nvar VisuallyHidden = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__.Primitive.span, {\n        ...props,\n        ref: forwardedRef,\n        style: {\n            ...VISUALLY_HIDDEN_STYLES,\n            ...props.style\n        }\n    });\n});\nVisuallyHidden.displayName = NAME;\nvar Root = VisuallyHidden;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-visually-hidden/dist/index.mjs\n");

/***/ })

};
;