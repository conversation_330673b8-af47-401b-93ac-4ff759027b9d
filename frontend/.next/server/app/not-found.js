/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/not-found";
exports.ids = ["app/not-found"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?ed2e":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?abbf":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fnot-found&page=%2Fnot-found&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=%2FVolumes%2F2tb%2Fgithub%2Fsuper-scraper%2Ffrontend%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FVolumes%2F2tb%2Fgithub%2Fsuper-scraper%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fnot-found&page=%2Fnot-found&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=%2FVolumes%2F2tb%2Fgithub%2Fsuper-scraper%2Ffrontend%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FVolumes%2F2tb%2Fgithub%2Fsuper-scraper%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n          '__DEFAULT__',\n          {},\n          {\n            defaultPage: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/parallel-route-default */ \"(rsc)/./node_modules/next/dist/client/components/parallel-route-default.js\", 23)), \"next/dist/client/components/parallel-route-default\"],\n          }\n        ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"/Volumes/2tb/github/super-scraper/frontend/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/not-found\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/not-found\",\n        pathname: \"/not-found\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fnot-found&page=%2Fnot-found&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=%2FVolumes%2F2tb%2Fgithub%2Fsuper-scraper%2Ffrontend%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FVolumes%2F2tb%2Fgithub%2Fsuper-scraper%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FVolumes%2F2tb%2Fgithub%2Fsuper-scraper%2Ffrontend%2Fapp%2Fproviders.tsx&modules=%2FVolumes%2F2tb%2Fgithub%2Fsuper-scraper%2Ffrontend%2Fcomponents%2Flayout%2FHeader.tsx&modules=%2FVolumes%2F2tb%2Fgithub%2Fsuper-scraper%2Ffrontend%2Fcomponents%2Fui%2Ftoaster.tsx&modules=%2FVolumes%2F2tb%2Fgithub%2Fsuper-scraper%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22app%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FVolumes%2F2tb%2Fgithub%2Fsuper-scraper%2Ffrontend%2Fapp%2Fglobals.css&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FVolumes%2F2tb%2Fgithub%2Fsuper-scraper%2Ffrontend%2Fapp%2Fproviders.tsx&modules=%2FVolumes%2F2tb%2Fgithub%2Fsuper-scraper%2Ffrontend%2Fcomponents%2Flayout%2FHeader.tsx&modules=%2FVolumes%2F2tb%2Fgithub%2Fsuper-scraper%2Ffrontend%2Fcomponents%2Fui%2Ftoaster.tsx&modules=%2FVolumes%2F2tb%2Fgithub%2Fsuper-scraper%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22app%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FVolumes%2F2tb%2Fgithub%2Fsuper-scraper%2Ffrontend%2Fapp%2Fglobals.css&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/providers.tsx */ \"(ssr)/./app/providers.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/layout/Header.tsx */ \"(ssr)/./components/layout/Header.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ui/toaster.tsx */ \"(ssr)/./components/ui/toaster.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVm9sdW1lcyUyRjJ0YiUyRmdpdGh1YiUyRnN1cGVyLXNjcmFwZXIlMkZmcm9udGVuZCUyRmFwcCUyRnByb3ZpZGVycy50c3gmbW9kdWxlcz0lMkZWb2x1bWVzJTJGMnRiJTJGZ2l0aHViJTJGc3VwZXItc2NyYXBlciUyRmZyb250ZW5kJTJGY29tcG9uZW50cyUyRmxheW91dCUyRkhlYWRlci50c3gmbW9kdWxlcz0lMkZWb2x1bWVzJTJGMnRiJTJGZ2l0aHViJTJGc3VwZXItc2NyYXBlciUyRmZyb250ZW5kJTJGY29tcG9uZW50cyUyRnVpJTJGdG9hc3Rlci50c3gmbW9kdWxlcz0lMkZWb2x1bWVzJTJGMnRiJTJGZ2l0aHViJTJGc3VwZXItc2NyYXBlciUyRmZyb250ZW5kJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmZvbnQlMkZnb29nbGUlMkZ0YXJnZXQuY3NzJTNGJTdCJTIycGF0aCUyMiUzQSUyMmFwcCUyRmxheW91dC50c3glMjIlMkMlMjJpbXBvcnQlMjIlM0ElMjJJbnRlciUyMiUyQyUyMmFyZ3VtZW50cyUyMiUzQSU1QiU3QiUyMnN1YnNldHMlMjIlM0ElNUIlMjJsYXRpbiUyMiU1RCU3RCU1RCUyQyUyMnZhcmlhYmxlTmFtZSUyMiUzQSUyMmludGVyJTIyJTdEJm1vZHVsZXM9JTJGVm9sdW1lcyUyRjJ0YiUyRmdpdGh1YiUyRnN1cGVyLXNjcmFwZXIlMkZmcm9udGVuZCUyRmFwcCUyRmdsb2JhbHMuY3NzJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxrSkFBaUc7QUFDakcsd0tBQTRHO0FBQzVHIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3VwZXJtYXJrZXQtZnJvbnRlbmQvPzkwMDQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVm9sdW1lcy8ydGIvZ2l0aHViL3N1cGVyLXNjcmFwZXIvZnJvbnRlbmQvYXBwL3Byb3ZpZGVycy50c3hcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Wb2x1bWVzLzJ0Yi9naXRodWIvc3VwZXItc2NyYXBlci9mcm9udGVuZC9jb21wb25lbnRzL2xheW91dC9IZWFkZXIudHN4XCIpO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVm9sdW1lcy8ydGIvZ2l0aHViL3N1cGVyLXNjcmFwZXIvZnJvbnRlbmQvY29tcG9uZW50cy91aS90b2FzdGVyLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FVolumes%2F2tb%2Fgithub%2Fsuper-scraper%2Ffrontend%2Fapp%2Fproviders.tsx&modules=%2FVolumes%2F2tb%2Fgithub%2Fsuper-scraper%2Ffrontend%2Fcomponents%2Flayout%2FHeader.tsx&modules=%2FVolumes%2F2tb%2Fgithub%2Fsuper-scraper%2Ffrontend%2Fcomponents%2Fui%2Ftoaster.tsx&modules=%2FVolumes%2F2tb%2Fgithub%2Fsuper-scraper%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22app%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FVolumes%2F2tb%2Fgithub%2Fsuper-scraper%2Ffrontend%2Fapp%2Fglobals.css&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FVolumes%2F2tb%2Fgithub%2Fsuper-scraper%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FVolumes%2F2tb%2Fgithub%2Fsuper-scraper%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FVolumes%2F2tb%2Fgithub%2Fsuper-scraper%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FVolumes%2F2tb%2Fgithub%2Fsuper-scraper%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FVolumes%2F2tb%2Fgithub%2Fsuper-scraper%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FVolumes%2F2tb%2Fgithub%2Fsuper-scraper%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FVolumes%2F2tb%2Fgithub%2Fsuper-scraper%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FVolumes%2F2tb%2Fgithub%2Fsuper-scraper%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FVolumes%2F2tb%2Fgithub%2Fsuper-scraper%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FVolumes%2F2tb%2Fgithub%2Fsuper-scraper%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FVolumes%2F2tb%2Fgithub%2Fsuper-scraper%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FVolumes%2F2tb%2Fgithub%2Fsuper-scraper%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FVolumes%2F2tb%2Fgithub%2Fsuper-scraper%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FVolumes%2F2tb%2Fgithub%2Fsuper-scraper%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FVolumes%2F2tb%2Fgithub%2Fsuper-scraper%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FVolumes%2F2tb%2Fgithub%2Fsuper-scraper%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FVolumes%2F2tb%2Fgithub%2Fsuper-scraper%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FVolumes%2F2tb%2Fgithub%2Fsuper-scraper%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/providers.tsx":
/*!***************************!*\
  !*** ./app/providers.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers),\n/* harmony export */   useApi: () => (/* binding */ useApi)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @supabase/auth-helpers-nextjs */ \"(ssr)/./node_modules/@supabase/auth-helpers-nextjs/dist/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _supabase_auth_helpers_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @supabase/auth-helpers-react */ \"(ssr)/./node_modules/@supabase/auth-helpers-react/dist/index.js\");\n/* harmony import */ var _supabase_auth_helpers_react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_supabase_auth_helpers_react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _lib_socket__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/socket */ \"(ssr)/./lib/socket.tsx\");\n/* harmony import */ var _components_theme_provider__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/theme-provider */ \"(ssr)/./components/theme-provider.tsx\");\n/* __next_internal_client_entry_do_not_use__ useApi,Providers auto */ \n\n\n\n\n\n// Create Supabase client with fallback values\nconst supabaseUrl = \"https://your-project.supabase.co\" || 0;\nconst supabaseAnonKey = \"your_anon_key_here\" || 0;\nconst supabase = (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2__.createClientComponentClient)({\n    supabaseUrl,\n    supabaseKey: supabaseAnonKey\n});\nconst ApiContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    apiUrl: \"http://localhost:5001\" || 0,\n    isOnline: true\n});\nconst useApi = ()=>(0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ApiContext);\n// Online status hook\nfunction useOnlineStatus() {\n    const [isOnline, setIsOnline] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleOnline = ()=>setIsOnline(true);\n        const handleOffline = ()=>setIsOnline(false);\n        window.addEventListener(\"online\", handleOnline);\n        window.addEventListener(\"offline\", handleOffline);\n        // Check initial status\n        setIsOnline(navigator.onLine);\n        return ()=>{\n            window.removeEventListener(\"online\", handleOnline);\n            window.removeEventListener(\"offline\", handleOffline);\n        };\n    }, []);\n    return isOnline;\n}\nfunction Providers({ children }) {\n    const isOnline = useOnlineStatus();\n    const apiUrl = \"http://localhost:5001\" || 0;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_theme_provider__WEBPACK_IMPORTED_MODULE_5__.ThemeProvider, {\n        attribute: \"class\",\n        defaultTheme: \"system\",\n        enableSystem: true,\n        disableTransitionOnChange: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_supabase_auth_helpers_react__WEBPACK_IMPORTED_MODULE_3__.SessionContextProvider, {\n            supabaseClient: supabase,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ApiContext.Provider, {\n                value: {\n                    apiUrl,\n                    isOnline\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_socket__WEBPACK_IMPORTED_MODULE_4__.SocketProvider, {\n                    url: apiUrl,\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/Volumes/2tb/github/super-scraper/frontend/app/providers.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Volumes/2tb/github/super-scraper/frontend/app/providers.tsx\",\n                lineNumber: 66,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Volumes/2tb/github/super-scraper/frontend/app/providers.tsx\",\n            lineNumber: 65,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Volumes/2tb/github/super-scraper/frontend/app/providers.tsx\",\n        lineNumber: 59,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/providers.tsx\n");

/***/ }),

/***/ "(ssr)/./components/layout/Header.tsx":
/*!**************************************!*\
  !*** ./components/layout/Header.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Header: () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Menu_Search_ShoppingCart_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,Search,ShoppingCart!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Menu_Search_ShoppingCart_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,Search,ShoppingCart!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Menu_Search_ShoppingCart_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,Search,ShoppingCart!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _lib_store__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/store */ \"(ssr)/./lib/store.ts\");\n/* __next_internal_client_entry_do_not_use__ Header auto */ \n\n\n\nfunction Header() {\n    const { getTotalItems } = (0,_lib_store__WEBPACK_IMPORTED_MODULE_2__.useCartStore)();\n    const totalItems = getTotalItems();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container flex h-14 items-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mr-4 hidden md:flex\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                        href: \"/\",\n                        className: \"mr-6 flex items-center space-x-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"hidden font-bold sm:inline-block\",\n                            children: \"SuperCompare\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/2tb/github/super-scraper/frontend/components/layout/Header.tsx\",\n                            lineNumber: 17,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Volumes/2tb/github/super-scraper/frontend/components/layout/Header.tsx\",\n                        lineNumber: 16,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Volumes/2tb/github/super-scraper/frontend/components/layout/Header.tsx\",\n                    lineNumber: 15,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    className: \"mr-2 px-0 md:hidden\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Search_ShoppingCart_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        className: \"h-6 w-6\"\n                    }, void 0, false, {\n                        fileName: \"/Volumes/2tb/github/super-scraper/frontend/components/layout/Header.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Volumes/2tb/github/super-scraper/frontend/components/layout/Header.tsx\",\n                    lineNumber: 24,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"flex items-center space-x-6 text-sm font-medium\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: \"/\",\n                            className: \"transition-colors hover:text-foreground/80 text-foreground\",\n                            children: \"Productos\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/2tb/github/super-scraper/frontend/components/layout/Header.tsx\",\n                            lineNumber: 30,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: \"/compare\",\n                            className: \"transition-colors hover:text-foreground/80 text-foreground/60\",\n                            children: \"Comparar\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/2tb/github/super-scraper/frontend/components/layout/Header.tsx\",\n                            lineNumber: 36,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: \"/admin\",\n                            className: \"transition-colors hover:text-foreground/80 text-foreground/60\",\n                            children: \"Admin\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/2tb/github/super-scraper/frontend/components/layout/Header.tsx\",\n                            lineNumber: 42,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/2tb/github/super-scraper/frontend/components/layout/Header.tsx\",\n                    lineNumber: 29,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-1 items-center justify-between space-x-2 md:justify-end\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full flex-1 md:w-auto md:flex-none\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Search_ShoppingCart_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/2tb/github/super-scraper/frontend/components/layout/Header.tsx\",\n                                        lineNumber: 55,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"search\",\n                                        placeholder: \"Buscar productos...\",\n                                        className: \"flex h-9 w-full rounded-md border border-input bg-transparent px-8 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:w-[200px] lg:w-[300px]\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/2tb/github/super-scraper/frontend/components/layout/Header.tsx\",\n                                        lineNumber: 56,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/2tb/github/super-scraper/frontend/components/layout/Header.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/2tb/github/super-scraper/frontend/components/layout/Header.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: \"/cart\",\n                            className: \"relative flex items-center space-x-2 rounded-md px-3 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Search_ShoppingCart_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/2tb/github/super-scraper/frontend/components/layout/Header.tsx\",\n                                    lineNumber: 69,\n                                    columnNumber: 13\n                                }, this),\n                                totalItems > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"absolute -top-1 -right-1 flex h-5 w-5 items-center justify-center rounded-full bg-primary text-xs text-primary-foreground\",\n                                    children: totalItems\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/2tb/github/super-scraper/frontend/components/layout/Header.tsx\",\n                                    lineNumber: 71,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"hidden sm:inline\",\n                                    children: \"Carrito\"\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/2tb/github/super-scraper/frontend/components/layout/Header.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/2tb/github/super-scraper/frontend/components/layout/Header.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/2tb/github/super-scraper/frontend/components/layout/Header.tsx\",\n                    lineNumber: 51,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Volumes/2tb/github/super-scraper/frontend/components/layout/Header.tsx\",\n            lineNumber: 13,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Volumes/2tb/github/super-scraper/frontend/components/layout/Header.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/layout/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./components/theme-provider.tsx":
/*!***************************************!*\
  !*** ./components/theme-provider.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.module.js\");\n/* __next_internal_client_entry_do_not_use__ ThemeProvider auto */ \n\n\nfunction ThemeProvider({ children, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Volumes/2tb/github/super-scraper/frontend/components/theme-provider.tsx\",\n        lineNumber: 8,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3RoZW1lLXByb3ZpZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBRThCO0FBQ21DO0FBRzFELFNBQVNDLGNBQWMsRUFBRUUsUUFBUSxFQUFFLEdBQUdDLE9BQTJCO0lBQ3RFLHFCQUFPLDhEQUFDRixzREFBa0JBO1FBQUUsR0FBR0UsS0FBSztrQkFBR0Q7Ozs7OztBQUN6QyIsInNvdXJjZXMiOlsid2VicGFjazovL3N1cGVybWFya2V0LWZyb250ZW5kLy4vY29tcG9uZW50cy90aGVtZS1wcm92aWRlci50c3g/OTI4OSJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIlxuXG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIlxuaW1wb3J0IHsgVGhlbWVQcm92aWRlciBhcyBOZXh0VGhlbWVzUHJvdmlkZXIgfSBmcm9tIFwibmV4dC10aGVtZXNcIlxuaW1wb3J0IHsgdHlwZSBUaGVtZVByb3ZpZGVyUHJvcHMgfSBmcm9tIFwibmV4dC10aGVtZXMvZGlzdC90eXBlc1wiXG5cbmV4cG9ydCBmdW5jdGlvbiBUaGVtZVByb3ZpZGVyKHsgY2hpbGRyZW4sIC4uLnByb3BzIH06IFRoZW1lUHJvdmlkZXJQcm9wcykge1xuICByZXR1cm4gPE5leHRUaGVtZXNQcm92aWRlciB7Li4ucHJvcHN9PntjaGlsZHJlbn08L05leHRUaGVtZXNQcm92aWRlcj5cbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsIlRoZW1lUHJvdmlkZXIiLCJOZXh0VGhlbWVzUHJvdmlkZXIiLCJjaGlsZHJlbiIsInByb3BzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/theme-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/toast.tsx":
/*!*********************************!*\
  !*** ./components/ui/toast.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toast: () => (/* binding */ Toast),\n/* harmony export */   ToastAction: () => (/* binding */ ToastAction),\n/* harmony export */   ToastClose: () => (/* binding */ ToastClose),\n/* harmony export */   ToastDescription: () => (/* binding */ ToastDescription),\n/* harmony export */   ToastProvider: () => (/* binding */ ToastProvider),\n/* harmony export */   ToastTitle: () => (/* binding */ ToastTitle),\n/* harmony export */   ToastViewport: () => (/* binding */ ToastViewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-toast */ \"(ssr)/./node_modules/@radix-ui/react-toast/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ ToastProvider,ToastViewport,Toast,ToastTitle,ToastDescription,ToastClose,ToastAction auto */ \n\n\n\n\n\nconst ToastProvider = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Provider;\nconst ToastViewport = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Viewport, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Volumes/2tb/github/super-scraper/frontend/components/ui/toast.tsx\",\n        lineNumber: 16,\n        columnNumber: 3\n    }, undefined));\nToastViewport.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Viewport.displayName;\nconst toastVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full\", {\n    variants: {\n        variant: {\n            default: \"border bg-background text-foreground\",\n            destructive: \"destructive border-destructive bg-destructive text-destructive-foreground\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nconst Toast = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Root, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(toastVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Volumes/2tb/github/super-scraper/frontend/components/ui/toast.tsx\",\n        lineNumber: 49,\n        columnNumber: 5\n    }, undefined);\n});\nToast.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Root.displayName;\nconst ToastAction = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Action, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Volumes/2tb/github/super-scraper/frontend/components/ui/toast.tsx\",\n        lineNumber: 62,\n        columnNumber: 3\n    }, undefined));\nToastAction.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Action.displayName;\nconst ToastClose = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Close, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600\", className),\n        \"toast-close\": \"\",\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"/Volumes/2tb/github/super-scraper/frontend/components/ui/toast.tsx\",\n            lineNumber: 86,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Volumes/2tb/github/super-scraper/frontend/components/ui/toast.tsx\",\n        lineNumber: 77,\n        columnNumber: 3\n    }, undefined));\nToastClose.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Close.displayName;\nconst ToastTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Title, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-sm font-semibold\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Volumes/2tb/github/super-scraper/frontend/components/ui/toast.tsx\",\n        lineNumber: 95,\n        columnNumber: 3\n    }, undefined));\nToastTitle.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Title.displayName;\nconst ToastDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Description, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-sm opacity-90\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Volumes/2tb/github/super-scraper/frontend/components/ui/toast.tsx\",\n        lineNumber: 107,\n        columnNumber: 3\n    }, undefined));\nToastDescription.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Description.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/toast.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/toaster.tsx":
/*!***********************************!*\
  !*** ./components/ui/toaster.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toaster: () => (/* binding */ Toaster)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _use_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./use-toast */ \"(ssr)/./components/ui/use-toast.ts\");\n/* harmony import */ var _toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./toast */ \"(ssr)/./components/ui/toast.tsx\");\n/* __next_internal_client_entry_do_not_use__ Toaster auto */ \n\n\nfunction Toaster() {\n    const { toasts } = (0,_use_toast__WEBPACK_IMPORTED_MODULE_1__.useToast)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_toast__WEBPACK_IMPORTED_MODULE_2__.ToastProvider, {\n        children: [\n            toasts.map(function({ id, title, description, action, ...props }) {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_toast__WEBPACK_IMPORTED_MODULE_2__.Toast, {\n                    ...props,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid gap-1\",\n                            children: [\n                                title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_toast__WEBPACK_IMPORTED_MODULE_2__.ToastTitle, {\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/2tb/github/super-scraper/frontend/components/ui/toaster.tsx\",\n                                    lineNumber: 22,\n                                    columnNumber: 25\n                                }, this),\n                                description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_toast__WEBPACK_IMPORTED_MODULE_2__.ToastDescription, {\n                                    children: description\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/2tb/github/super-scraper/frontend/components/ui/toaster.tsx\",\n                                    lineNumber: 24,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/2tb/github/super-scraper/frontend/components/ui/toaster.tsx\",\n                            lineNumber: 21,\n                            columnNumber: 13\n                        }, this),\n                        action,\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_toast__WEBPACK_IMPORTED_MODULE_2__.ToastClose, {}, void 0, false, {\n                            fileName: \"/Volumes/2tb/github/super-scraper/frontend/components/ui/toaster.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, id, true, {\n                    fileName: \"/Volumes/2tb/github/super-scraper/frontend/components/ui/toaster.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 11\n                }, this);\n            }),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_toast__WEBPACK_IMPORTED_MODULE_2__.ToastViewport, {}, void 0, false, {\n                fileName: \"/Volumes/2tb/github/super-scraper/frontend/components/ui/toaster.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Volumes/2tb/github/super-scraper/frontend/components/ui/toaster.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/toaster.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/use-toast.ts":
/*!************************************!*\
  !*** ./components/ui/use-toast.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reducer: () => (/* binding */ reducer),\n/* harmony export */   toast: () => (/* binding */ toast),\n/* harmony export */   useToast: () => (/* binding */ useToast)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ reducer,useToast,toast auto */ \nconst TOAST_LIMIT = 1;\nconst TOAST_REMOVE_DELAY = 1000000;\nconst actionTypes = {\n    ADD_TOAST: \"ADD_TOAST\",\n    UPDATE_TOAST: \"UPDATE_TOAST\",\n    DISMISS_TOAST: \"DISMISS_TOAST\",\n    REMOVE_TOAST: \"REMOVE_TOAST\"\n};\nlet count = 0;\nfunction genId() {\n    count = (count + 1) % Number.MAX_SAFE_INTEGER;\n    return count.toString();\n}\nconst toastTimeouts = new Map();\nconst addToRemoveQueue = (toastId)=>{\n    if (toastTimeouts.has(toastId)) {\n        return;\n    }\n    const timeout = setTimeout(()=>{\n        toastTimeouts.delete(toastId);\n        dispatch({\n            type: \"REMOVE_TOAST\",\n            toastId: toastId\n        });\n    }, TOAST_REMOVE_DELAY);\n    toastTimeouts.set(toastId, timeout);\n};\nconst reducer = (state, action)=>{\n    switch(action.type){\n        case \"ADD_TOAST\":\n            return {\n                ...state,\n                toasts: [\n                    action.toast,\n                    ...state.toasts\n                ].slice(0, TOAST_LIMIT)\n            };\n        case \"UPDATE_TOAST\":\n            return {\n                ...state,\n                toasts: state.toasts.map((t)=>t.id === action.toast.id ? {\n                        ...t,\n                        ...action.toast\n                    } : t)\n            };\n        case \"DISMISS_TOAST\":\n            {\n                const { toastId } = action;\n                if (toastId) {\n                    addToRemoveQueue(toastId);\n                } else {\n                    state.toasts.forEach((toast)=>{\n                        addToRemoveQueue(toast.id);\n                    });\n                }\n                return {\n                    ...state,\n                    toasts: state.toasts.map((t)=>t.id === toastId || toastId === undefined ? {\n                            ...t,\n                            open: false\n                        } : t)\n                };\n            }\n        case \"REMOVE_TOAST\":\n            if (action.toastId === undefined) {\n                return {\n                    ...state,\n                    toasts: []\n                };\n            }\n            return {\n                ...state,\n                toasts: state.toasts.filter((t)=>t.id !== action.toastId)\n            };\n    }\n};\nconst listeners = [];\nlet memoryState = {\n    toasts: []\n};\nfunction dispatch(action) {\n    memoryState = reducer(memoryState, action);\n    listeners.forEach((listener)=>{\n        listener(memoryState);\n    });\n}\nfunction toast({ ...props }) {\n    const id = genId();\n    const update = (props)=>dispatch({\n            type: \"UPDATE_TOAST\",\n            toast: {\n                ...props,\n                id\n            }\n        });\n    const dismiss = ()=>dispatch({\n            type: \"DISMISS_TOAST\",\n            toastId: id\n        });\n    dispatch({\n        type: \"ADD_TOAST\",\n        toast: {\n            ...props,\n            id,\n            open: true,\n            onOpenChange: (open)=>{\n                if (!open) dismiss();\n            }\n        }\n    });\n    return {\n        id: id,\n        dismiss,\n        update\n    };\n}\nfunction useToast() {\n    const [state, setState] = react__WEBPACK_IMPORTED_MODULE_0__.useState(memoryState);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        listeners.push(setState);\n        return ()=>{\n            const index = listeners.indexOf(setState);\n            if (index > -1) {\n                listeners.splice(index, 1);\n            }\n        };\n    }, [\n        state\n    ]);\n    return {\n        ...state,\n        toast,\n        dismiss: (toastId)=>dispatch({\n                type: \"DISMISS_TOAST\",\n                toastId\n            })\n    };\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/use-toast.ts\n");

/***/ }),

/***/ "(ssr)/./lib/socket.tsx":
/*!************************!*\
  !*** ./lib/socket.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SocketProvider: () => (/* binding */ SocketProvider),\n/* harmony export */   useSocket: () => (/* binding */ useSocket)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var socket_io_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! socket.io-client */ \"(ssr)/./node_modules/socket.io-client/build/esm-debug/index.js\");\n/* harmony import */ var _store__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./store */ \"(ssr)/./lib/store.ts\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(ssr)/./components/ui/use-toast.ts\");\n/* __next_internal_client_entry_do_not_use__ useSocket,SocketProvider auto */ \n\n\n\n\nconst SocketContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    socket: null,\n    isConnected: false,\n    joinScraperRoom: ()=>{},\n    leaveScraperRoom: ()=>{}\n});\nconst useSocket = ()=>(0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(SocketContext);\nfunction SocketProvider({ children, url }) {\n    const [socket, setSocket] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { updateRun, addRun } = (0,_store__WEBPACK_IMPORTED_MODULE_3__.useScraperStore)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Initialize socket connection\n        const socketInstance = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_2__.io)(url, {\n            transports: [\n                \"websocket\",\n                \"polling\"\n            ],\n            timeout: 20000,\n            forceNew: true\n        });\n        // Connection event handlers\n        socketInstance.on(\"connect\", ()=>{\n            console.log(\"Socket connected:\", socketInstance.id);\n            setIsConnected(true);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_4__.toast)({\n                title: \"Conectado\",\n                description: \"Conexi\\xf3n en tiempo real establecida\",\n                duration: 3000\n            });\n        });\n        socketInstance.on(\"disconnect\", (reason)=>{\n            console.log(\"Socket disconnected:\", reason);\n            setIsConnected(false);\n            if (reason === \"io server disconnect\") {\n                // Server disconnected, try to reconnect\n                socketInstance.connect();\n            }\n        });\n        socketInstance.on(\"connect_error\", (error)=>{\n            console.error(\"Socket connection error:\", error);\n            setIsConnected(false);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_4__.toast)({\n                title: \"Error de conexi\\xf3n\",\n                description: \"No se pudo establecer conexi\\xf3n en tiempo real\",\n                variant: \"destructive\",\n                duration: 5000\n            });\n        });\n        // Scraper event handlers\n        socketInstance.on(\"scraper-progress\", (data)=>{\n            console.log(\"Scraper progress:\", data);\n            updateRun(data.runId, {\n                progress: data.progress\n            });\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_4__.toast)({\n                title: `${data.supermarket} - Progreso`,\n                description: `${data.progress}% completado`,\n                duration: 2000\n            });\n        });\n        socketInstance.on(\"scraper-completed\", (data)=>{\n            console.log(\"Scraper completed:\", data);\n            if (data.status === \"completed\") {\n                (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_4__.toast)({\n                    title: `${data.supermarket} - Completado`,\n                    description: `Se procesaron ${data.productsScraped || 0} productos`,\n                    duration: 5000\n                });\n            } else {\n                (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_4__.toast)({\n                    title: `${data.supermarket} - Error`,\n                    description: data.errorMessage || \"El scraper fall\\xf3\",\n                    variant: \"destructive\",\n                    duration: 5000\n                });\n            }\n        });\n        socketInstance.on(\"scraper-started\", (data)=>{\n            console.log(\"Scraper started:\", data);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_4__.toast)({\n                title: `${data.supermarket} - Iniciado`,\n                description: `Procesando ${data.totalUrls} páginas`,\n                duration: 3000\n            });\n        });\n        // Error handlers\n        socketInstance.on(\"error\", (error)=>{\n            console.error(\"Socket error:\", error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_4__.toast)({\n                title: \"Error\",\n                description: \"Error en la conexi\\xf3n en tiempo real\",\n                variant: \"destructive\",\n                duration: 3000\n            });\n        });\n        setSocket(socketInstance);\n        // Cleanup on unmount\n        return ()=>{\n            console.log(\"Cleaning up socket connection\");\n            socketInstance.disconnect();\n        };\n    }, [\n        url,\n        updateRun,\n        addRun\n    ]);\n    const joinScraperRoom = (supermarket)=>{\n        if (socket && isConnected) {\n            console.log(`Joining scraper room: ${supermarket}`);\n            socket.emit(\"join-scraper-room\", supermarket);\n        }\n    };\n    const leaveScraperRoom = (supermarket)=>{\n        if (socket && isConnected) {\n            console.log(`Leaving scraper room: ${supermarket}`);\n            socket.emit(\"leave-scraper-room\", supermarket);\n        }\n    };\n    const value = {\n        socket,\n        isConnected,\n        joinScraperRoom,\n        leaveScraperRoom\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SocketContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Volumes/2tb/github/super-scraper/frontend/lib/socket.tsx\",\n        lineNumber: 169,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvc29ja2V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBRWlGO0FBQ3BDO0FBQ0o7QUFDUTtBQVNqRCxNQUFNTyw4QkFBZ0JQLG9EQUFhQSxDQUFvQjtJQUNyRFEsUUFBUTtJQUNSQyxhQUFhO0lBQ2JDLGlCQUFpQixLQUFPO0lBQ3hCQyxrQkFBa0IsS0FBTztBQUMzQjtBQUVPLE1BQU1DLFlBQVksSUFBTVgsaURBQVVBLENBQUNNLGVBQWM7QUFPakQsU0FBU00sZUFBZSxFQUFFQyxRQUFRLEVBQUVDLEdBQUcsRUFBdUI7SUFDbkUsTUFBTSxDQUFDUCxRQUFRUSxVQUFVLEdBQUdiLCtDQUFRQSxDQUFnQjtJQUNwRCxNQUFNLENBQUNNLGFBQWFRLGVBQWUsR0FBR2QsK0NBQVFBLENBQUM7SUFDL0MsTUFBTSxFQUFFZSxTQUFTLEVBQUVDLE1BQU0sRUFBRSxHQUFHZCx1REFBZUE7SUFFN0NILGdEQUFTQSxDQUFDO1FBQ1IsK0JBQStCO1FBQy9CLE1BQU1rQixpQkFBaUJoQixvREFBRUEsQ0FBQ1csS0FBSztZQUM3Qk0sWUFBWTtnQkFBQztnQkFBYTthQUFVO1lBQ3BDQyxTQUFTO1lBQ1RDLFVBQVU7UUFDWjtRQUVBLDRCQUE0QjtRQUM1QkgsZUFBZUksRUFBRSxDQUFDLFdBQVc7WUFDM0JDLFFBQVFDLEdBQUcsQ0FBQyxxQkFBcUJOLGVBQWVPLEVBQUU7WUFDbERWLGVBQWU7WUFDZlgsK0RBQUtBLENBQUM7Z0JBQ0pzQixPQUFPO2dCQUNQQyxhQUFhO2dCQUNiQyxVQUFVO1lBQ1o7UUFDRjtRQUVBVixlQUFlSSxFQUFFLENBQUMsY0FBYyxDQUFDTztZQUMvQk4sUUFBUUMsR0FBRyxDQUFDLHdCQUF3Qks7WUFDcENkLGVBQWU7WUFFZixJQUFJYyxXQUFXLHdCQUF3QjtnQkFDckMsd0NBQXdDO2dCQUN4Q1gsZUFBZVksT0FBTztZQUN4QjtRQUNGO1FBRUFaLGVBQWVJLEVBQUUsQ0FBQyxpQkFBaUIsQ0FBQ1M7WUFDbENSLFFBQVFRLEtBQUssQ0FBQyw0QkFBNEJBO1lBQzFDaEIsZUFBZTtZQUNmWCwrREFBS0EsQ0FBQztnQkFDSnNCLE9BQU87Z0JBQ1BDLGFBQWE7Z0JBQ2JLLFNBQVM7Z0JBQ1RKLFVBQVU7WUFDWjtRQUNGO1FBRUEseUJBQXlCO1FBQ3pCVixlQUFlSSxFQUFFLENBQUMsb0JBQW9CLENBQUNXO1lBS3JDVixRQUFRQyxHQUFHLENBQUMscUJBQXFCUztZQUNqQ2pCLFVBQVVpQixLQUFLQyxLQUFLLEVBQUU7Z0JBQUVDLFVBQVVGLEtBQUtFLFFBQVE7WUFBQztZQUVoRC9CLCtEQUFLQSxDQUFDO2dCQUNKc0IsT0FBTyxDQUFDLEVBQUVPLEtBQUtHLFdBQVcsQ0FBQyxXQUFXLENBQUM7Z0JBQ3ZDVCxhQUFhLENBQUMsRUFBRU0sS0FBS0UsUUFBUSxDQUFDLFlBQVksQ0FBQztnQkFDM0NQLFVBQVU7WUFDWjtRQUNGO1FBRUFWLGVBQWVJLEVBQUUsQ0FBQyxxQkFBcUIsQ0FBQ1c7WUFNdENWLFFBQVFDLEdBQUcsQ0FBQyxzQkFBc0JTO1lBRWxDLElBQUlBLEtBQUtJLE1BQU0sS0FBSyxhQUFhO2dCQUMvQmpDLCtEQUFLQSxDQUFDO29CQUNKc0IsT0FBTyxDQUFDLEVBQUVPLEtBQUtHLFdBQVcsQ0FBQyxhQUFhLENBQUM7b0JBQ3pDVCxhQUFhLENBQUMsY0FBYyxFQUFFTSxLQUFLSyxlQUFlLElBQUksRUFBRSxVQUFVLENBQUM7b0JBQ25FVixVQUFVO2dCQUNaO1lBQ0YsT0FBTztnQkFDTHhCLCtEQUFLQSxDQUFDO29CQUNKc0IsT0FBTyxDQUFDLEVBQUVPLEtBQUtHLFdBQVcsQ0FBQyxRQUFRLENBQUM7b0JBQ3BDVCxhQUFhTSxLQUFLTSxZQUFZLElBQUk7b0JBQ2xDUCxTQUFTO29CQUNUSixVQUFVO2dCQUNaO1lBQ0Y7UUFDRjtRQUVBVixlQUFlSSxFQUFFLENBQUMsbUJBQW1CLENBQUNXO1lBS3BDVixRQUFRQyxHQUFHLENBQUMsb0JBQW9CUztZQUNoQzdCLCtEQUFLQSxDQUFDO2dCQUNKc0IsT0FBTyxDQUFDLEVBQUVPLEtBQUtHLFdBQVcsQ0FBQyxXQUFXLENBQUM7Z0JBQ3ZDVCxhQUFhLENBQUMsV0FBVyxFQUFFTSxLQUFLTyxTQUFTLENBQUMsUUFBUSxDQUFDO2dCQUNuRFosVUFBVTtZQUNaO1FBQ0Y7UUFFQSxpQkFBaUI7UUFDakJWLGVBQWVJLEVBQUUsQ0FBQyxTQUFTLENBQUNTO1lBQzFCUixRQUFRUSxLQUFLLENBQUMsaUJBQWlCQTtZQUMvQjNCLCtEQUFLQSxDQUFDO2dCQUNKc0IsT0FBTztnQkFDUEMsYUFBYTtnQkFDYkssU0FBUztnQkFDVEosVUFBVTtZQUNaO1FBQ0Y7UUFFQWQsVUFBVUk7UUFFVixxQkFBcUI7UUFDckIsT0FBTztZQUNMSyxRQUFRQyxHQUFHLENBQUM7WUFDWk4sZUFBZXVCLFVBQVU7UUFDM0I7SUFDRixHQUFHO1FBQUM1QjtRQUFLRztRQUFXQztLQUFPO0lBRTNCLE1BQU1ULGtCQUFrQixDQUFDNEI7UUFDdkIsSUFBSTlCLFVBQVVDLGFBQWE7WUFDekJnQixRQUFRQyxHQUFHLENBQUMsQ0FBQyxzQkFBc0IsRUFBRVksWUFBWSxDQUFDO1lBQ2xEOUIsT0FBT29DLElBQUksQ0FBQyxxQkFBcUJOO1FBQ25DO0lBQ0Y7SUFFQSxNQUFNM0IsbUJBQW1CLENBQUMyQjtRQUN4QixJQUFJOUIsVUFBVUMsYUFBYTtZQUN6QmdCLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLHNCQUFzQixFQUFFWSxZQUFZLENBQUM7WUFDbEQ5QixPQUFPb0MsSUFBSSxDQUFDLHNCQUFzQk47UUFDcEM7SUFDRjtJQUVBLE1BQU1PLFFBQVE7UUFDWnJDO1FBQ0FDO1FBQ0FDO1FBQ0FDO0lBQ0Y7SUFFQSxxQkFDRSw4REFBQ0osY0FBY3VDLFFBQVE7UUFBQ0QsT0FBT0E7a0JBQzVCL0I7Ozs7OztBQUdQIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3VwZXJtYXJrZXQtZnJvbnRlbmQvLi9saWIvc29ja2V0LnRzeD9jODQzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgeyBjcmVhdGVDb250ZXh0LCB1c2VDb250ZXh0LCB1c2VFZmZlY3QsIHVzZVN0YXRlLCBSZWFjdE5vZGUgfSBmcm9tICdyZWFjdCdcbmltcG9ydCB7IGlvLCBTb2NrZXQgfSBmcm9tICdzb2NrZXQuaW8tY2xpZW50J1xuaW1wb3J0IHsgdXNlU2NyYXBlclN0b3JlIH0gZnJvbSAnLi9zdG9yZSdcbmltcG9ydCB7IHRvYXN0IH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL3VzZS10b2FzdCdcblxuaW50ZXJmYWNlIFNvY2tldENvbnRleHRUeXBlIHtcbiAgc29ja2V0OiBTb2NrZXQgfCBudWxsXG4gIGlzQ29ubmVjdGVkOiBib29sZWFuXG4gIGpvaW5TY3JhcGVyUm9vbTogKHN1cGVybWFya2V0OiBzdHJpbmcpID0+IHZvaWRcbiAgbGVhdmVTY3JhcGVyUm9vbTogKHN1cGVybWFya2V0OiBzdHJpbmcpID0+IHZvaWRcbn1cblxuY29uc3QgU29ja2V0Q29udGV4dCA9IGNyZWF0ZUNvbnRleHQ8U29ja2V0Q29udGV4dFR5cGU+KHtcbiAgc29ja2V0OiBudWxsLFxuICBpc0Nvbm5lY3RlZDogZmFsc2UsXG4gIGpvaW5TY3JhcGVyUm9vbTogKCkgPT4ge30sXG4gIGxlYXZlU2NyYXBlclJvb206ICgpID0+IHt9XG59KVxuXG5leHBvcnQgY29uc3QgdXNlU29ja2V0ID0gKCkgPT4gdXNlQ29udGV4dChTb2NrZXRDb250ZXh0KVxuXG5pbnRlcmZhY2UgU29ja2V0UHJvdmlkZXJQcm9wcyB7XG4gIGNoaWxkcmVuOiBSZWFjdE5vZGVcbiAgdXJsOiBzdHJpbmdcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIFNvY2tldFByb3ZpZGVyKHsgY2hpbGRyZW4sIHVybCB9OiBTb2NrZXRQcm92aWRlclByb3BzKSB7XG4gIGNvbnN0IFtzb2NrZXQsIHNldFNvY2tldF0gPSB1c2VTdGF0ZTxTb2NrZXQgfCBudWxsPihudWxsKVxuICBjb25zdCBbaXNDb25uZWN0ZWQsIHNldElzQ29ubmVjdGVkXSA9IHVzZVN0YXRlKGZhbHNlKVxuICBjb25zdCB7IHVwZGF0ZVJ1biwgYWRkUnVuIH0gPSB1c2VTY3JhcGVyU3RvcmUoKVxuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgLy8gSW5pdGlhbGl6ZSBzb2NrZXQgY29ubmVjdGlvblxuICAgIGNvbnN0IHNvY2tldEluc3RhbmNlID0gaW8odXJsLCB7XG4gICAgICB0cmFuc3BvcnRzOiBbJ3dlYnNvY2tldCcsICdwb2xsaW5nJ10sXG4gICAgICB0aW1lb3V0OiAyMDAwMCxcbiAgICAgIGZvcmNlTmV3OiB0cnVlXG4gICAgfSlcblxuICAgIC8vIENvbm5lY3Rpb24gZXZlbnQgaGFuZGxlcnNcbiAgICBzb2NrZXRJbnN0YW5jZS5vbignY29ubmVjdCcsICgpID0+IHtcbiAgICAgIGNvbnNvbGUubG9nKCdTb2NrZXQgY29ubmVjdGVkOicsIHNvY2tldEluc3RhbmNlLmlkKVxuICAgICAgc2V0SXNDb25uZWN0ZWQodHJ1ZSlcbiAgICAgIHRvYXN0KHtcbiAgICAgICAgdGl0bGU6ICdDb25lY3RhZG8nLFxuICAgICAgICBkZXNjcmlwdGlvbjogJ0NvbmV4acOzbiBlbiB0aWVtcG8gcmVhbCBlc3RhYmxlY2lkYScsXG4gICAgICAgIGR1cmF0aW9uOiAzMDAwXG4gICAgICB9KVxuICAgIH0pXG5cbiAgICBzb2NrZXRJbnN0YW5jZS5vbignZGlzY29ubmVjdCcsIChyZWFzb24pID0+IHtcbiAgICAgIGNvbnNvbGUubG9nKCdTb2NrZXQgZGlzY29ubmVjdGVkOicsIHJlYXNvbilcbiAgICAgIHNldElzQ29ubmVjdGVkKGZhbHNlKVxuICAgICAgXG4gICAgICBpZiAocmVhc29uID09PSAnaW8gc2VydmVyIGRpc2Nvbm5lY3QnKSB7XG4gICAgICAgIC8vIFNlcnZlciBkaXNjb25uZWN0ZWQsIHRyeSB0byByZWNvbm5lY3RcbiAgICAgICAgc29ja2V0SW5zdGFuY2UuY29ubmVjdCgpXG4gICAgICB9XG4gICAgfSlcblxuICAgIHNvY2tldEluc3RhbmNlLm9uKCdjb25uZWN0X2Vycm9yJywgKGVycm9yKSA9PiB7XG4gICAgICBjb25zb2xlLmVycm9yKCdTb2NrZXQgY29ubmVjdGlvbiBlcnJvcjonLCBlcnJvcilcbiAgICAgIHNldElzQ29ubmVjdGVkKGZhbHNlKVxuICAgICAgdG9hc3Qoe1xuICAgICAgICB0aXRsZTogJ0Vycm9yIGRlIGNvbmV4acOzbicsXG4gICAgICAgIGRlc2NyaXB0aW9uOiAnTm8gc2UgcHVkbyBlc3RhYmxlY2VyIGNvbmV4acOzbiBlbiB0aWVtcG8gcmVhbCcsXG4gICAgICAgIHZhcmlhbnQ6ICdkZXN0cnVjdGl2ZScsXG4gICAgICAgIGR1cmF0aW9uOiA1MDAwXG4gICAgICB9KVxuICAgIH0pXG5cbiAgICAvLyBTY3JhcGVyIGV2ZW50IGhhbmRsZXJzXG4gICAgc29ja2V0SW5zdGFuY2Uub24oJ3NjcmFwZXItcHJvZ3Jlc3MnLCAoZGF0YToge1xuICAgICAgc3VwZXJtYXJrZXQ6IHN0cmluZ1xuICAgICAgcHJvZ3Jlc3M6IG51bWJlclxuICAgICAgcnVuSWQ6IHN0cmluZ1xuICAgIH0pID0+IHtcbiAgICAgIGNvbnNvbGUubG9nKCdTY3JhcGVyIHByb2dyZXNzOicsIGRhdGEpXG4gICAgICB1cGRhdGVSdW4oZGF0YS5ydW5JZCwgeyBwcm9ncmVzczogZGF0YS5wcm9ncmVzcyB9KVxuICAgICAgXG4gICAgICB0b2FzdCh7XG4gICAgICAgIHRpdGxlOiBgJHtkYXRhLnN1cGVybWFya2V0fSAtIFByb2dyZXNvYCxcbiAgICAgICAgZGVzY3JpcHRpb246IGAke2RhdGEucHJvZ3Jlc3N9JSBjb21wbGV0YWRvYCxcbiAgICAgICAgZHVyYXRpb246IDIwMDBcbiAgICAgIH0pXG4gICAgfSlcblxuICAgIHNvY2tldEluc3RhbmNlLm9uKCdzY3JhcGVyLWNvbXBsZXRlZCcsIChkYXRhOiB7XG4gICAgICBzdXBlcm1hcmtldDogc3RyaW5nXG4gICAgICBzdGF0dXM6ICdjb21wbGV0ZWQnIHwgJ2ZhaWxlZCdcbiAgICAgIHByb2R1Y3RzU2NyYXBlZD86IG51bWJlclxuICAgICAgZXJyb3JNZXNzYWdlPzogc3RyaW5nXG4gICAgfSkgPT4ge1xuICAgICAgY29uc29sZS5sb2coJ1NjcmFwZXIgY29tcGxldGVkOicsIGRhdGEpXG4gICAgICBcbiAgICAgIGlmIChkYXRhLnN0YXR1cyA9PT0gJ2NvbXBsZXRlZCcpIHtcbiAgICAgICAgdG9hc3Qoe1xuICAgICAgICAgIHRpdGxlOiBgJHtkYXRhLnN1cGVybWFya2V0fSAtIENvbXBsZXRhZG9gLFxuICAgICAgICAgIGRlc2NyaXB0aW9uOiBgU2UgcHJvY2VzYXJvbiAke2RhdGEucHJvZHVjdHNTY3JhcGVkIHx8IDB9IHByb2R1Y3Rvc2AsXG4gICAgICAgICAgZHVyYXRpb246IDUwMDBcbiAgICAgICAgfSlcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHRvYXN0KHtcbiAgICAgICAgICB0aXRsZTogYCR7ZGF0YS5zdXBlcm1hcmtldH0gLSBFcnJvcmAsXG4gICAgICAgICAgZGVzY3JpcHRpb246IGRhdGEuZXJyb3JNZXNzYWdlIHx8ICdFbCBzY3JhcGVyIGZhbGzDsycsXG4gICAgICAgICAgdmFyaWFudDogJ2Rlc3RydWN0aXZlJyxcbiAgICAgICAgICBkdXJhdGlvbjogNTAwMFxuICAgICAgICB9KVxuICAgICAgfVxuICAgIH0pXG5cbiAgICBzb2NrZXRJbnN0YW5jZS5vbignc2NyYXBlci1zdGFydGVkJywgKGRhdGE6IHtcbiAgICAgIHN1cGVybWFya2V0OiBzdHJpbmdcbiAgICAgIHJ1bklkOiBzdHJpbmdcbiAgICAgIHRvdGFsVXJsczogbnVtYmVyXG4gICAgfSkgPT4ge1xuICAgICAgY29uc29sZS5sb2coJ1NjcmFwZXIgc3RhcnRlZDonLCBkYXRhKVxuICAgICAgdG9hc3Qoe1xuICAgICAgICB0aXRsZTogYCR7ZGF0YS5zdXBlcm1hcmtldH0gLSBJbmljaWFkb2AsXG4gICAgICAgIGRlc2NyaXB0aW9uOiBgUHJvY2VzYW5kbyAke2RhdGEudG90YWxVcmxzfSBww6FnaW5hc2AsXG4gICAgICAgIGR1cmF0aW9uOiAzMDAwXG4gICAgICB9KVxuICAgIH0pXG5cbiAgICAvLyBFcnJvciBoYW5kbGVyc1xuICAgIHNvY2tldEluc3RhbmNlLm9uKCdlcnJvcicsIChlcnJvcikgPT4ge1xuICAgICAgY29uc29sZS5lcnJvcignU29ja2V0IGVycm9yOicsIGVycm9yKVxuICAgICAgdG9hc3Qoe1xuICAgICAgICB0aXRsZTogJ0Vycm9yJyxcbiAgICAgICAgZGVzY3JpcHRpb246ICdFcnJvciBlbiBsYSBjb25leGnDs24gZW4gdGllbXBvIHJlYWwnLFxuICAgICAgICB2YXJpYW50OiAnZGVzdHJ1Y3RpdmUnLFxuICAgICAgICBkdXJhdGlvbjogMzAwMFxuICAgICAgfSlcbiAgICB9KVxuXG4gICAgc2V0U29ja2V0KHNvY2tldEluc3RhbmNlKVxuXG4gICAgLy8gQ2xlYW51cCBvbiB1bm1vdW50XG4gICAgcmV0dXJuICgpID0+IHtcbiAgICAgIGNvbnNvbGUubG9nKCdDbGVhbmluZyB1cCBzb2NrZXQgY29ubmVjdGlvbicpXG4gICAgICBzb2NrZXRJbnN0YW5jZS5kaXNjb25uZWN0KClcbiAgICB9XG4gIH0sIFt1cmwsIHVwZGF0ZVJ1biwgYWRkUnVuXSlcblxuICBjb25zdCBqb2luU2NyYXBlclJvb20gPSAoc3VwZXJtYXJrZXQ6IHN0cmluZykgPT4ge1xuICAgIGlmIChzb2NrZXQgJiYgaXNDb25uZWN0ZWQpIHtcbiAgICAgIGNvbnNvbGUubG9nKGBKb2luaW5nIHNjcmFwZXIgcm9vbTogJHtzdXBlcm1hcmtldH1gKVxuICAgICAgc29ja2V0LmVtaXQoJ2pvaW4tc2NyYXBlci1yb29tJywgc3VwZXJtYXJrZXQpXG4gICAgfVxuICB9XG5cbiAgY29uc3QgbGVhdmVTY3JhcGVyUm9vbSA9IChzdXBlcm1hcmtldDogc3RyaW5nKSA9PiB7XG4gICAgaWYgKHNvY2tldCAmJiBpc0Nvbm5lY3RlZCkge1xuICAgICAgY29uc29sZS5sb2coYExlYXZpbmcgc2NyYXBlciByb29tOiAke3N1cGVybWFya2V0fWApXG4gICAgICBzb2NrZXQuZW1pdCgnbGVhdmUtc2NyYXBlci1yb29tJywgc3VwZXJtYXJrZXQpXG4gICAgfVxuICB9XG5cbiAgY29uc3QgdmFsdWUgPSB7XG4gICAgc29ja2V0LFxuICAgIGlzQ29ubmVjdGVkLFxuICAgIGpvaW5TY3JhcGVyUm9vbSxcbiAgICBsZWF2ZVNjcmFwZXJSb29tXG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxTb2NrZXRDb250ZXh0LlByb3ZpZGVyIHZhbHVlPXt2YWx1ZX0+XG4gICAgICB7Y2hpbGRyZW59XG4gICAgPC9Tb2NrZXRDb250ZXh0LlByb3ZpZGVyPlxuICApXG59XG4iXSwibmFtZXMiOlsiY3JlYXRlQ29udGV4dCIsInVzZUNvbnRleHQiLCJ1c2VFZmZlY3QiLCJ1c2VTdGF0ZSIsImlvIiwidXNlU2NyYXBlclN0b3JlIiwidG9hc3QiLCJTb2NrZXRDb250ZXh0Iiwic29ja2V0IiwiaXNDb25uZWN0ZWQiLCJqb2luU2NyYXBlclJvb20iLCJsZWF2ZVNjcmFwZXJSb29tIiwidXNlU29ja2V0IiwiU29ja2V0UHJvdmlkZXIiLCJjaGlsZHJlbiIsInVybCIsInNldFNvY2tldCIsInNldElzQ29ubmVjdGVkIiwidXBkYXRlUnVuIiwiYWRkUnVuIiwic29ja2V0SW5zdGFuY2UiLCJ0cmFuc3BvcnRzIiwidGltZW91dCIsImZvcmNlTmV3Iiwib24iLCJjb25zb2xlIiwibG9nIiwiaWQiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiZHVyYXRpb24iLCJyZWFzb24iLCJjb25uZWN0IiwiZXJyb3IiLCJ2YXJpYW50IiwiZGF0YSIsInJ1bklkIiwicHJvZ3Jlc3MiLCJzdXBlcm1hcmtldCIsInN0YXR1cyIsInByb2R1Y3RzU2NyYXBlZCIsImVycm9yTWVzc2FnZSIsInRvdGFsVXJscyIsImRpc2Nvbm5lY3QiLCJlbWl0IiwidmFsdWUiLCJQcm92aWRlciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./lib/socket.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/store.ts":
/*!**********************!*\
  !*** ./lib/store.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCartStore: () => (/* binding */ useCartStore),\n/* harmony export */   useScraperStore: () => (/* binding */ useScraperStore),\n/* harmony export */   useUIStore: () => (/* binding */ useUIStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/index.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand/middleware */ \"(ssr)/./node_modules/zustand/esm/middleware.mjs\");\n/* __next_internal_client_entry_do_not_use__ useCartStore,useScraperStore,useUIStore auto */ \n\nconst useCartStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.persist)((set, get)=>({\n        items: [],\n        isLoading: false,\n        error: null,\n        addItem: (product, quantity = 1)=>{\n            const items = get().items;\n            const existingItem = items.find((item)=>item.product.id === product.id);\n            if (existingItem) {\n                set({\n                    items: items.map((item)=>item.product.id === product.id ? {\n                            ...item,\n                            quantity: item.quantity + quantity\n                        } : item)\n                });\n            } else {\n                const newItem = {\n                    id: `cart-${Date.now()}-${Math.random()}`,\n                    product,\n                    quantity,\n                    added_at: new Date().toISOString(),\n                    totalPrice: (product.price_discount || product.price_list || 0) * quantity\n                };\n                set({\n                    items: [\n                        ...items,\n                        newItem\n                    ]\n                });\n            }\n        },\n        removeItem: (itemId)=>{\n            set({\n                items: get().items.filter((item)=>item.id !== itemId)\n            });\n        },\n        updateQuantity: (itemId, quantity)=>{\n            if (quantity <= 0) {\n                get().removeItem(itemId);\n                return;\n            }\n            set({\n                items: get().items.map((item)=>item.id === itemId ? {\n                        ...item,\n                        quantity,\n                        totalPrice: (item.currentPrice?.price_discount || item.product.price_discount || item.product.price_list || 0) * quantity\n                    } : item)\n            });\n        },\n        clearCart: ()=>{\n            set({\n                items: []\n            });\n        },\n        setItems: (items)=>{\n            set({\n                items\n            });\n        },\n        setLoading: (loading)=>{\n            set({\n                isLoading: loading\n            });\n        },\n        setError: (error)=>{\n            set({\n                error\n            });\n        },\n        getTotalItems: ()=>{\n            return get().items.reduce((total, item)=>total + item.quantity, 0);\n        },\n        getTotalPrice: ()=>{\n            return get().items.reduce((total, item)=>total + item.totalPrice, 0);\n        },\n        getTotalsBySuper: ()=>{\n            const items = get().items;\n            return items.reduce((acc, item)=>{\n                const supermarket = item.product.supermarkets?.slug || \"unknown\";\n                const name = item.product.supermarkets?.name || \"Unknown\";\n                if (!acc[supermarket]) {\n                    acc[supermarket] = {\n                        name,\n                        total: 0,\n                        itemCount: 0\n                    };\n                }\n                acc[supermarket].total += item.totalPrice;\n                acc[supermarket].itemCount += item.quantity;\n                return acc;\n            }, {});\n        }\n    }), {\n    name: \"cart-storage\",\n    storage: (0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.createJSONStorage)(()=>localStorage),\n    partialize: (state)=>({\n            items: state.items\n        })\n}));\nconst useScraperStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)((set, get)=>({\n        runs: [],\n        isLoading: false,\n        error: null,\n        setRuns: (runs)=>{\n            set({\n                runs\n            });\n        },\n        updateRun: (runId, updates)=>{\n            set({\n                runs: get().runs.map((run)=>run.id === runId ? {\n                        ...run,\n                        ...updates\n                    } : run)\n            });\n        },\n        addRun: (run)=>{\n            set({\n                runs: [\n                    run,\n                    ...get().runs\n                ]\n            });\n        },\n        setLoading: (loading)=>{\n            set({\n                isLoading: loading\n            });\n        },\n        setError: (error)=>{\n            set({\n                error\n            });\n        },\n        getActiveRuns: ()=>{\n            return get().runs.filter((run)=>run.status === \"running\" || run.isActive);\n        },\n        getRunBySuper: (supermarket)=>{\n            return get().runs.find((run)=>run.supermarkets.slug === supermarket && (run.status === \"running\" || run.isActive));\n        }\n    }));\nconst useUIStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.persist)((set, get)=>({\n        sidebarOpen: false,\n        theme: \"system\",\n        setSidebarOpen: (open)=>{\n            set({\n                sidebarOpen: open\n            });\n        },\n        toggleSidebar: ()=>{\n            set({\n                sidebarOpen: !get().sidebarOpen\n            });\n        },\n        setTheme: (theme)=>{\n            set({\n                theme\n            });\n        }\n    }), {\n    name: \"ui-storage\",\n    storage: (0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.createJSONStorage)(()=>localStorage)\n}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/store.ts\n");

/***/ }),

/***/ "(ssr)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTRDO0FBQ0o7QUFFakMsU0FBU0UsR0FBRyxHQUFHQyxNQUFvQjtJQUN4QyxPQUFPRix1REFBT0EsQ0FBQ0QsMENBQUlBLENBQUNHO0FBQ3RCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3VwZXJtYXJrZXQtZnJvbnRlbmQvLi9saWIvdXRpbHMudHM/Zjc0NSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB0eXBlIENsYXNzVmFsdWUsIGNsc3ggfSBmcm9tIFwiY2xzeFwiXG5pbXBvcnQgeyB0d01lcmdlIH0gZnJvbSBcInRhaWx3aW5kLW1lcmdlXCJcblxuZXhwb3J0IGZ1bmN0aW9uIGNuKC4uLmlucHV0czogQ2xhc3NWYWx1ZVtdKSB7XG4gIHJldHVybiB0d01lcmdlKGNsc3goaW5wdXRzKSlcbn1cbiJdLCJuYW1lcyI6WyJjbHN4IiwidHdNZXJnZSIsImNuIiwiaW5wdXRzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"f24ccb63f2e7\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdXBlcm1hcmtldC1mcm9udGVuZC8uL2FwcC9nbG9iYWxzLmNzcz83MTBhIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiZjI0Y2NiNjNmMmU3XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _providers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./providers */ \"(rsc)/./app/providers.tsx\");\n/* harmony import */ var _components_layout_Header__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/layout/Header */ \"(rsc)/./components/layout/Header.tsx\");\n/* harmony import */ var _components_layout_Footer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/layout/Footer */ \"(rsc)/./components/layout/Footer.tsx\");\n/* harmony import */ var _components_ui_toaster__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/toaster */ \"(rsc)/./components/ui/toaster.tsx\");\n\n\n\n\n\n\n\nconst metadata = {\n    title: \"SuperCompare - Compare Supermarket Prices\",\n    description: \"Compare prices between Carrefour and Coto supermarkets in Argentina. Find the best deals and save money on your groceries.\",\n    keywords: \"supermarket, prices, comparison, carrefour, coto, argentina, groceries, deals\",\n    authors: [\n        {\n            name: \"SuperCompare Team\"\n        }\n    ],\n    creator: \"SuperCompare\",\n    publisher: \"SuperCompare\",\n    formatDetection: {\n        email: false,\n        address: false,\n        telephone: false\n    },\n    metadataBase: new URL(\"http://localhost:3000\" || 0),\n    openGraph: {\n        title: \"SuperCompare - Compare Supermarket Prices\",\n        description: \"Compare prices between Carrefour and Coto supermarkets in Argentina.\",\n        url: \"/\",\n        siteName: \"SuperCompare\",\n        locale: \"es_AR\",\n        type: \"website\"\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"SuperCompare - Compare Supermarket Prices\",\n        description: \"Compare prices between Carrefour and Coto supermarkets in Argentina.\"\n    },\n    robots: {\n        index: true,\n        follow: true,\n        googleBot: {\n            index: true,\n            follow: true,\n            \"max-video-preview\": -1,\n            \"max-image-preview\": \"large\",\n            \"max-snippet\": -1\n        }\n    },\n    verification: {\n        google: process.env.GOOGLE_VERIFICATION_ID\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"es\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers__WEBPACK_IMPORTED_MODULE_2__.Providers, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"min-h-screen flex flex-col\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Header__WEBPACK_IMPORTED_MODULE_3__.Header, {}, void 0, false, {\n                                fileName: \"/Volumes/2tb/github/super-scraper/frontend/app/layout.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                                className: \"flex-1\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"/Volumes/2tb/github/super-scraper/frontend/app/layout.tsx\",\n                                lineNumber: 64,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Footer__WEBPACK_IMPORTED_MODULE_4__.Footer, {}, void 0, false, {\n                                fileName: \"/Volumes/2tb/github/super-scraper/frontend/app/layout.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Volumes/2tb/github/super-scraper/frontend/app/layout.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toaster__WEBPACK_IMPORTED_MODULE_5__.Toaster, {}, void 0, false, {\n                        fileName: \"/Volumes/2tb/github/super-scraper/frontend/app/layout.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/2tb/github/super-scraper/frontend/app/layout.tsx\",\n                lineNumber: 61,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Volumes/2tb/github/super-scraper/frontend/app/layout.tsx\",\n            lineNumber: 60,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Volumes/2tb/github/super-scraper/frontend/app/layout.tsx\",\n        lineNumber: 59,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/providers.tsx":
/*!***************************!*\
  !*** ./app/providers.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Providers: () => (/* binding */ e1),
/* harmony export */   useApi: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Volumes/2tb/github/super-scraper/frontend/app/providers.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Volumes/2tb/github/super-scraper/frontend/app/providers.tsx#useApi`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Volumes/2tb/github/super-scraper/frontend/app/providers.tsx#Providers`);


/***/ }),

/***/ "(rsc)/./components/layout/Footer.tsx":
/*!**************************************!*\
  !*** ./components/layout/Footer.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Footer: () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction Footer() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"border-t bg-background\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container flex flex-col items-center justify-between gap-4 py-10 md:h-24 md:flex-row md:py-0\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col items-center gap-4 px-8 md:flex-row md:gap-2 md:px-0\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-center text-sm leading-loose text-muted-foreground md:text-left\",\n                        children: \"SuperCompare - Compara precios entre Carrefour y Coto\"\n                    }, void 0, false, {\n                        fileName: \"/Volumes/2tb/github/super-scraper/frontend/components/layout/Footer.tsx\",\n                        lineNumber: 6,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Volumes/2tb/github/super-scraper/frontend/components/layout/Footer.tsx\",\n                    lineNumber: 5,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-muted-foreground\",\n                        children: \"\\xa9 2024 SuperCompare. Todos los derechos reservados.\"\n                    }, void 0, false, {\n                        fileName: \"/Volumes/2tb/github/super-scraper/frontend/components/layout/Footer.tsx\",\n                        lineNumber: 11,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Volumes/2tb/github/super-scraper/frontend/components/layout/Footer.tsx\",\n                    lineNumber: 10,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Volumes/2tb/github/super-scraper/frontend/components/layout/Footer.tsx\",\n            lineNumber: 4,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Volumes/2tb/github/super-scraper/frontend/components/layout/Footer.tsx\",\n        lineNumber: 3,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9jb21wb25lbnRzL2xheW91dC9Gb290ZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBTyxTQUFTQTtJQUNkLHFCQUNFLDhEQUFDQztRQUFPQyxXQUFVO2tCQUNoQiw0RUFBQ0M7WUFBSUQsV0FBVTs7OEJBQ2IsOERBQUNDO29CQUFJRCxXQUFVOzhCQUNiLDRFQUFDRTt3QkFBRUYsV0FBVTtrQ0FBdUU7Ozs7Ozs7Ozs7OzhCQUl0Riw4REFBQ0M7b0JBQUlELFdBQVU7OEJBQ2IsNEVBQUNFO3dCQUFFRixXQUFVO2tDQUFnQzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU92RCIsInNvdXJjZXMiOlsid2VicGFjazovL3N1cGVybWFya2V0LWZyb250ZW5kLy4vY29tcG9uZW50cy9sYXlvdXQvRm9vdGVyLnRzeD83ODNkIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBmdW5jdGlvbiBGb290ZXIoKSB7XG4gIHJldHVybiAoXG4gICAgPGZvb3RlciBjbGFzc05hbWU9XCJib3JkZXItdCBiZy1iYWNrZ3JvdW5kXCI+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbnRhaW5lciBmbGV4IGZsZXgtY29sIGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gZ2FwLTQgcHktMTAgbWQ6aC0yNCBtZDpmbGV4LXJvdyBtZDpweS0wXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBpdGVtcy1jZW50ZXIgZ2FwLTQgcHgtOCBtZDpmbGV4LXJvdyBtZDpnYXAtMiBtZDpweC0wXCI+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgdGV4dC1zbSBsZWFkaW5nLWxvb3NlIHRleHQtbXV0ZWQtZm9yZWdyb3VuZCBtZDp0ZXh0LWxlZnRcIj5cbiAgICAgICAgICAgIFN1cGVyQ29tcGFyZSAtIENvbXBhcmEgcHJlY2lvcyBlbnRyZSBDYXJyZWZvdXIgeSBDb3RvXG4gICAgICAgICAgPC9wPlxuICAgICAgICA8L2Rpdj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTRcIj5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPlxuICAgICAgICAgICAgwqkgMjAyNCBTdXBlckNvbXBhcmUuIFRvZG9zIGxvcyBkZXJlY2hvcyByZXNlcnZhZG9zLlxuICAgICAgICAgIDwvcD5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Zvb3Rlcj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIkZvb3RlciIsImZvb3RlciIsImNsYXNzTmFtZSIsImRpdiIsInAiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./components/layout/Footer.tsx\n");

/***/ }),

/***/ "(rsc)/./components/layout/Header.tsx":
/*!**************************************!*\
  !*** ./components/layout/Header.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Header: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Volumes/2tb/github/super-scraper/frontend/components/layout/Header.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Volumes/2tb/github/super-scraper/frontend/components/layout/Header.tsx#Header`);


/***/ }),

/***/ "(rsc)/./components/ui/toaster.tsx":
/*!***********************************!*\
  !*** ./components/ui/toaster.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Toaster: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Volumes/2tb/github/super-scraper/frontend/components/ui/toaster.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Volumes/2tb/github/super-scraper/frontend/components/ui/toaster.tsx#Toaster`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/engine.io-client","vendor-chunks/tailwind-merge","vendor-chunks/@radix-ui","vendor-chunks/socket.io-client","vendor-chunks/whatwg-url","vendor-chunks/socket.io-parser","vendor-chunks/zustand","vendor-chunks/xmlhttprequest-ssl","vendor-chunks/lucide-react","vendor-chunks/engine.io-parser","vendor-chunks/use-sync-external-store","vendor-chunks/set-cookie-parser","vendor-chunks/next-themes","vendor-chunks/webidl-conversions","vendor-chunks/@socket.io","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/class-variance-authority","vendor-chunks/jose","vendor-chunks/clsx","vendor-chunks/has-flag"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fnot-found&page=%2Fnot-found&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=%2FVolumes%2F2tb%2Fgithub%2Fsuper-scraper%2Ffrontend%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FVolumes%2F2tb%2Fgithub%2Fsuper-scraper%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();