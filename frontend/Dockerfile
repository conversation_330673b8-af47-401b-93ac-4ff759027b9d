# Frontend Dockerfile - Development Mode
FROM node:18-alpine

WORKDIR /app

# Install dependencies
RUN apk add --no-cache libc6-compat

# Copy package files
COPY package.json package-lock.json* ./
RUN npm ci

# Copy source code
COPY . .

# Set environment variables with defaults
ENV NEXT_PUBLIC_SUPABASE_URL=https://demo.supabase.co
ENV NEXT_PUBLIC_SUPABASE_ANON_KEY=demo_anon_key
ENV NEXT_PUBLIC_API_URL=http://backend:5000
ENV NEXT_PUBLIC_SITE_URL=http://localhost:3000
ENV NODE_ENV=development

EXPOSE 3000

# Run in development mode
CMD ["npm", "run", "dev"]
