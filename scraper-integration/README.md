# Scraper Integration

This directory contains scripts to integrate the existing Scrapy spiders with the backend API, providing real-time progress tracking and automated data import.

## Files

- `progress-tracker.py` - Progress tracking system for scrapers
- `run-scraper.py` - Main script to run scrapers with API integration
- `requirements.txt` - Python dependencies

## Setup

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Ensure the backend API is running on `http://localhost:5000` (or specify different URL)

3. Make sure the original scraper directories exist:
   - `/Volumes/2tb/github/super-scraper/carrefour/`
   - `/Volumes/2tb/github/super-scraper/coto/`

## Usage

### Run Carrefour Scraper
```bash
python run-scraper.py carrefour --api-url http://localhost:5000
```

### Run Coto Scraper
```bash
python run-scraper.py coto --api-url http://localhost:5000
```

### With Authentication Token
```bash
python run-scraper.py carrefour --api-url http://localhost:5000 --api-token your_token_here
```

### Verbose Logging
```bash
python run-scraper.py carrefour --verbose
```

## Features

- **Real-time Progress Tracking**: Reports progress to backend API via WebSocket
- **Automatic Data Import**: Imports scraped data to database after completion
- **Error Handling**: Graceful error handling and reporting
- **Signal Handling**: Proper cleanup on interruption
- **Logging**: Comprehensive logging of scraper operations

## Integration with Backend

The scraper integration works with the backend API endpoints:

- `POST /api/scrapers/start` - Start a scraper run
- `PUT /api/scrapers/progress` - Update progress (via WebSocket)
- `POST /api/scrapers/complete` - Mark run as completed

## Progress Calculation

Progress is calculated based on:
1. Number of products scraped vs expected total
2. Elapsed time vs estimated duration
3. Output file size and line count

## Error Handling

- Network errors are retried automatically
- Process failures are reported to the API
- Graceful shutdown on SIGINT/SIGTERM
- Cleanup of temporary files

## Monitoring

The progress tracker runs in a separate thread and:
- Monitors output file growth
- Calculates progress percentage
- Reports updates every 5 seconds
- Handles API communication errors gracefully
