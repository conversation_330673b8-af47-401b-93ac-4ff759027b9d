#!/usr/bin/env python3
"""
Progress Tracker for Scrapy Spiders
Integrates with the backend API to provide real-time progress updates
"""

import json
import time
import requests
import logging
from pathlib import Path
from typing import Optional, Dict, Any
from dataclasses import dataclass
from threading import Thread, Event
import signal
import sys

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

@dataclass
class ScraperConfig:
    name: str
    supermarket: str
    working_dir: str
    output_file: str
    expected_urls: int
    api_url: str
    api_token: Optional[str] = None

class ProgressTracker:
    """Tracks scraper progress and reports to backend API"""
    
    def __init__(self, config: ScraperConfig):
        self.config = config
        self.run_id: Optional[str] = None
        self.start_time: Optional[float] = None
        self.stop_event = Event()
        self.progress_thread: Optional[Thread] = None
        
        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        logger.info(f"Received signal {signum}, shutting down gracefully...")
        self.stop()
        sys.exit(0)
    
    def _make_api_request(self, method: str, endpoint: str, data: Optional[Dict] = None) -> Optional[Dict]:
        """Make API request to backend"""
        url = f"{self.config.api_url}/api{endpoint}"
        headers = {'Content-Type': 'application/json'}
        
        if self.config.api_token:
            headers['Authorization'] = f'Bearer {self.config.api_token}'
        
        try:
            if method.upper() == 'GET':
                response = requests.get(url, headers=headers, timeout=10)
            elif method.upper() == 'POST':
                response = requests.post(url, headers=headers, json=data, timeout=10)
            elif method.upper() == 'PUT':
                response = requests.put(url, headers=headers, json=data, timeout=10)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")
            
            response.raise_for_status()
            return response.json()
            
        except requests.exceptions.RequestException as e:
            logger.error(f"API request failed: {e}")
            return None
    
    def start_scraper_run(self) -> bool:
        """Start a new scraper run and get run ID"""
        data = {'supermarket': self.config.supermarket}
        result = self._make_api_request('POST', '/scrapers/start', data)
        
        if result and 'runId' in result:
            self.run_id = result['runId']
            self.start_time = time.time()
            logger.info(f"Started scraper run with ID: {self.run_id}")
            
            # Start progress monitoring thread
            self.progress_thread = Thread(target=self._monitor_progress, daemon=True)
            self.progress_thread.start()
            
            return True
        else:
            logger.error("Failed to start scraper run")
            return False
    
    def _monitor_progress(self):
        """Monitor scraper progress in background thread"""
        last_progress = 0
        last_product_count = 0
        
        while not self.stop_event.is_set():
            try:
                # Calculate progress based on output file
                current_progress = self._calculate_progress()
                product_count = self._count_products()
                
                # Only update if progress changed significantly
                if abs(current_progress - last_progress) >= 1 or product_count != last_product_count:
                    self._update_progress(current_progress, product_count)
                    last_progress = current_progress
                    last_product_count = product_count
                
                # Sleep for a bit before next check
                self.stop_event.wait(5)  # Check every 5 seconds
                
            except Exception as e:
                logger.error(f"Error monitoring progress: {e}")
                self.stop_event.wait(10)  # Wait longer on error
    
    def _calculate_progress(self) -> int:
        """Calculate progress percentage based on various indicators"""
        if not self.start_time:
            return 0
        
        # Method 1: Based on output file size/lines
        output_path = Path(self.config.working_dir) / self.config.output_file
        if output_path.exists():
            try:
                with open(output_path, 'r', encoding='utf-8') as f:
                    lines = sum(1 for line in f if line.strip())
                
                # Estimate progress based on products per URL
                estimated_products_per_url = 24  # VTEX/Coto typical page size
                estimated_total_products = self.config.expected_urls * estimated_products_per_url
                
                if estimated_total_products > 0:
                    progress = min(100, int((lines / estimated_total_products) * 100))
                    return progress
            except Exception as e:
                logger.warning(f"Error reading output file: {e}")
        
        # Method 2: Based on elapsed time (fallback)
        elapsed = time.time() - self.start_time
        estimated_duration = self.config.expected_urls * 2  # 2 seconds per URL estimate
        
        if estimated_duration > 0:
            time_progress = min(100, int((elapsed / estimated_duration) * 100))
            return time_progress
        
        return 0
    
    def _count_products(self) -> int:
        """Count products in output file"""
        output_path = Path(self.config.working_dir) / self.config.output_file
        if output_path.exists():
            try:
                with open(output_path, 'r', encoding='utf-8') as f:
                    return sum(1 for line in f if line.strip())
            except Exception as e:
                logger.warning(f"Error counting products: {e}")
        return 0
    
    def _update_progress(self, progress: int, product_count: int):
        """Update progress via API"""
        if not self.run_id:
            return
        
        # This would typically be handled by the backend when it receives
        # progress updates from the scraper process itself
        logger.info(f"Progress: {progress}%, Products: {product_count}")
    
    def complete_scraper_run(self, success: bool = True, error_message: Optional[str] = None):
        """Mark scraper run as completed"""
        if not self.run_id:
            return
        
        # Stop monitoring
        self.stop()
        
        # Count final products
        final_count = self._count_products()
        
        logger.info(f"Scraper run completed. Success: {success}, Products: {final_count}")
        
        if error_message:
            logger.error(f"Scraper error: {error_message}")
    
    def stop(self):
        """Stop progress monitoring"""
        if self.stop_event:
            self.stop_event.set()
        
        if self.progress_thread and self.progress_thread.is_alive():
            self.progress_thread.join(timeout=5)

def create_carrefour_tracker(api_url: str, api_token: Optional[str] = None) -> ProgressTracker:
    """Create progress tracker for Carrefour scraper"""
    config = ScraperConfig(
        name="carrefour_next",
        supermarket="carrefour",
        working_dir="/Volumes/2tb/github/super-scraper/carrefour",
        output_file="carrefour_products.jsonl",
        expected_urls=500,  # Approximate from carrefour_full_urls.txt
        api_url=api_url,
        api_token=api_token
    )
    return ProgressTracker(config)

def create_coto_tracker(api_url: str, api_token: Optional[str] = None) -> ProgressTracker:
    """Create progress tracker for Coto scraper"""
    config = ScraperConfig(
        name="coto_api",
        supermarket="coto",
        working_dir="/Volumes/2tb/github/super-scraper/coto",
        output_file="coto_products.jsonl",
        expected_urls=1207,  # From gen_cotodigital_urls.py
        api_url=api_url,
        api_token=api_token
    )
    return ProgressTracker(config)

def main():
    """Main function for testing"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Test scraper progress tracker')
    parser.add_argument('--supermarket', choices=['carrefour', 'coto'], required=True)
    parser.add_argument('--api-url', default='http://localhost:5000')
    parser.add_argument('--api-token', help='API authentication token')
    
    args = parser.parse_args()
    
    # Create tracker
    if args.supermarket == 'carrefour':
        tracker = create_carrefour_tracker(args.api_url, args.api_token)
    else:
        tracker = create_coto_tracker(args.api_url, args.api_token)
    
    # Start tracking
    if tracker.start_scraper_run():
        try:
            # Simulate scraper running
            logger.info("Simulating scraper run...")
            time.sleep(30)  # Run for 30 seconds
            tracker.complete_scraper_run(success=True)
        except KeyboardInterrupt:
            logger.info("Interrupted by user")
            tracker.complete_scraper_run(success=False, error_message="Interrupted by user")
    else:
        logger.error("Failed to start scraper run")

if __name__ == '__main__':
    main()
