#!/usr/bin/env python3
"""
Scraper Runner with API Integration
Runs the existing Scrapy spiders and integrates with the backend API
"""

import os
import sys
import json
import subprocess
import time
import signal
from pathlib import Path
from typing import Optional, Dict, Any
import argparse
import logging
from progress_tracker import create_carrefour_tracker, create_coto_tracker

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ScraperRunner:
    """Runs Scrapy spiders with API integration"""
    
    def __init__(self, supermarket: str, api_url: str, api_token: Optional[str] = None):
        self.supermarket = supermarket
        self.api_url = api_url
        self.api_token = api_token
        self.process: Optional[subprocess.Popen] = None
        self.tracker = None
        
        # Setup signal handlers
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
        # Configure scraper settings
        self.config = self._get_scraper_config()
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        logger.info(f"Received signal {signum}, shutting down...")
        self.stop()
        sys.exit(0)
    
    def _get_scraper_config(self) -> Dict[str, Any]:
        """Get configuration for the specified scraper"""
        configs = {
            'carrefour': {
                'working_dir': '/Volumes/2tb/github/super-scraper/carrefour',
                'spider_name': 'carrefour_next',
                'output_file': 'carrefour_products.jsonl',
                'command': ['scrapy', 'crawl', 'carrefour_next'],
                'expected_urls': 500
            },
            'coto': {
                'working_dir': '/Volumes/2tb/github/super-scraper/coto',
                'spider_name': 'coto_api',
                'output_file': 'coto_products.jsonl',
                'command': ['scrapy', 'crawl', 'coto_api'],
                'expected_urls': 1207
            }
        }
        
        if self.supermarket not in configs:
            raise ValueError(f"Unknown supermarket: {self.supermarket}")
        
        return configs[self.supermarket]
    
    def _setup_environment(self):
        """Setup environment for scraper execution"""
        working_dir = Path(self.config['working_dir'])
        
        if not working_dir.exists():
            raise FileNotFoundError(f"Working directory not found: {working_dir}")
        
        # Change to working directory
        os.chdir(working_dir)
        
        # Remove old output file if exists
        output_file = working_dir / self.config['output_file']
        if output_file.exists():
            logger.info(f"Removing old output file: {output_file}")
            output_file.unlink()
        
        # Ensure required files exist
        if self.supermarket == 'carrefour':
            url_files = ['carrefour_full_urls.txt', 'utils/carrefour_full_urls.txt']
            if not any((working_dir / f).exists() for f in url_files):
                raise FileNotFoundError("Carrefour URL file not found")
        
        elif self.supermarket == 'coto':
            url_file = working_dir / 'cotodigital_urls.txt'
            if not url_file.exists():
                logger.warning(f"Coto URL file not found: {url_file}")
                logger.info("Generating Coto URLs...")
                self._generate_coto_urls()
    
    def _generate_coto_urls(self):
        """Generate Coto URLs if not present"""
        gen_script = Path(self.config['working_dir']) / 'gen_cotodigital_urls.py'
        if gen_script.exists():
            try:
                subprocess.run([sys.executable, str(gen_script)], check=True)
                logger.info("Coto URLs generated successfully")
            except subprocess.CalledProcessError as e:
                logger.error(f"Failed to generate Coto URLs: {e}")
                raise
        else:
            raise FileNotFoundError(f"URL generator script not found: {gen_script}")
    
    def _create_progress_tracker(self):
        """Create progress tracker for the scraper"""
        if self.supermarket == 'carrefour':
            self.tracker = create_carrefour_tracker(self.api_url, self.api_token)
        elif self.supermarket == 'coto':
            self.tracker = create_coto_tracker(self.api_url, self.api_token)
        else:
            raise ValueError(f"Unknown supermarket: {self.supermarket}")
    
    def run(self) -> bool:
        """Run the scraper"""
        try:
            logger.info(f"Starting {self.supermarket} scraper...")
            
            # Setup environment
            self._setup_environment()
            
            # Create progress tracker
            self._create_progress_tracker()
            
            # Start scraper run in API
            if not self.tracker.start_scraper_run():
                logger.error("Failed to start scraper run in API")
                return False
            
            # Prepare command
            command = self.config['command'] + ['-o', self.config['output_file']]
            
            logger.info(f"Executing command: {' '.join(command)}")
            logger.info(f"Working directory: {os.getcwd()}")
            
            # Start scraper process
            self.process = subprocess.Popen(
                command,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True
            )
            
            # Monitor process output
            success = self._monitor_process()
            
            # Complete scraper run
            if success:
                logger.info("Scraper completed successfully")
                self.tracker.complete_scraper_run(success=True)
                
                # Import data to database
                self._import_data()
            else:
                logger.error("Scraper failed")
                self.tracker.complete_scraper_run(success=False, error_message="Scraper process failed")
            
            return success
            
        except Exception as e:
            logger.error(f"Error running scraper: {e}")
            if self.tracker:
                self.tracker.complete_scraper_run(success=False, error_message=str(e))
            return False
        finally:
            if self.tracker:
                self.tracker.stop()
    
    def _monitor_process(self) -> bool:
        """Monitor scraper process and log output"""
        if not self.process:
            return False
        
        try:
            # Read output in real-time
            while True:
                output = self.process.stdout.readline()
                if output == '' and self.process.poll() is not None:
                    break
                if output:
                    logger.info(f"[SCRAPER] {output.strip()}")
            
            # Get any remaining output
            stdout, stderr = self.process.communicate()
            
            if stdout:
                for line in stdout.strip().split('\n'):
                    if line:
                        logger.info(f"[SCRAPER] {line}")
            
            if stderr:
                for line in stderr.strip().split('\n'):
                    if line:
                        logger.error(f"[SCRAPER ERROR] {line}")
            
            # Check return code
            return_code = self.process.returncode
            logger.info(f"Scraper process finished with return code: {return_code}")
            
            return return_code == 0
            
        except Exception as e:
            logger.error(f"Error monitoring process: {e}")
            return False
    
    def _import_data(self):
        """Import scraped data to database"""
        output_file = Path(self.config['working_dir']) / self.config['output_file']
        
        if not output_file.exists():
            logger.warning(f"Output file not found: {output_file}")
            return
        
        # Count products
        try:
            with open(output_file, 'r', encoding='utf-8') as f:
                product_count = sum(1 for line in f if line.strip())
            
            logger.info(f"Scraped {product_count} products")
            
            # Here you would typically run the import script
            # For now, just log the action
            logger.info(f"Data import would be triggered for {output_file}")
            
        except Exception as e:
            logger.error(f"Error processing output file: {e}")
    
    def stop(self):
        """Stop the scraper"""
        if self.process and self.process.poll() is None:
            logger.info("Stopping scraper process...")
            self.process.terminate()
            
            # Wait for graceful shutdown
            try:
                self.process.wait(timeout=10)
            except subprocess.TimeoutExpired:
                logger.warning("Process didn't terminate gracefully, killing...")
                self.process.kill()
                self.process.wait()
        
        if self.tracker:
            self.tracker.stop()

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Run supermarket scraper with API integration')
    parser.add_argument('supermarket', choices=['carrefour', 'coto'], help='Supermarket to scrape')
    parser.add_argument('--api-url', default='http://localhost:5000', help='Backend API URL')
    parser.add_argument('--api-token', help='API authentication token')
    parser.add_argument('--verbose', '-v', action='store_true', help='Verbose logging')
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Create and run scraper
    runner = ScraperRunner(args.supermarket, args.api_url, args.api_token)
    
    try:
        success = runner.run()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("Interrupted by user")
        runner.stop()
        sys.exit(1)
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        runner.stop()
        sys.exit(1)

if __name__ == '__main__':
    main()
