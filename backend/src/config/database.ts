import { createClient } from '@supabase/supabase-js';
import { logger } from '../utils/logger';

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  throw new Error('Missing Supabase environment variables');
}

export const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// Test database connection
export async function testConnection() {
  try {
    const { data, error } = await supabase
      .from('supermarkets')
      .select('count')
      .limit(1);
    
    if (error) {
      logger.error('Database connection failed:', error);
      return false;
    }
    
    logger.info('Database connection successful');
    return true;
  } catch (error) {
    logger.error('Database connection error:', error);
    return false;
  }
}

// Database types
export interface Supermarket {
  id: string;
  name: string;
  slug: string;
  base_url?: string;
  logo_url?: string;
  created_at: string;
  updated_at: string;
}

export interface Product {
  id: string;
  external_id?: string;
  supermarket_id: string;
  name: string;
  normalized_name?: string;
  category?: string;
  image_url?: string;
  product_url?: string;
  created_at: string;
  updated_at: string;
}

export interface ProductPrice {
  id: string;
  product_id: string;
  price_list?: number;
  price_discount?: number;
  price_without_tax?: number;
  scraped_at: string;
  scraper_run_id?: string;
}

export interface ScraperRun {
  id: string;
  supermarket_id: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  progress: number;
  total_urls: number;
  products_scraped: number;
  started_at: string;
  completed_at?: string;
  error_message?: string;
}

export interface CartItem {
  id: string;
  user_id: string;
  product_id: string;
  quantity: number;
  added_at: string;
}
