import { Router } from 'express';
import { z } from 'zod';
import { supabase } from '../config/database';
import { logger } from '../utils/logger';
import { adminAuthMiddleware } from '../middleware/auth';

const router = Router();

// Apply admin authentication middleware to all admin routes
router.use(adminAuthMiddleware);

// Validation schemas
const confirmSchema = z.object({
  confirm: z.literal(true)
});

// GET /api/admin/stats - Get database statistics
router.get('/stats', async (req, res) => {
  try {
    // Get product counts by supermarket
    const { data: productCounts, error: productError } = await supabase
      .from('products')
      .select(`
        supermarket_id,
        supermarkets (name, slug)
      `);

    if (productError) {
      logger.error('Error fetching product stats:', productError);
      return res.status(500).json({ error: 'Failed to fetch product statistics' });
    }

    // Count products by supermarket
    const productStats = productCounts.reduce((acc, product) => {
      const supermarket = product.supermarkets.slug;
      acc[supermarket] = (acc[supermarket] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // Get total price records
    const { count: priceCount, error: priceError } = await supabase
      .from('product_prices')
      .select('*', { count: 'exact', head: true });

    if (priceError) {
      logger.error('Error fetching price stats:', priceError);
      return res.status(500).json({ error: 'Failed to fetch price statistics' });
    }

    // Get scraper run statistics
    const { data: scraperStats, error: scraperError } = await supabase
      .from('scraper_runs')
      .select(`
        status,
        supermarkets (name, slug),
        started_at,
        completed_at,
        products_scraped
      `)
      .order('started_at', { ascending: false })
      .limit(10);

    if (scraperError) {
      logger.error('Error fetching scraper stats:', scraperError);
      return res.status(500).json({ error: 'Failed to fetch scraper statistics' });
    }

    // Get latest scrape dates by supermarket
    const latestScrapes = await Promise.all(
      Object.keys(productStats).map(async (supermarket) => {
        const { data: latestRun, error } = await supabase
          .from('scraper_runs')
          .select('completed_at, products_scraped')
          .eq('supermarkets.slug', supermarket)
          .eq('status', 'completed')
          .order('completed_at', { ascending: false })
          .limit(1)
          .single();

        return {
          supermarket,
          lastScrape: latestRun?.completed_at || null,
          lastProductCount: latestRun?.products_scraped || 0
        };
      })
    );

    res.json({
      productStats,
      totalProducts: Object.values(productStats).reduce((sum, count) => sum + count, 0),
      totalPriceRecords: priceCount || 0,
      recentScraperRuns: scraperStats,
      latestScrapes
    });

  } catch (error) {
    logger.error('Error fetching admin stats:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// GET /api/admin/health - System health check
router.get('/health', async (req, res) => {
  try {
    // Test database connection
    const { data: dbTest, error: dbError } = await supabase
      .from('supermarkets')
      .select('count')
      .limit(1);

    const dbStatus = dbError ? 'error' : 'healthy';

    // Check for stale data (products not updated in 7 days)
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

    const { data: staleProducts, error: staleError } = await supabase
      .from('products')
      .select('count')
      .lt('updated_at', sevenDaysAgo.toISOString());

    const staleCount = staleError ? 0 : staleProducts?.length || 0;

    // Check for failed scraper runs in last 24 hours
    const twentyFourHoursAgo = new Date();
    twentyFourHoursAgo.setHours(twentyFourHoursAgo.getHours() - 24);

    const { data: failedRuns, error: failedError } = await supabase
      .from('scraper_runs')
      .select('count')
      .eq('status', 'failed')
      .gte('started_at', twentyFourHoursAgo.toISOString());

    const recentFailures = failedError ? 0 : failedRuns?.length || 0;

    res.json({
      database: {
        status: dbStatus,
        error: dbError?.message || null
      },
      dataFreshness: {
        staleProductCount: staleCount,
        threshold: '7 days'
      },
      scrapers: {
        recentFailures,
        threshold: '24 hours'
      },
      overall: dbStatus === 'healthy' && staleCount < 1000 && recentFailures === 0 ? 'healthy' : 'warning'
    });

  } catch (error) {
    logger.error('Error checking system health:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// DELETE /api/admin/data/all - Delete all data (dangerous!)
router.delete('/data/all', async (req, res) => {
  try {
    const { confirm } = confirmSchema.parse(req.body);

    if (!confirm) {
      return res.status(400).json({ error: 'Confirmation required' });
    }

    logger.warn('Admin initiated complete data deletion');

    // Delete in correct order due to foreign key constraints
    const tables = ['cart_items', 'product_prices', 'product_matches', 'products', 'scraper_runs'];
    
    for (const table of tables) {
      const { error } = await supabase
        .from(table)
        .delete()
        .neq('id', '00000000-0000-0000-0000-000000000000'); // Delete all records

      if (error) {
        logger.error(`Error deleting from ${table}:`, error);
        return res.status(500).json({ error: `Failed to delete from ${table}` });
      }
    }

    logger.info('Complete data deletion completed successfully');

    res.json({
      message: 'All data deleted successfully',
      deletedTables: tables,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({ error: 'Invalid parameters', details: error.errors });
    }
    logger.error('Error deleting all data:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// DELETE /api/admin/data/products - Delete all products and related data
router.delete('/data/products', async (req, res) => {
  try {
    const { confirm } = confirmSchema.parse(req.body);

    if (!confirm) {
      return res.status(400).json({ error: 'Confirmation required' });
    }

    logger.warn('Admin initiated product data deletion');

    // Delete products and related data (cascading deletes will handle related records)
    const { error: cartError } = await supabase
      .from('cart_items')
      .delete()
      .neq('id', '00000000-0000-0000-0000-000000000000');

    const { error: priceError } = await supabase
      .from('product_prices')
      .delete()
      .neq('id', '00000000-0000-0000-0000-000000000000');

    const { error: matchError } = await supabase
      .from('product_matches')
      .delete()
      .neq('id', '00000000-0000-0000-0000-000000000000');

    const { error: productError } = await supabase
      .from('products')
      .delete()
      .neq('id', '00000000-0000-0000-0000-000000000000');

    if (cartError || priceError || matchError || productError) {
      const errors = [cartError, priceError, matchError, productError].filter(Boolean);
      logger.error('Error deleting product data:', errors);
      return res.status(500).json({ error: 'Failed to delete product data' });
    }

    logger.info('Product data deletion completed successfully');

    res.json({
      message: 'All product data deleted successfully',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({ error: 'Invalid parameters', details: error.errors });
    }
    logger.error('Error deleting product data:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// DELETE /api/admin/data/prices - Delete all price history
router.delete('/data/prices', async (req, res) => {
  try {
    const { confirm } = confirmSchema.parse(req.body);

    if (!confirm) {
      return res.status(400).json({ error: 'Confirmation required' });
    }

    logger.warn('Admin initiated price history deletion');

    const { error } = await supabase
      .from('product_prices')
      .delete()
      .neq('id', '00000000-0000-0000-0000-000000000000');

    if (error) {
      logger.error('Error deleting price history:', error);
      return res.status(500).json({ error: 'Failed to delete price history' });
    }

    logger.info('Price history deletion completed successfully');

    res.json({
      message: 'All price history deleted successfully',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({ error: 'Invalid parameters', details: error.errors });
    }
    logger.error('Error deleting price history:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

export default router;
