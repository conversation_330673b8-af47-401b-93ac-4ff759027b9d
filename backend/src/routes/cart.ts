import { Router } from 'express';
import { z } from 'zod';
import { supabase } from '../config/database';
import { logger } from '../utils/logger';
import { authMiddleware } from '../middleware/auth';

const router = Router();

// Apply authentication middleware to all cart routes
router.use(authMiddleware);

// Validation schemas
const addToCartSchema = z.object({
  productId: z.string().uuid(),
  quantity: z.number().min(1).max(100).default(1)
});

const updateCartSchema = z.object({
  quantity: z.number().min(1).max(100)
});

const cartItemIdSchema = z.object({
  id: z.string().uuid()
});

// GET /api/cart - Get user's cart with price comparison
router.get('/', async (req, res) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({ error: 'User not authenticated' });
    }

    // Get cart items with product details and latest prices
    const { data: cartItems, error } = await supabase
      .from('cart_items')
      .select(`
        id,
        quantity,
        added_at,
        products (
          id,
          name,
          category,
          image_url,
          supermarkets (name, slug)
        )
      `)
      .eq('user_id', userId)
      .order('added_at', { ascending: false });

    if (error) {
      logger.error('Error fetching cart:', error);
      return res.status(500).json({ error: 'Failed to fetch cart' });
    }

    // Get latest prices for each product
    const cartWithPrices = await Promise.all(
      cartItems.map(async (item) => {
        const { data: latestPrice, error: priceError } = await supabase
          .from('latest_product_prices')
          .select('price_list, price_discount, scraped_at')
          .eq('product_id', item.products.id)
          .single();

        if (priceError) {
          logger.warn(`No price found for product ${item.products.id}`);
        }

        // Find comparable products in other supermarkets
        const { data: comparableProducts, error: compareError } = await supabase
          .from('latest_product_prices')
          .select('*')
          .ilike('name', `%${item.products.name}%`)
          .neq('supermarket_slug', item.products.supermarkets.slug)
          .limit(3);

        return {
          ...item,
          currentPrice: latestPrice,
          comparableProducts: comparableProducts || [],
          totalPrice: latestPrice ? (latestPrice.price_discount || latestPrice.price_list) * item.quantity : 0
        };
      })
    );

    // Calculate cart totals by supermarket
    const totals = cartWithPrices.reduce((acc, item) => {
      const supermarket = item.products.supermarkets.slug;
      if (!acc[supermarket]) {
        acc[supermarket] = {
          name: item.products.supermarkets.name,
          total: 0,
          itemCount: 0
        };
      }
      acc[supermarket].total += item.totalPrice;
      acc[supermarket].itemCount += item.quantity;
      return acc;
    }, {} as Record<string, any>);

    res.json({
      cartItems: cartWithPrices,
      totals,
      itemCount: cartItems.length
    });

  } catch (error) {
    logger.error('Error fetching cart:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// POST /api/cart - Add item to cart
router.post('/', async (req, res) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({ error: 'User not authenticated' });
    }

    const { productId, quantity } = addToCartSchema.parse(req.body);

    // Check if product exists
    const { data: product, error: productError } = await supabase
      .from('products')
      .select('id, name')
      .eq('id', productId)
      .single();

    if (productError || !product) {
      return res.status(404).json({ error: 'Product not found' });
    }

    // Check if item already exists in cart
    const { data: existingItem, error: existingError } = await supabase
      .from('cart_items')
      .select('id, quantity')
      .eq('user_id', userId)
      .eq('product_id', productId)
      .single();

    if (existingItem) {
      // Update existing item quantity
      const { data: updatedItem, error: updateError } = await supabase
        .from('cart_items')
        .update({ quantity: existingItem.quantity + quantity })
        .eq('id', existingItem.id)
        .select()
        .single();

      if (updateError) {
        logger.error('Error updating cart item:', updateError);
        return res.status(500).json({ error: 'Failed to update cart item' });
      }

      res.json({
        message: 'Cart item updated successfully',
        cartItem: updatedItem
      });
    } else {
      // Add new item to cart
      const { data: newItem, error: insertError } = await supabase
        .from('cart_items')
        .insert({
          user_id: userId,
          product_id: productId,
          quantity
        })
        .select()
        .single();

      if (insertError) {
        logger.error('Error adding to cart:', insertError);
        return res.status(500).json({ error: 'Failed to add item to cart' });
      }

      res.status(201).json({
        message: 'Item added to cart successfully',
        cartItem: newItem
      });
    }

  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({ error: 'Invalid parameters', details: error.errors });
    }
    logger.error('Error adding to cart:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// PUT /api/cart/:id - Update cart item quantity
router.put('/:id', async (req, res) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({ error: 'User not authenticated' });
    }

    const { id } = cartItemIdSchema.parse(req.params);
    const { quantity } = updateCartSchema.parse(req.body);

    const { data: updatedItem, error } = await supabase
      .from('cart_items')
      .update({ quantity })
      .eq('id', id)
      .eq('user_id', userId)
      .select()
      .single();

    if (error) {
      logger.error('Error updating cart item:', error);
      return res.status(500).json({ error: 'Failed to update cart item' });
    }

    if (!updatedItem) {
      return res.status(404).json({ error: 'Cart item not found' });
    }

    res.json({
      message: 'Cart item updated successfully',
      cartItem: updatedItem
    });

  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({ error: 'Invalid parameters', details: error.errors });
    }
    logger.error('Error updating cart item:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// DELETE /api/cart/:id - Remove item from cart
router.delete('/:id', async (req, res) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({ error: 'User not authenticated' });
    }

    const { id } = cartItemIdSchema.parse(req.params);

    const { error } = await supabase
      .from('cart_items')
      .delete()
      .eq('id', id)
      .eq('user_id', userId);

    if (error) {
      logger.error('Error removing cart item:', error);
      return res.status(500).json({ error: 'Failed to remove cart item' });
    }

    res.json({ message: 'Item removed from cart successfully' });

  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({ error: 'Invalid parameters', details: error.errors });
    }
    logger.error('Error removing cart item:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// DELETE /api/cart - Clear entire cart
router.delete('/', async (req, res) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({ error: 'User not authenticated' });
    }

    const { error } = await supabase
      .from('cart_items')
      .delete()
      .eq('user_id', userId);

    if (error) {
      logger.error('Error clearing cart:', error);
      return res.status(500).json({ error: 'Failed to clear cart' });
    }

    res.json({ message: 'Cart cleared successfully' });

  } catch (error) {
    logger.error('Error clearing cart:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

export default router;
