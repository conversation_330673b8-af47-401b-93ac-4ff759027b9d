import { Router } from 'express';
import { z } from 'zod';
import { supabase } from '../config/database';
import { logger } from '../utils/logger';

const router = Router();

// Validation schemas
const searchSchema = z.object({
  q: z.string().optional(),
  category: z.string().optional(),
  supermarket: z.string().optional(),
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(100).default(20),
  sort: z.enum(['name', 'price', 'date']).default('name'),
  order: z.enum(['asc', 'desc']).default('asc')
});

const productIdSchema = z.object({
  id: z.string().uuid()
});

// GET /api/products - Search and list products
router.get('/', async (req, res) => {
  try {
    const params = searchSchema.parse(req.query);
    const offset = (params.page - 1) * params.limit;

    let query = supabase
      .from('latest_product_prices')
      .select('*', { count: 'exact' });

    // Apply filters
    if (params.q) {
      query = query.ilike('name', `%${params.q}%`);
    }

    if (params.category) {
      query = query.eq('category', params.category);
    }

    if (params.supermarket) {
      query = query.eq('supermarket_slug', params.supermarket);
    }

    // Apply sorting
    const sortColumn = params.sort === 'price' ? 'price_discount' : 
                      params.sort === 'date' ? 'scraped_at' : 'name';
    
    query = query.order(sortColumn, { ascending: params.order === 'asc' });

    // Apply pagination
    query = query.range(offset, offset + params.limit - 1);

    const { data, error, count } = await query;

    if (error) {
      logger.error('Error fetching products:', error);
      return res.status(500).json({ error: 'Failed to fetch products' });
    }

    res.json({
      products: data,
      pagination: {
        page: params.page,
        limit: params.limit,
        total: count || 0,
        pages: Math.ceil((count || 0) / params.limit)
      }
    });

  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({ error: 'Invalid parameters', details: error.errors });
    }
    logger.error('Error in products search:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// GET /api/products/:id - Get single product with price history
router.get('/:id', async (req, res) => {
  try {
    const { id } = productIdSchema.parse(req.params);

    // Get product details
    const { data: product, error: productError } = await supabase
      .from('products')
      .select(`
        *,
        supermarkets (name, slug, logo_url)
      `)
      .eq('id', id)
      .single();

    if (productError || !product) {
      return res.status(404).json({ error: 'Product not found' });
    }

    // Get price history (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const { data: priceHistory, error: priceError } = await supabase
      .from('product_prices')
      .select('price_list, price_discount, price_without_tax, scraped_at')
      .eq('product_id', id)
      .gte('scraped_at', thirtyDaysAgo.toISOString())
      .order('scraped_at', { ascending: true });

    if (priceError) {
      logger.error('Error fetching price history:', priceError);
      return res.status(500).json({ error: 'Failed to fetch price history' });
    }

    res.json({
      product,
      priceHistory: priceHistory || []
    });

  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({ error: 'Invalid product ID' });
    }
    logger.error('Error fetching product:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// GET /api/products/:id/compare - Compare product across supermarkets
router.get('/:id/compare', async (req, res) => {
  try {
    const { id } = productIdSchema.parse(req.params);

    // Get the product
    const { data: product, error: productError } = await supabase
      .from('products')
      .select('normalized_name, supermarket_id')
      .eq('id', id)
      .single();

    if (productError || !product) {
      return res.status(404).json({ error: 'Product not found' });
    }

    // Find similar products in other supermarkets
    const { data: similarProducts, error: similarError } = await supabase
      .from('latest_product_prices')
      .select('*')
      .ilike('name', `%${product.normalized_name}%`)
      .neq('supermarket_slug', product.supermarket_id);

    if (similarError) {
      logger.error('Error finding similar products:', similarError);
      return res.status(500).json({ error: 'Failed to find similar products' });
    }

    res.json({
      originalProduct: product,
      similarProducts: similarProducts || []
    });

  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({ error: 'Invalid product ID' });
    }
    logger.error('Error comparing product:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// GET /api/products/categories - Get all categories
router.get('/categories', async (req, res) => {
  try {
    const { data, error } = await supabase
      .from('products')
      .select('category')
      .not('category', 'is', null)
      .order('category');

    if (error) {
      logger.error('Error fetching categories:', error);
      return res.status(500).json({ error: 'Failed to fetch categories' });
    }

    // Get unique categories
    const categories = [...new Set(data.map(item => item.category))];

    res.json({ categories });

  } catch (error) {
    logger.error('Error fetching categories:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// GET /api/products/supermarkets - Get all supermarkets
router.get('/supermarkets', async (req, res) => {
  try {
    const { data, error } = await supabase
      .from('supermarkets')
      .select('*')
      .order('name');

    if (error) {
      logger.error('Error fetching supermarkets:', error);
      return res.status(500).json({ error: 'Failed to fetch supermarkets' });
    }

    res.json({ supermarkets: data });

  } catch (error) {
    logger.error('Error fetching supermarkets:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

export default router;
