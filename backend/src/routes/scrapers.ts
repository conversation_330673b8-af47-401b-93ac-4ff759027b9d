import { Router } from 'express';
import { z } from 'zod';
import { spawn } from 'child_process';
import path from 'path';
import fs from 'fs';
import { supabase } from '../config/database';
import { logger } from '../utils/logger';

const router = Router();

// Validation schemas
const scraperSchema = z.object({
  supermarket: z.enum(['carrefour', 'coto'])
});

// Scraper configurations
const SCRAPER_CONFIGS = {
  carrefour: {
    scriptPath: '/Volumes/2tb/github/super-scraper/carrefour/carrefour/spiders/carrefour_spider.py',
    workingDir: '/Volumes/2tb/github/super-scraper/carrefour',
    outputFile: 'carrefour_products.jsonl',
    expectedUrls: 500, // Approximate from carrefour_full_urls.txt
    command: 'scrapy',
    args: ['crawl', 'carrefour_next', '-o', 'carrefour_products.jsonl']
  },
  coto: {
    scriptPath: '/Volumes/2tb/github/super-scraper/coto/coto/spiders/coto_api.py',
    workingDir: '/Volumes/2tb/github/super-scraper/coto',
    outputFile: 'coto_products.jsonl',
    expectedUrls: 1207, // From gen_cotodigital_urls.py
    command: 'scrapy',
    args: ['crawl', 'coto_api', '-o', 'coto_products.jsonl']
  }
};

// Active scraper processes
const activeScrapers = new Map<string, any>();

// GET /api/scrapers/status - Get status of all scrapers
router.get('/status', async (req, res) => {
  try {
    const { data, error } = await supabase
      .from('scraper_runs')
      .select(`
        *,
        supermarkets (name, slug)
      `)
      .order('started_at', { ascending: false })
      .limit(10);

    if (error) {
      logger.error('Error fetching scraper status:', error);
      return res.status(500).json({ error: 'Failed to fetch scraper status' });
    }

    // Add active process info
    const statusWithActive = data.map(run => ({
      ...run,
      isActive: activeScrapers.has(run.supermarkets.slug)
    }));

    res.json({ scraperRuns: statusWithActive });

  } catch (error) {
    logger.error('Error fetching scraper status:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// POST /api/scrapers/start - Start a scraper
router.post('/start', async (req, res) => {
  try {
    const { supermarket } = scraperSchema.parse(req.body);
    const config = SCRAPER_CONFIGS[supermarket];
    const io = req.app.get('io');

    // Check if scraper is already running
    if (activeScrapers.has(supermarket)) {
      return res.status(409).json({ error: 'Scraper already running for this supermarket' });
    }

    // Get supermarket ID
    const { data: supermarketData, error: supermarketError } = await supabase
      .from('supermarkets')
      .select('id')
      .eq('slug', supermarket)
      .single();

    if (supermarketError || !supermarketData) {
      return res.status(404).json({ error: 'Supermarket not found' });
    }

    // Create scraper run record
    const { data: scraperRun, error: runError } = await supabase
      .from('scraper_runs')
      .insert({
        supermarket_id: supermarketData.id,
        status: 'running',
        progress: 0,
        total_urls: config.expectedUrls,
        products_scraped: 0
      })
      .select('id')
      .single();

    if (runError) {
      logger.error('Error creating scraper run:', runError);
      return res.status(500).json({ error: 'Failed to create scraper run' });
    }

    // Start the scraper process
    const scraperProcess = spawn(config.command, config.args, {
      cwd: config.workingDir,
      stdio: ['pipe', 'pipe', 'pipe']
    });

    activeScrapers.set(supermarket, {
      process: scraperProcess,
      runId: scraperRun.id,
      startTime: Date.now(),
      progress: 0
    });

    // Handle process output
    scraperProcess.stdout.on('data', (data) => {
      const output = data.toString();
      logger.info(`[${supermarket}] ${output}`);
      
      // Parse progress from scrapy output
      const progressMatch = output.match(/(\d+) pages/);
      if (progressMatch) {
        const progress = Math.min(100, Math.round((parseInt(progressMatch[1]) / config.expectedUrls) * 100));
        updateScraperProgress(supermarket, progress, scraperRun.id, io);
      }
    });

    scraperProcess.stderr.on('data', (data) => {
      logger.error(`[${supermarket}] ${data.toString()}`);
    });

    scraperProcess.on('close', async (code) => {
      logger.info(`[${supermarket}] Process exited with code ${code}`);
      
      const scraperInfo = activeScrapers.get(supermarket);
      activeScrapers.delete(supermarket);

      // Update scraper run status
      const status = code === 0 ? 'completed' : 'failed';
      const errorMessage = code !== 0 ? `Process exited with code ${code}` : null;

      // Count scraped products
      let productsScraped = 0;
      try {
        const outputPath = path.join(config.workingDir, config.outputFile);
        if (fs.existsSync(outputPath)) {
          const content = fs.readFileSync(outputPath, 'utf-8');
          productsScraped = content.split('\n').filter(line => line.trim()).length;
        }
      } catch (error) {
        logger.error('Error counting scraped products:', error);
      }

      await supabase
        .from('scraper_runs')
        .update({
          status,
          progress: status === 'completed' ? 100 : scraperInfo?.progress || 0,
          products_scraped: productsScraped,
          completed_at: new Date().toISOString(),
          error_message: errorMessage
        })
        .eq('id', scraperRun.id);

      // Emit completion event
      io.to(`scraper-${supermarket}`).emit('scraper-completed', {
        supermarket,
        status,
        productsScraped,
        errorMessage
      });

      // If successful, trigger data import
      if (code === 0) {
        try {
          await importScrapedData(supermarket, config, scraperRun.id);
        } catch (error) {
          logger.error('Error importing scraped data:', error);
        }
      }
    });

    res.json({
      message: 'Scraper started successfully',
      runId: scraperRun.id,
      supermarket
    });

  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({ error: 'Invalid parameters', details: error.errors });
    }
    logger.error('Error starting scraper:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// POST /api/scrapers/stop - Stop a scraper
router.post('/stop', async (req, res) => {
  try {
    const { supermarket } = scraperSchema.parse(req.body);

    const scraperInfo = activeScrapers.get(supermarket);
    if (!scraperInfo) {
      return res.status(404).json({ error: 'No active scraper found for this supermarket' });
    }

    // Kill the process
    scraperInfo.process.kill('SIGTERM');
    activeScrapers.delete(supermarket);

    // Update scraper run status
    await supabase
      .from('scraper_runs')
      .update({
        status: 'failed',
        completed_at: new Date().toISOString(),
        error_message: 'Manually stopped'
      })
      .eq('id', scraperInfo.runId);

    res.json({ message: 'Scraper stopped successfully' });

  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({ error: 'Invalid parameters', details: error.errors });
    }
    logger.error('Error stopping scraper:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Helper function to update scraper progress
async function updateScraperProgress(supermarket: string, progress: number, runId: string, io: any) {
  const scraperInfo = activeScrapers.get(supermarket);
  if (scraperInfo) {
    scraperInfo.progress = progress;
    
    // Update database
    await supabase
      .from('scraper_runs')
      .update({ progress })
      .eq('id', runId);

    // Emit progress update
    io.to(`scraper-${supermarket}`).emit('scraper-progress', {
      supermarket,
      progress,
      runId
    });
  }
}

// Helper function to import scraped data
async function importScrapedData(supermarket: string, config: any, runId: string) {
  // This would use the import script we created earlier
  // For now, just log that import should happen
  logger.info(`Should import data for ${supermarket} from ${config.outputFile}`);
  
  // In production, you would:
  // 1. Run the import-existing-data.js script
  // 2. Or implement the import logic directly here
  // 3. Update the scraper_run record with import results
}

export default router;
