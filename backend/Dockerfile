# Backend Dockerfile - Development Mode
FROM node:18-alpine

WORKDIR /app

# Install dependencies
RUN apk add --no-cache libc6-compat

# Copy package files
COPY package.json package-lock.json* ./

# Install dependencies in the container to avoid platform issues
RUN npm ci

# Copy source code
COPY . .

# Create logs directory
RUN mkdir -p logs

# Set environment variables
ENV NODE_ENV=development
ENV PORT=5000

EXPOSE 5000

# Use mock server for demo
CMD ["npx", "tsx", "src/mock-server.ts"]
