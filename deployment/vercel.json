{"version": 2, "name": "supermarket-frontend", "builds": [{"src": "frontend/package.json", "use": "@vercel/next"}], "routes": [{"src": "/frontend/(.*)", "dest": "/frontend/$1"}], "env": {"NEXT_PUBLIC_SUPABASE_URL": "@supabase_url", "NEXT_PUBLIC_SUPABASE_ANON_KEY": "@supabase_anon_key", "NEXT_PUBLIC_API_URL": "@api_url", "NEXT_PUBLIC_SITE_URL": "@site_url"}, "build": {"env": {"NEXT_PUBLIC_SUPABASE_URL": "@supabase_url", "NEXT_PUBLIC_SUPABASE_ANON_KEY": "@supabase_anon_key", "NEXT_PUBLIC_API_URL": "@api_url", "NEXT_PUBLIC_SITE_URL": "@site_url"}}, "functions": {"frontend/app/**": {"maxDuration": 30}}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=()"}]}], "redirects": [{"source": "/admin", "destination": "/admin/dashboard", "permanent": false}], "rewrites": [{"source": "/api/:path*", "destination": "https://your-backend-url.railway.app/api/:path*"}]}