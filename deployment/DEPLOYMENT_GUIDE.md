# Deployment Guide

This guide covers deploying the supermarket comparison application to production using Vercel (frontend) and Railway (backend).

## Prerequisites

- [Vercel account](https://vercel.com)
- [Railway account](https://railway.app)
- [Supabase account](https://supabase.com)
- GitHub repository with your code

## 1. Database Setup (Supabase)

### Create Supabase Project
1. Go to [Supabase Dashboard](https://app.supabase.com)
2. Create a new project
3. Wait for the project to be ready

### Setup Database Schema
1. Go to SQL Editor in Supabase dashboard
2. Copy and paste the contents of `database-schema.sql`
3. Run the SQL to create tables and setup

### Get Database Credentials
Save these for later configuration:
- Project URL: `https://your-project.supabase.co`
- Anon Key: Found in Settings > API
- Service Role Key: Found in Settings > API (keep secret!)

## 2. Backend Deployment (Railway)

### Setup Railway Project
1. Go to [Railway Dashboard](https://railway.app/dashboard)
2. Create new project from GitHub repo
3. Select your repository
4. Choose the backend service

### Configure Environment Variables
In Railway dashboard, add these environment variables:

```bash
NODE_ENV=production
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
FRONTEND_URL=https://your-app.vercel.app
ADMIN_EMAILS=<EMAIL>,<EMAIL>
```

### Configure Build Settings
1. Root Directory: `backend`
2. Build Command: `npm install && npm run build`
3. Start Command: `npm start`

### Deploy
1. Railway will automatically deploy on push to main branch
2. Note the generated URL (e.g., `https://your-app.railway.app`)

## 3. Frontend Deployment (Vercel)

### Setup Vercel Project
1. Go to [Vercel Dashboard](https://vercel.com/dashboard)
2. Import your GitHub repository
3. Configure project settings

### Configure Environment Variables
In Vercel dashboard, add these environment variables:

```bash
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
NEXT_PUBLIC_API_URL=https://your-app.railway.app
NEXT_PUBLIC_SITE_URL=https://your-app.vercel.app
```

### Configure Build Settings
1. Framework Preset: Next.js
2. Root Directory: `frontend`
3. Build Command: `npm run build`
4. Output Directory: `.next`

### Deploy
1. Vercel will automatically deploy on push to main branch
2. Your app will be available at `https://your-app.vercel.app`

## 4. Data Import

### Import Existing Data
1. SSH into your Railway backend or run locally
2. Set environment variables:
   ```bash
   export SUPABASE_URL=https://your-project.supabase.co
   export SUPABASE_ANON_KEY=your_anon_key
   ```
3. Run the import script:
   ```bash
   node import-existing-data.js
   ```

## 5. CI/CD Setup (GitHub Actions)

### Setup Repository Secrets
In your GitHub repository settings, add these secrets:

**Vercel Secrets:**
- `VERCEL_TOKEN` - Vercel API token
- `VERCEL_ORG_ID` - Your Vercel organization ID
- `VERCEL_PROJECT_ID` - Your Vercel project ID

**Railway Secrets:**
- `RAILWAY_TOKEN` - Railway API token
- `RAILWAY_PROJECT_ID` - Your Railway project ID

**Supabase Secrets:**
- `SUPABASE_DB_URL` - Database connection string
- `SUPABASE_ACCESS_TOKEN` - Supabase access token

**Environment Variables:**
- `NEXT_PUBLIC_SUPABASE_URL`
- `NEXT_PUBLIC_SUPABASE_ANON_KEY`
- `NEXT_PUBLIC_API_URL`
- `NEXT_PUBLIC_SITE_URL`

### Setup GitHub Actions
1. Copy `github-actions.yml` to `.github/workflows/deploy.yml`
2. Commit and push to trigger the workflow

## 6. Domain Configuration (Optional)

### Custom Domain for Frontend
1. In Vercel dashboard, go to your project settings
2. Add your custom domain
3. Configure DNS records as instructed

### Custom Domain for Backend
1. In Railway dashboard, go to your service settings
2. Add custom domain
3. Configure DNS records as instructed

## 7. Monitoring and Logging

### Vercel Analytics
1. Enable Vercel Analytics in project settings
2. Monitor performance and usage

### Railway Logs
1. View logs in Railway dashboard
2. Set up log retention as needed

### Supabase Monitoring
1. Monitor database performance in Supabase dashboard
2. Set up alerts for high usage

## 8. Security Considerations

### Environment Variables
- Never commit secrets to version control
- Use different keys for development and production
- Rotate keys regularly

### Database Security
- Enable Row Level Security (RLS) in Supabase
- Review and test access policies
- Monitor database access logs

### API Security
- Implement rate limiting
- Use HTTPS only
- Validate all inputs

## 9. Backup and Recovery

### Database Backups
- Supabase automatically backs up your database
- Set up additional backup strategies if needed

### Code Backups
- Use GitHub for version control
- Tag releases for easy rollback

## 10. Scaling Considerations

### Frontend Scaling
- Vercel automatically scales based on traffic
- Monitor usage and upgrade plan if needed

### Backend Scaling
- Railway provides automatic scaling
- Monitor resource usage
- Consider upgrading plan for high traffic

### Database Scaling
- Supabase provides automatic scaling
- Monitor database performance
- Consider read replicas for high read loads

## Troubleshooting

### Common Issues

**Build Failures:**
- Check environment variables are set correctly
- Verify all dependencies are listed in package.json
- Check build logs for specific errors

**Database Connection Issues:**
- Verify Supabase URL and keys
- Check network connectivity
- Review Supabase dashboard for issues

**CORS Issues:**
- Ensure FRONTEND_URL is set correctly in backend
- Check Supabase CORS settings
- Verify API URLs in frontend

### Getting Help
- Check service status pages (Vercel, Railway, Supabase)
- Review documentation for each service
- Check GitHub Issues for known problems
