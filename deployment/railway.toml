[build]
builder = "NIXPACKS"
buildCommand = "cd backend && npm install && npm run build"

[deploy]
startCommand = "cd backend && npm start"
healthcheckPath = "/health"
healthcheckTimeout = 300
restartPolicyType = "ON_FAILURE"
restartPolicyMaxRetries = 10

[environments.production]
variables = { NODE_ENV = "production" }

[environments.staging]
variables = { NODE_ENV = "staging" }

# Railway will automatically detect and expose the PORT
# No need to specify PORT in variables as Railway sets it automatically
